import * as functions from 'firebase-functions';
import * as cors from 'cors';
import * as dotenv from 'dotenv';
import { ESimPlansResponse, DataPlansResponse } from './models';
import { PricingUtils } from './utils';

const corsHandler = cors({ origin: true });

interface ESimPricingParams {
  start?: string;
  length?: string;
  search?: string;
}

export class ESimService {
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly defaultLength: string;

  constructor() {
    // Load environment variables when service is actually instantiated
    dotenv.config();
    
    this.apiKey = process.env.SMSPOOL_API_KEY || '';
    this.baseUrl = process.env.SMSPOOL_BASE_URL || 'https://api.smspool.net';
    this.defaultLength = process.env.SMSPOOL_DEFAULT_LENGTH || '200';
    
    if (!this.apiKey) {
      throw new Error('SMSPOOL_API_KEY is required');
    }
  }

  async getESimPricing(params: ESimPricingParams = {}): Promise<ESimPlansResponse> {
    const formData = new URLSearchParams();
    formData.append('key', this.apiKey);
    formData.append('start', params.start || '');
    formData.append('length', params.length || this.defaultLength);
    formData.append('Search', params.search || '');

    try {
      const response = await fetch(`${this.baseUrl}/esim/pricing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData.toString(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ESimPlansResponse = await response.json();
      
      console.log('Original API response sample:', data.data?.[0]);
      
      // Apply profit margins
      data.data = data.data.map(plan => {
        console.log('Original price:', plan.price);
        const calculatedPrice = PricingUtils.calculateESimPrice(plan.price);
        console.log('Calculated price:', calculatedPrice);
        
        return {
          ...plan,
          originalPrice: plan.price,
          priceUsd: calculatedPrice.toString()
        };
      });
      
      return data;
    } catch (error) {
      console.error('Error fetching eSIM pricing:', error);
      throw new Error(`Failed to fetch eSIM pricing: ${error}`);
    }
  }

  async getDataPlans(planId: string): Promise<DataPlansResponse> {
    const formData = new URLSearchParams();
    formData.append('plan', planId);

    try {
      const response = await fetch(`${this.baseUrl}/esim/topup_plans`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData.toString(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: DataPlansResponse = await response.json();
      
      // Apply profit margins
      return data.map(plan => ({
        ...plan,
        originalPrice: plan.price,
        priceUsd: PricingUtils.calculateDataPlanPrice(plan.price).toString()
      }));
    } catch (error) {
      console.error('Error fetching data plans:', error);
      throw new Error(`Failed to fetch data plans: ${error}`);
    }
  }
}

// Firebase Functions exports
let eSimService: ESimService;

function getESimService(): ESimService {
  if (!eSimService) {
    eSimService = new ESimService();
  }
  return eSimService;
}

export const getESimPricing = functions.https.onRequest((req, res) => {
  corsHandler(req, res, async () => {
    try {
      const data = req.method === 'POST' ? req.body : req.query;
      const result = await getESimService().getESimPricing(data as ESimPricingParams);
      res.status(200).json(result);
    } catch (error: any) {
      console.error('Error in getESimPricing:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });
});

export const getDataPlans = functions.https.onRequest((req, res) => {
  corsHandler(req, res, async () => {
    try {
      const planId = req.query.plan || req.body?.plan || req.body?.planId;
      if (!planId) {
        throw new Error('Plan ID is required');
      }
      const result = await getESimService().getDataPlans(planId as string);
      res.status(200).json(result);
    } catch (error: any) {
      console.error('Error in getDataPlans:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });
});
