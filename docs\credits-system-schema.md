# Credits-Based System Database Schema

## Overview
Migration from country-specific eSIM plans to a flexible credits-based system with animal-themed packages.

## New Firestore Collections

### 1. `credit_packages` (Global Collection)
```typescript
interface CreditPackage {
  id: string;                    // 'minnow', 'tuna', 'dolphin', 'octopus', 'shark', 'whale'
  name: string;                  // 'Minnow', 'Tuna', etc.
  animal: string;                // '🐟 Minnow', '🐟 Tuna', etc.
  price: number;                 // USD price: 5, 10, 20, 35, 50, 75
  credits: number;               // Credits awarded: 5, 10, 20, 35, 50, 75
  vibe: string;                  // 'Tiny but mighty', 'Reliable and steady', etc.
  useCase: string;               // 'Light travelers, test users', etc.
  lemonSqueezyVariantId: string; // LemonSqueezy product variant ID
  lemonSqueezyProductId: string; // LemonSqueezy product ID
  active: boolean;               // Whether package is available for purchase
  createdAt: Date;
  updatedAt: Date;
}
```

### 2. `users/{userId}/credits` (User Subcollection)
```typescript
interface UserCredits {
  userId: string;
  totalCredits: number;          // Current available credits
  lifetimeCredits: number;       // Total credits ever purchased
  lifetimeSpent: number;         // Total credits ever spent
  lastUpdated: Date;
  createdAt: Date;
}
```

### 3. `users/{userId}/credit_transactions` (User Subcollection)
```typescript
interface CreditTransaction {
  id: string;                    // Auto-generated document ID
  userId: string;
  type: 'purchase' | 'redemption' | 'refund' | 'bonus';
  amount: number;                // Credits added (positive) or spent (negative)
  packageId?: string;            // For purchases: 'minnow', 'tuna', etc.
  esimPlanId?: string;           // For redemptions: which eSIM plan was purchased
  esimCountryCode?: string;      // For redemptions: country code
  orderId?: string;              // Related order ID (LemonSqueezy or in-app)
  lemonSqueezyOrderId?: string;  // LemonSqueezy order ID for purchases
  transactionId?: string;        // Platform transaction ID (iOS/Android)
  platform: 'web' | 'ios' | 'android';
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  metadata?: {
    packageName?: string;        // Human-readable package name
    esimPlanName?: string;       // Human-readable eSIM plan name
    priceUsd?: number;           // USD price for purchases
    [key: string]: any;
  };
  createdAt: Date;
  updatedAt: Date;
}
```

### 4. `esim_credit_costs` (Global Collection)
```typescript
interface ESimCreditCost {
  id: string;                    // Auto-generated or planId_countryCode
  planId: number;                // Original eSIM plan ID
  countryCode: string;           // Country code
  countryName: string;           // Human-readable country name
  dataInGb: number;              // Data amount in GB
  creditCost: number;            // How many credits this eSIM costs
  originalPriceUsd: number;      // Original USD price for reference
  active: boolean;               // Whether this eSIM is available for redemption
  createdAt: Date;
  updatedAt: Date;
}
```

### 5. `credit_orders` (Global Collection) - Replaces current orders
```typescript
interface CreditOrder {
  orderId: string;               // Unique order ID
  userId: string;
  type: 'credit_purchase' | 'esim_redemption';
  
  // For credit purchases
  packageId?: string;            // 'minnow', 'tuna', etc.
  creditsAwarded?: number;       // Credits given to user
  
  // For eSIM redemptions
  esimPlanId?: string;
  esimCountryCode?: string;
  creditsSpent?: number;         // Credits deducted from user
  
  // Payment/Platform info
  platform: 'web' | 'ios' | 'android';
  lemonSqueezyOrderId?: string;  // For web purchases
  transactionId?: string;        // For in-app purchases
  
  // Order details
  amountUsd?: number;            // USD amount for purchases
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  
  // Additional data
  metadata?: {
    customerEmail?: string;
    customerName?: string;
    webhookData?: any;
    [key: string]: any;
  };
}
```

## Migration Strategy

### Phase 1: Database Setup
1. Create new collections with initial data
2. Populate `credit_packages` with 6 animal packages
3. Populate `esim_credit_costs` based on current eSIM pricing
4. Create migration script for existing users

### Phase 2: Credit Cost Calculation
```typescript
// Example credit cost calculation
const calculateCreditCost = (originalPriceUsd: number): number => {
  // 1 credit = ~$1 USD, but can be adjusted
  return Math.ceil(originalPriceUsd);
};
```

### Phase 3: User Migration
```typescript
// Migrate existing users to credits system
const migrateUser = async (userId: string) => {
  // Initialize user credits
  await db.collection('users').doc(userId).collection('credits').doc('balance').set({
    userId,
    totalCredits: 0,        // Start with 0 credits
    lifetimeCredits: 0,
    lifetimeSpent: 0,
    lastUpdated: new Date(),
    createdAt: new Date()
  });
};
```

## Benefits of New System

1. **Flexibility**: Users can buy credits once, use for any country
2. **Simplified Pricing**: Clear credit packages instead of complex country pricing
3. **Better UX**: Single purchase flow, multiple redemptions
4. **Scalability**: Easy to add new eSIM providers without changing pricing
5. **Marketing**: Animal themes create memorable, fun experience
6. **Analytics**: Better tracking of user spending patterns

## Implementation Notes

- Credits never expire (better than current system)
- 1 credit ≈ $1 USD for easy mental math
- LemonSqueezy overlay for seamless web checkout
- Maintain backward compatibility during transition
- Real-time credit balance updates
- Transaction history for transparency
