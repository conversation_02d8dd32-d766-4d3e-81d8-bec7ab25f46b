import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions';
import * as dotenv from 'dotenv';
import { CreditPackage } from './types/credits';
import * as cors from 'cors';

const corsHandler = cors({ origin: true });

// Initialize Firebase Admin if not already done
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

class StripeProductSyncService {
  private stripe: any = null;

  private getStripe() {
    if (!this.stripe) {
      dotenv.config();
      
      const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
      if (!stripeSecretKey) {
        throw new Error('STRIPE_SECRET_KEY environment variable is required');
      }

      const Stripe = require('stripe');
      this.stripe = new Stripe(stripeSecretKey, {
        apiVersion: '2025-08-27.basil',
      });
    }
    return this.stripe;
  }

  async syncProductsFromStripe(): Promise<{ success: boolean; message: string; count?: number; products?: CreditPackage[] }> {
    try {
      const stripe = this.getStripe();
      // Fetch products from Stripe
      const products = await stripe.products.list({ 
        active: true,
        limit: 20 
      });

      const batch = db.batch();
      let syncedCount = 0;
      const packageIds: string[] = [];

      for (const product of products.data) {
        // Get the price for this product
        const prices = await stripe.prices.list({ 
          product: product.id,
          active: true,
          limit: 1 
        });

        if (prices.data.length === 0) continue;

        const price = prices.data[0];
        const priceUsd = price.unit_amount / 100;

        // Map Stripe product to our credit package format
        const packageData: CreditPackage = {
          id: product.name.toLowerCase().replace(/\s+/g, '_'),
          name: product.name,
          animal: this.getAnimalEmoji(product.name),
          price: priceUsd,
          credits: priceUsd, // 1:1 ratio for now
          vibe: product.description || 'Credit package',
          useCase: this.getUseCase(product.name),
          stripePriceId: price.id,
          stripeProductId: product.id,
          active: product.active,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const docRef = db.collection('credit_packages').doc(packageData.id);
        batch.set(docRef, packageData);
        packageIds.push(packageData.id);
        syncedCount++;
      }

      await batch.commit();

      // Fetch the saved products from Firestore
      const snapshot = await db.collection('credit_packages').where('id', 'in', packageIds).get();
      const savedProducts: CreditPackage[] = [];
      snapshot.forEach(doc => {
        savedProducts.push(doc.data() as CreditPackage);
      });

      return {
        success: true,
        message: `Successfully synced ${syncedCount} products from Stripe`,
        count: syncedCount,
        products: savedProducts
      };

    } catch (error) {
      console.error('Error syncing Stripe products:', error);
      return {
        success: false,
        message: `Failed to sync products: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private getAnimalEmoji(productName: string): string {
    const name = productName.toLowerCase();
    if (name.includes('minnow')) return '🐟 Minnow';
    if (name.includes('tuna')) return '🐟 Tuna';
    if (name.includes('dolphin')) return '🐬 Dolphin';
    if (name.includes('octopus')) return '🐙 Octopus';
    if (name.includes('shark')) return '🦈 Shark';
    if (name.includes('whale')) return '🐋 Whale';
    return `🐟 ${productName}`;
  }

  private getUseCase(productName: string): string {
    const name = productName.toLowerCase();
    if (name.includes('minnow')) return 'Light travelers, test users';
    if (name.includes('tuna')) return 'Regional users';
    if (name.includes('dolphin')) return 'Business travelers';
    if (name.includes('octopus')) return 'Remote workers';
    if (name.includes('shark')) return 'Heavy data users';
    if (name.includes('whale')) return 'Global nomads, teams';
    return 'Perfect for your needs';
  }
}

let syncService: StripeProductSyncService;

function getSyncService(): StripeProductSyncService {
  if (!syncService) {
    syncService = new StripeProductSyncService();
  }
  return syncService;
}

export const syncStripeProducts = functions.https.onRequest((req, res) => {
  corsHandler(req, res, async () => {
    try {
      const result = await getSyncService().syncProductsFromStripe();
    
      if (result.success) {
        res.status(200).json(result);
      } else {
        res.status(500).json(result);
      }
    } catch (error) {
      console.error('Error in syncStripeProducts:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  });
});