export class PricingUtils {
  private static getESimProfitMargin(): number {
    return parseFloat(process.env.ESIM_PROFIT_MARGIN || '1.5');
  }

  private static getDataPlanProfitMargin(): number {
    return parseFloat(process.env.DATA_PLAN_PROFIT_MARGIN || '1.5');
  }

  static calculateESimPrice(costUsd: string): number {
    const cost = parseFloat(costUsd);
    if (isNaN(cost)) {
      console.error('Invalid cost value:', costUsd);
      return 0;
    }
    return Math.round((cost * this.getESimProfitMargin()) * 100) / 100;
  }

  static calculateDataPlanPrice(costUsd: string): number {
    const cost = parseFloat(costUsd);
    if (isNaN(cost)) {
      console.error('Invalid cost value:', costUsd);
      return 0;
    }
    return Math.round((cost * this.getDataPlanProfitMargin()) * 100) / 100;
  }

  static formatPrice(price: number): string {
    return `$${price.toFixed(2)}`;
  }
}
