<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">esimnumero.com</domain>
        <domain includeSubdomains="true">us-central1-esim-numero.cloudfunctions.net</domain>
        <domain includeSubdomains="true">firebase.googleapis.com</domain>
        <domain includeSubdomains="true">firebaseapp.com</domain>
        <domain includeSubdomains="true">api.smspool.net</domain>
        <domain includeSubdomains="true">api.stripe.com</domain>
        <domain includeSubdomains="true">js.stripe.com</domain>
    </domain-config>
    
    <!-- Allow localhost for development -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
    </domain-config>
    
    <!-- Default configuration for other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
