"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeUserCredits = exports.initializeUserCreditsHttp = exports.getUserCreditTransactionsCallable = exports.getUserCreditsCallable = exports.getUserCreditTransactions = exports.getUserCreditTransactionsHttp = exports.redeemCreditsForESimHttp = exports.processCreditPurchase = exports.processCreditPurchaseHttp = exports.getUserCreditsHttp = exports.getCreditPackagesHttp = exports.CreditsService = void 0;
const admin = require("firebase-admin");
const functions = require("firebase-functions");
// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}
// Manual CORS handler for mobile compatibility
const handleCors = (req, res) => {
    // Set CORS headers for all requests
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept, Authorization, X-Requested-With');
    res.set('Access-Control-Max-Age', '3600');
    // Handle preflight OPTIONS request
    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return true; // Indicates preflight was handled
    }
    return false; // Continue with normal processing
};
class CreditsService {
    constructor() {
        this.db = admin.firestore();
    }
    // Get all available credit packages
    async getCreditPackages() {
        const snapshot = await this.db
            .collection('credit_packages')
            .where('active', '==', true)
            .orderBy('price', 'asc')
            .get();
        return snapshot.docs.map(doc => (Object.assign(Object.assign({}, doc.data()), { createdAt: doc.data().createdAt.toDate(), updatedAt: doc.data().updatedAt.toDate() })));
    }
    // Get user's current credit balance
    async getUserCredits(userId) {
        const doc = await this.db
            .collection('users')
            .doc(userId)
            .collection('credits')
            .doc('balance')
            .get();
        if (!doc.exists) {
            return null;
        }
        const data = doc.data();
        return Object.assign(Object.assign({}, data), { createdAt: data.createdAt.toDate(), lastUpdated: data.lastUpdated.toDate() });
    }
    // Initialize user credits (for new users)
    async initializeUserCredits(userId) {
        const userCredits = {
            userId,
            totalCredits: 0,
            lifetimeCredits: 0,
            lifetimeSpent: 0,
            pendingCredits: 0,
            lastUpdated: new Date(),
            createdAt: new Date()
        };
        await this.db
            .collection('users')
            .doc(userId)
            .collection('credits')
            .doc('balance')
            .set(userCredits);
        return userCredits;
    }
    // Process credit purchase
    async processCreditPurchase(request) {
        try {
            // Get the credit package
            const packageDoc = await this.db.collection('credit_packages').doc(request.packageId).get();
            if (!packageDoc.exists) {
                return { success: false, error: 'Credit package not found' };
            }
            const creditPackage = packageDoc.data();
            if (!creditPackage.active) {
                return { success: false, error: 'Credit package is not available' };
            }
            // Create order ID
            const orderId = `credit_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
            // Use Firestore transaction to ensure consistency
            const result = await this.db.runTransaction(async (transaction) => {
                // Get current user credits
                const userCreditsRef = this.db
                    .collection('users')
                    .doc(request.userId)
                    .collection('credits')
                    .doc('balance');
                const userCreditsDoc = await transaction.get(userCreditsRef);
                let currentCredits;
                if (!userCreditsDoc.exists) {
                    // Initialize if doesn't exist
                    currentCredits = {
                        userId: request.userId,
                        totalCredits: 0,
                        lifetimeCredits: 0,
                        lifetimeSpent: 0,
                        pendingCredits: 0,
                        lastUpdated: new Date(),
                        createdAt: new Date()
                    };
                }
                else {
                    currentCredits = Object.assign(Object.assign({}, userCreditsDoc.data()), { createdAt: userCreditsDoc.data().createdAt.toDate(), lastUpdated: userCreditsDoc.data().lastUpdated.toDate() });
                }
                // Update user credits
                const newCredits = Object.assign(Object.assign({}, currentCredits), { totalCredits: currentCredits.totalCredits + creditPackage.credits, lifetimeCredits: currentCredits.lifetimeCredits + creditPackage.credits, lastUpdated: new Date() });
                // Create credit transaction
                const creditTransaction = {
                    id: `${orderId}_transaction`,
                    userId: request.userId,
                    type: 'purchase',
                    amount: creditPackage.credits,
                    packageId: request.packageId,
                    orderId,
                    stripePaymentIntentId: request.stripePaymentIntentId,
                    transactionId: request.transactionId,
                    platform: request.platform,
                    status: 'completed',
                    metadata: {
                        packageName: creditPackage.animal,
                        priceUsd: creditPackage.price,
                        stripeSessionId: request.stripeSessionId // Keep in metadata for backward compatibility
                    },
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                // Create credit order
                const creditOrder = {
                    orderId,
                    userId: request.userId,
                    type: 'credit_purchase',
                    packageId: request.packageId,
                    creditsAwarded: creditPackage.credits,
                    platform: request.platform,
                    stripePaymentIntentId: request.stripePaymentIntentId,
                    stripeSessionId: request.stripeSessionId,
                    transactionId: request.transactionId,
                    amountUsd: creditPackage.price,
                    status: 'completed',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    completedAt: new Date(),
                    metadata: {
                        customerEmail: request.customerEmail,
                        customerName: request.customerName
                    }
                };
                // Write all updates
                transaction.set(userCreditsRef, newCredits);
                transaction.set(this.db.collection('users').doc(request.userId).collection('credit_transactions').doc(creditTransaction.id), creditTransaction);
                transaction.set(this.db.collection('credit_orders').doc(orderId), creditOrder);
                return {
                    success: true,
                    orderId,
                    creditsAwarded: creditPackage.credits,
                    newBalance: newCredits.totalCredits
                };
            });
            return result;
        }
        catch (error) {
            console.error('Error processing credit purchase:', error);
            return { success: false, error: 'Failed to process credit purchase' };
        }
    }
    // Get eSIM credit cost
    async getESimCreditCost(planId, countryCode) {
        const snapshot = await this.db
            .collection('esim_credit_costs')
            .where('planId', '==', planId)
            .where('countryCode', '==', countryCode)
            .where('active', '==', true)
            .limit(1)
            .get();
        if (snapshot.empty) {
            return null;
        }
        const doc = snapshot.docs[0];
        return Object.assign(Object.assign({}, doc.data()), { createdAt: doc.data().createdAt.toDate(), updatedAt: doc.data().updatedAt.toDate() });
    }
    // Redeem credits for eSIM
    async redeemCreditsForESim(request) {
        try {
            // Get eSIM credit cost
            const esimCost = await this.getESimCreditCost(request.planId, request.countryCode);
            if (!esimCost) {
                return { success: false, error: 'eSIM plan not available for redemption' };
            }
            // Use Firestore transaction to ensure consistency
            const result = await this.db.runTransaction(async (transaction) => {
                // Get current user credits
                const userCreditsRef = this.db
                    .collection('users')
                    .doc(request.userId)
                    .collection('credits')
                    .doc('balance');
                const userCreditsDoc = await transaction.get(userCreditsRef);
                if (!userCreditsDoc.exists) {
                    throw new Error('User credits not found');
                }
                const currentCredits = Object.assign(Object.assign({}, userCreditsDoc.data()), { createdAt: userCreditsDoc.data().createdAt.toDate(), lastUpdated: userCreditsDoc.data().lastUpdated.toDate() });
                // Check if user has enough credits
                if (currentCredits.totalCredits < esimCost.creditCost) {
                    throw new Error('Insufficient credits');
                }
                // Create order ID
                const orderId = `esim_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
                // Update user credits
                const newCredits = Object.assign(Object.assign({}, currentCredits), { totalCredits: currentCredits.totalCredits - esimCost.creditCost, lifetimeSpent: currentCredits.lifetimeSpent + esimCost.creditCost, lastUpdated: new Date() });
                // Create credit transaction
                const creditTransaction = {
                    id: `${orderId}_transaction`,
                    userId: request.userId,
                    type: 'redemption',
                    amount: -esimCost.creditCost,
                    esimPlanId: request.planId.toString(),
                    esimCountryCode: request.countryCode,
                    orderId,
                    platform: 'web',
                    status: 'completed',
                    metadata: {
                        esimPlanName: `${esimCost.countryName} ${esimCost.dataInGb}GB`
                    },
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                // Create credit order
                const creditOrder = {
                    orderId,
                    userId: request.userId,
                    type: 'esim_redemption',
                    esimPlanId: request.planId.toString(),
                    esimCountryCode: request.countryCode,
                    creditsSpent: esimCost.creditCost,
                    platform: 'web',
                    status: 'completed',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    completedAt: new Date()
                };
                // Write all updates
                transaction.set(userCreditsRef, newCredits);
                transaction.set(this.db.collection('users').doc(request.userId).collection('credit_transactions').doc(creditTransaction.id), creditTransaction);
                transaction.set(this.db.collection('credit_orders').doc(orderId), creditOrder);
                return {
                    success: true,
                    orderId,
                    creditsSpent: esimCost.creditCost,
                    remainingCredits: newCredits.totalCredits
                };
            });
            return result;
        }
        catch (error) {
            console.error('Error redeeming credits for eSIM:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to redeem credits for eSIM'
            };
        }
    }
    // Get user's credit transaction history
    async getUserCreditTransactions(userId, limit = 50) {
        try {
            console.log(`Getting credit transactions for user: ${userId}, limit: ${limit}`);
            const snapshot = await this.db
                .collection('users')
                .doc(userId)
                .collection('credit_transactions')
                .orderBy('createdAt', 'desc')
                .limit(limit)
                .get();
            console.log(`Found ${snapshot.docs.length} credit transactions for user: ${userId}`);
            return snapshot.docs.map(doc => {
                const data = doc.data();
                const transaction = Object.assign(Object.assign({ id: doc.id }, data), { 
                    // Safely handle date conversion
                    createdAt: data.createdAt ? (data.createdAt.toDate ? data.createdAt.toDate() : data.createdAt) : new Date(), updatedAt: data.updatedAt ? (data.updatedAt.toDate ? data.updatedAt.toDate() : data.updatedAt) : new Date() });
                return transaction;
            });
        }
        catch (error) {
            console.error(`Error getting credit transactions for user ${userId}:`, error);
            throw error;
        }
    }
}
exports.CreditsService = CreditsService;
// Firebase Functions exports
let creditsService;
function getCreditsService() {
    if (!creditsService) {
        creditsService = new CreditsService();
    }
    return creditsService;
}
// Convert to HTTP endpoints with CORS
exports.getCreditPackagesHttp = functions.https.onRequest(async (req, res) => {
    try {
        // Handle CORS
        if (handleCors(req, res)) {
            return; // Preflight request handled
        }
        const result = await getCreditsService().getCreditPackages();
        res.status(200).json(result);
    }
    catch (error) {
        console.error('Error in getCreditPackagesHttp:', error);
        res.status(500).json({ error: error.message || 'Internal server error' });
    }
});
exports.getUserCreditsHttp = functions.https.onRequest(async (req, res) => {
    try {
        // Handle CORS
        if (handleCors(req, res)) {
            return; // Preflight request handled
        }
        console.log('getUserCreditsHttp - Request received');
        // Verify authentication
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.error('getUserCreditsHttp - Missing or invalid authorization header');
            res.status(401).json({ error: 'Authentication required' });
            return;
        }
        const idToken = authHeader.split('Bearer ')[1];
        console.log('getUserCreditsHttp - Verifying ID token...');
        let decodedToken;
        try {
            decodedToken = await admin.auth().verifyIdToken(idToken);
            console.log('getUserCreditsHttp - Token verified for user:', decodedToken.uid);
        }
        catch (authError) {
            console.error('getUserCreditsHttp - Token verification failed:', authError);
            res.status(401).json({ error: 'Invalid authentication token' });
            return;
        }
        const userId = decodedToken.uid;
        console.log(`getUserCreditsHttp - Getting credits for user: ${userId}`);
        const result = await getCreditsService().getUserCredits(userId);
        console.log(`getUserCreditsHttp - Successfully retrieved credits:`, result);
        res.status(200).json(result);
    }
    catch (error) {
        console.error('Error in getUserCreditsHttp:', error);
        console.error('Error stack:', error.stack);
        res.status(500).json({ error: error.message || 'Internal server error' });
    }
});
exports.processCreditPurchaseHttp = functions.https.onRequest(async (req, res) => {
    try {
        // Handle CORS
        if (handleCors(req, res)) {
            return; // Preflight request handled
        }
        const result = await getCreditsService().processCreditPurchase(req.body);
        res.status(200).json(result);
    }
    catch (error) {
        console.error('Error in processCreditPurchaseHttp:', error);
        res.status(500).json({ error: error.message || 'Internal server error' });
    }
});
// Alias for frontend compatibility
exports.processCreditPurchase = exports.processCreditPurchaseHttp;
exports.redeemCreditsForESimHttp = functions.https.onRequest(async (req, res) => {
    try {
        // Handle CORS
        if (handleCors(req, res)) {
            return; // Preflight request handled
        }
        const result = await getCreditsService().redeemCreditsForESim(req.body);
        res.status(200).json(result);
    }
    catch (error) {
        console.error('Error in redeemCreditsForESimHttp:', error);
        res.status(500).json({ error: error.message || 'Internal server error' });
    }
});
exports.getUserCreditTransactionsHttp = functions.https.onRequest(async (req, res) => {
    try {
        // Handle CORS
        if (handleCors(req, res)) {
            return; // Preflight request handled
        }
        console.log('getUserCreditTransactionsHttp - Request received');
        console.log('getUserCreditTransactionsHttp - Headers:', req.headers);
        console.log('getUserCreditTransactionsHttp - Body:', req.body);
        // Verify authentication
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.error('getUserCreditTransactionsHttp - Missing or invalid authorization header');
            res.status(401).json({ error: 'Authentication required' });
            return;
        }
        const idToken = authHeader.split('Bearer ')[1];
        console.log('getUserCreditTransactionsHttp - Verifying ID token...');
        let decodedToken;
        try {
            decodedToken = await admin.auth().verifyIdToken(idToken);
            console.log('getUserCreditTransactionsHttp - Token verified for user:', decodedToken.uid);
        }
        catch (authError) {
            console.error('getUserCreditTransactionsHttp - Token verification failed:', authError);
            res.status(401).json({ error: 'Invalid authentication token' });
            return;
        }
        const userId = decodedToken.uid;
        const { limitCount } = req.body;
        console.log(`getUserCreditTransactionsHttp - Getting transactions for user: ${userId}, limit: ${limitCount || 50}`);
        const result = await getCreditsService().getUserCreditTransactions(userId, limitCount || 50);
        console.log(`getUserCreditTransactionsHttp - Successfully retrieved ${result.length} transactions`);
        res.status(200).json({ transactions: result });
    }
    catch (error) {
        console.error('Error in getUserCreditTransactionsHttp:', error);
        console.error('Error stack:', error.stack);
        res.status(500).json({ error: error.message || 'Internal server error' });
    }
});
// Additional aliases for frontend compatibility
exports.getUserCreditTransactions = exports.getUserCreditTransactionsHttp;
// Callable functions (no CORS issues) - Enhanced for mobile auth
exports.getUserCreditsCallable = functions.https.onCall(async (data, context) => {
    try {
        console.log('getUserCreditsCallable - Context auth:', context === null || context === void 0 ? void 0 : context.auth);
        console.log('getUserCreditsCallable - Data:', data);
        let userId = (context && context.auth && context.auth.uid);
        // If no auth context, try to verify ID token from data
        if (!userId && (data === null || data === void 0 ? void 0 : data.idToken)) {
            try {
                console.log('getUserCreditsCallable - Verifying ID token from data...');
                const decodedToken = await admin.auth().verifyIdToken(data.idToken);
                userId = decodedToken.uid;
                console.log('getUserCreditsCallable - Token verified for user:', userId);
            }
            catch (tokenError) {
                console.error('getUserCreditsCallable - Token verification failed:', tokenError);
                throw new functions.https.HttpsError('unauthenticated', 'Invalid authentication token');
            }
        }
        if (!userId) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const result = await getCreditsService().getUserCredits(userId);
        return result;
    }
    catch (error) {
        console.error('Error in getUserCreditsCallable:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', (error === null || error === void 0 ? void 0 : error.message) || 'Internal server error');
    }
});
exports.getUserCreditTransactionsCallable = functions.https.onCall(async (data, context) => {
    try {
        console.log('getUserCreditTransactionsCallable - Context auth:', context === null || context === void 0 ? void 0 : context.auth);
        console.log('getUserCreditTransactionsCallable - Data:', data);
        let userId = (context && context.auth && context.auth.uid);
        // If no auth context, try to verify ID token from data
        if (!userId && (data === null || data === void 0 ? void 0 : data.idToken)) {
            try {
                console.log('getUserCreditTransactionsCallable - Verifying ID token from data...');
                const decodedToken = await admin.auth().verifyIdToken(data.idToken);
                userId = decodedToken.uid;
                console.log('getUserCreditTransactionsCallable - Token verified for user:', userId);
            }
            catch (tokenError) {
                console.error('getUserCreditTransactionsCallable - Token verification failed:', tokenError);
                throw new functions.https.HttpsError('unauthenticated', 'Invalid authentication token');
            }
        }
        if (!userId) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const { limitCount } = data;
        const result = await getCreditsService().getUserCreditTransactions(userId, limitCount || 50);
        return { transactions: result };
    }
    catch (error) {
        console.error('Error in getUserCreditTransactionsCallable:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', (error === null || error === void 0 ? void 0 : error.message) || 'Internal server error');
    }
});
// Initialize user credits endpoint
exports.initializeUserCreditsHttp = functions.https.onRequest(async (req, res) => {
    try {
        // Handle CORS
        if (handleCors(req, res)) {
            return; // Preflight request handled
        }
        // Verify authentication
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({ success: false, error: 'Unauthorized: Missing or invalid authorization header' });
            return;
        }
        const idToken = authHeader.split('Bearer ')[1];
        let decodedToken;
        try {
            decodedToken = await admin.auth().verifyIdToken(idToken);
        }
        catch (authError) {
            console.error('Authentication error:', authError);
            res.status(401).json({ success: false, error: 'Unauthorized: Invalid token' });
            return;
        }
        const userId = decodedToken.uid;
        const result = await getCreditsService().initializeUserCredits(userId);
        res.status(200).json({ success: true, userCredits: result });
    }
    catch (error) {
        console.error('Error in initializeUserCreditsHttp:', error);
        res.status(500).json({ success: false, error: error.message || 'Internal server error' });
    }
});
exports.initializeUserCredits = exports.initializeUserCreditsHttp;
//# sourceMappingURL=creditsService.js.map