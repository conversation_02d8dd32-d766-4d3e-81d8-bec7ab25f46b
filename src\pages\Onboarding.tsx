import React, { useState, useRef } from 'react';
import {
    IonContent,
    IonPage,
    IonRow,
    IonButton,
    IonIcon,
    isPlatform
} from '@ionic/react';
import { arrowBack, arrowForward } from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import OnboardingSlide from '../components/OnboardingSlide';
import './Onboarding.css';

const Onboarding: React.FC = () => {
    const history = useHistory();
    const [activeSlide, setActiveSlide] = useState(0);

    const slideContent = [
        {
            image: "/assets/logo.png",
            mainSlide: true,
            title: "KrakenSim",
            text: "Global connectivity at your fingertips. Stay connected everywhere you go without roaming fees."
        },
        {
            image: "/assets/Onboarding/Page1.png",
            title: "Instant Activation",
            text: "Get online in minutes. Scan a QR code and you're connected instantly to local networks."
        },
        {
            image: "/assets/Onboarding/Page2.png",
            title: "Global Coverage",
            text: "Reliable data in over 150 countries. High-speed 4G/5G connection wherever you travel."
        },
        {
            image: "/assets/Onboarding/Page3.png",
            title: "Secure & Private",
            finalSlide: true,
            text: "Keep your connection private and secure. No more shady public WiFi or expensive roaming bill shock."
        }
    ];

    const handleNext = () => {
        if (activeSlide < slideContent.length - 1) {
            setActiveSlide(prev => prev + 1);
        }
    };

    const handlePrev = () => {
        if (activeSlide > 0) {
            setActiveSlide(prev => prev - 1);
        }
    };

    const handleLogin = () => history.push('/login');
    const handleRegister = () => history.push('/register');

    const isLast = activeSlide === slideContent.length - 1;
    const isFirst = activeSlide === 0;

    return (
        <IonPage>
            <IonContent fullscreen className="onboarding-page">
                <div className="slides-viewport" style={{ transform: `translateX(-${activeSlide * 100}%)` }}>
                    {slideContent.map((slide, index) => (
                        <OnboardingSlide
                            key={index}
                            {...slide}
                            onNext={handleNext}
                            onLogin={handleLogin}
                            onRegister={handleRegister}
                        />
                    ))}
                </div>

                {/* Navigation Dots */}
                <div className="navigation-dots">
                    {slideContent.map((_, index) => (
                        <div
                            key={index}
                            className={`dot ${activeSlide === index ? 'active' : ''}`}
                            onClick={() => setActiveSlide(index)}
                        />
                    ))}
                </div>

                <IonRow className="slide-navigation-buttons">
                    <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                        {!isFirst ? (
                            <IonButton fill="clear" onClick={handlePrev} className="nav-btn-classy">
                                <IonIcon icon={arrowBack} />
                            </IonButton>
                        ) : <div></div>}

                        {!isLast ? (
                            <IonButton fill="clear" onClick={handleNext} className="nav-btn-classy">
                                <IonIcon icon={arrowForward} />
                            </IonButton>
                        ) : <div></div>}
                    </div>
                </IonRow>
            </IonContent>
        </IonPage>
    );
};

export default Onboarding;
