/* Auth.css */

.auth-page {
  --background: #000000;
  --ion-background-color: #000000;
  display: flex;
  flex-direction: column;
}

.auth-content {
  --background: radial-gradient(circle at 50% 0%, #1a1a2e 0%, #000000 100%);
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 100%;
}

.auth-wrapper {
  display: flex;
  width: 95%;
  max-width: 1100px;
  min-height: 600px;
  margin: auto;
  border-radius: 32px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 40px 100px rgba(0, 0, 0, 0.5);
}

.auth-sidebar {
  display: none;
  flex: 1.2;
  background: linear-gradient(135deg, rgba(58, 123, 213, 0.15) 0%, rgba(0, 210, 255, 0.15) 100%);
  padding: 4rem;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.auth-sidebar::before {
  content: '';
  position: absolute;
  top: -20%;
  left: -20%;
  width: 140%;
  height: 140%;
  background: radial-gradient(circle at 30% 30%, rgba(0, 210, 255, 0.2) 0%, transparent 60%);
  z-index: 0;
}

.sidebar-content {
  position: relative;
  z-index: 1;
  max-width: 400px;
}

.sidebar-logo {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
}

.sidebar-logo img {
  height: 50px;
  margin-right: 15px;
}

.sidebar-tagline {
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.1;
  color: white;
  margin-bottom: 1.5rem;
  letter-spacing: -1px;
}

.sidebar-description {
  color: #aaa;
  font-size: 1.2rem;
  line-height: 1.6;
  font-weight: 400;
}

.auth-container {
  flex: 1;
  width: 100%;
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  background: rgba(0, 0, 0, 0.2);
}

@media (min-width: 992px) {
  .auth-sidebar {
    display: flex;
  }
}

@media (max-width: 991px) {
  .auth-wrapper {
    background: transparent;
    backdrop-filter: none;
    border: none;
    box-shadow: none;
    width: 100%;
    margin: 0;
    justify-content: center;
    align-items: center;
    min-height: auto;
  }
  
  .auth-container {
    padding: 1.5rem;
    background: transparent;
    max-width: 480px;
    margin: auto;
  }
}

.auth-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  margin: 0;
}

.auth-header {
  text-align: center;
  padding-bottom: 1rem;
}

.auth-title {
  font-size: 2rem;
  font-weight: 800;
  margin: 0;
  background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.auth-subtitle {
  color: #a0a0a0;
  font-size: 1rem;
  margin-top: 0.5rem;
}

@media (max-width: 500px) {
  .auth-content {
    justify-content: flex-start !important;
    padding-top: 2rem !important;
  }
  
  .auth-container {
    padding: 0.75rem 1rem;
  }
  
  .auth-header {
    padding-bottom: 0.25rem;
  }

  .auth-header img {
    height: 32px !important;
    width: auto !important;
    margin-bottom: 0.25rem !important;
    filter: drop-shadow(0 0 8px rgba(0, 210, 255, 0.3));
    background: transparent !important;
  }

  .auth-title {
    font-size: 1.4rem;
    letter-spacing: -0.5px;
  }

  .auth-subtitle {
    font-size: 0.85rem;
    margin-top: 0.15rem;
    opacity: 0.8;
  }

  .auth-form {
    margin-top: 0.75rem;
  }

  .auth-item {
    margin-bottom: 0.5rem;
    --min-height: 48px;
  }

  .auth-item ion-icon {
    font-size: 1.1rem;
  }

  .auth-item ion-label {
    font-size: 0.7rem;
    margin-bottom: 2px;
  }

  .auth-item ion-input {
    font-size: 0.95rem;
    --padding-top: 4px;
    --padding-bottom: 4px;
  }

  .auth-button-main, .auth-button-google {
    height: 44px;
    margin-top: 0.5rem;
    font-size: 0.95rem;
  }

  .terms-item {
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
    --min-height: auto;
  }

  .terms-checkbox {
    --size: 18px;
    margin-top: 0;
  }

  .terms-label {
    font-size: 0.8rem !important;
  }

  .auth-footer {
    margin-top: 1rem;
    font-size: 0.85rem;
  }
}

.auth-form {
  margin-top: 1.5rem;
}

.auth-item {
  --background: rgba(255, 255, 255, 0.03);
  --border-radius: 12px;
  --padding-start: 16px;
  --border-color: rgba(255, 255, 255, 0.1);
  margin-bottom: 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.auth-item.item-has-focus {
  border-color: #00d2ff;
  box-shadow: 0 0 10px rgba(0, 210, 255, 0.2);
}

.auth-item ion-icon {
  color: #00d2ff;
  font-size: 1.25rem;
}

.auth-item ion-label {
  color: #666;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.auth-item ion-input {
  --color: #ffffff;
  font-size: 1.1rem;
}

.auth-error {
  background: rgba(235, 68, 90, 0.1);
  border: 1px solid rgba(235, 68, 90, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  margin-bottom: 1.25rem;
  color: #eb445a;
  font-size: 0.9rem;
  text-align: center;
}

.auth-success {
  background: rgba(45, 211, 111, 0.1);
  border: 1px solid rgba(45, 211, 111, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  margin-bottom: 1.25rem;
  color: #2dd36f;
  font-size: 0.9rem;
  text-align: center;
}

.auth-button-main {
  --background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  --background-hover: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
  --color: #ffffff;
  --border-radius: 12px;
  --box-shadow: 0 4px 15px rgba(0, 210, 255, 0.3);
  font-weight: 700;
  height: 56px;
  margin-top: 1rem;
  font-size: 1.1rem;
  letter-spacing: 0.5px;
}

.auth-button-google {
  --background: rgba(255, 255, 255, 0.05);
  --color: white;
  --border-color: rgba(255, 255, 255, 0.2);
  --border-radius: 12px;
  --border-width: 1px;
  --border-style: solid;
  height: 56px;
  margin-top: 1rem;
  font-weight: 600;
}

.auth-button-google ion-icon {
  margin-right: 12px;
}

.auth-button-guest {
  --color: #a0a0a0;
  margin-top: 0.5rem;
  font-weight: 500;
}

.auth-footer {
  margin-top: 2rem;
  text-align: center;
  color: #888;
  font-size: 0.95rem;
}

.auth-link {
  color: #00d2ff;
  font-weight: 700;
  cursor: pointer;
  transition: color 0.2s;
}

.auth-link:hover {
  color: #ffffff;
  text-decoration: underline;
}

/* Checkbox specific styles */
.terms-item {
  --background: transparent;
  --padding-start: 0;
  --inner-padding-end: 0;
  margin-top: 1rem;
  margin-bottom: 1rem;
  align-items: flex-start;
}

.terms-checkbox {
  --size: 20px;
  --checkbox-background-checked: #00d2ff;
  --border-color: rgba(255, 255, 255, 0.2);
  --border-radius: 6px;
  margin-right: 12px;
  margin-top: 2px;
}

.terms-label {
  font-size: 0.9rem !important;
  color: #aaa !important;
  line-height: 1.4 !important;
  text-transform: none !important;
  letter-spacing: 0 !important;
  white-space: normal !important;
}

.terms-label span {
  color: #00d2ff;
  font-weight: 600;
}

/* Animations */
.auth-fade-in {
  animation: authFadeIn 0.8s ease-out forwards;
}

@keyframes authFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
