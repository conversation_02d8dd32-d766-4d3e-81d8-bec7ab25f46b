"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getESimOrderDetails = exports.getUserESimTopups = exports.getUserESimTopupsHttp = exports.getUserESimOrders = exports.getUserESimOrdersHttp = exports.topupESimCredits = exports.redeemCreditsForESim = exports.getESimRedemptionService = exports.ESimRedemptionService = void 0;
const admin = require("firebase-admin");
const functions = require("firebase-functions");
const cors = require("cors");
const firebase_functions_1 = require("firebase-functions");
const eSimService_1 = require("./eSimService");
// CORS handler for HTTP functions
const corsHandler = cors({ origin: true });
class ESimRedemptionService {
    constructor() {
        this.db = admin.firestore();
    }
    // Redeem credits for eSIM
    async redeemCreditsForESim(userId, request) {
        try {
            return await this.db.runTransaction(async (transaction) => {
                // 1. Check user's credit balance
                const userCreditsRef = this.db.collection('users').doc(userId).collection('credits').doc('balance');
                const userCreditsDoc = await transaction.get(userCreditsRef);
                if (!userCreditsDoc.exists) {
                    throw new Error('User credits not found');
                }
                const userCredits = userCreditsDoc.data();
                if (userCredits.totalCredits < request.creditCost) {
                    throw new Error(`Insufficient credits. Required: ${request.creditCost}, Available: ${userCredits.totalCredits}`);
                }
                // 2. Create eSIM order
                const orderRef = this.db.collection('users').doc(userId).collection('esim_orders').doc();
                const orderId = orderRef.id;
                const esimOrder = {
                    orderId,
                    userId,
                    planId: request.planId,
                    countryCode: request.countryCode,
                    countryName: request.countryName,
                    dataInGb: request.dataInGb,
                    durationDays: request.durationDays,
                    creditCost: request.creditCost,
                    originalPriceUsd: request.originalPriceUsd,
                    status: 'pending',
                    purchasedAt: new Date(),
                    dataUsed: 0,
                    lastUsageUpdate: new Date(),
                    metadata: {}
                };
                // 3. Deduct credits
                const updatedCredits = Object.assign(Object.assign({}, userCredits), { totalCredits: userCredits.totalCredits - request.creditCost, lifetimeSpent: userCredits.lifetimeSpent + request.creditCost, lastUpdated: new Date() });
                // 4. Create credit transaction
                const transactionRef = this.db.collection('users').doc(userId).collection('credit_transactions').doc();
                const creditTransaction = {
                    id: transactionRef.id,
                    userId,
                    type: 'redemption',
                    amount: -request.creditCost,
                    esimOrderId: orderId,
                    esimPlanId: request.planId.toString(),
                    esimCountryCode: request.countryCode,
                    platform: 'web',
                    status: 'completed',
                    metadata: {
                        countryName: request.countryName,
                        dataInGb: request.dataInGb,
                        durationDays: request.durationDays
                    },
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                // 5. Commit all changes atomically
                transaction.set(orderRef, esimOrder);
                transaction.set(userCreditsRef, updatedCredits);
                transaction.set(transactionRef, creditTransaction);
                return { success: true, orderId };
            });
        }
        catch (error) {
            firebase_functions_1.logger.error('Failed to redeem credits for eSIM:', error);
            return {
                success: false,
                error: error.message || 'Failed to redeem credits'
            };
        }
    }
    // Process eSIM with provider (called after successful redemption)
    async processESimWithProvider(userId, orderId) {
        try {
            const orderRef = this.db.collection('users').doc(userId).collection('esim_orders').doc(orderId);
            const orderDoc = await orderRef.get();
            if (!orderDoc.exists) {
                throw new Error('eSIM order not found');
            }
            const order = orderDoc.data();
            // Call provider API to purchase eSIM
            const providerResult = await eSimService_1.eSimApiService.purchaseESim(order.planId);
            if (providerResult.success !== 1) {
                // Update order status to failed
                await orderRef.update({
                    status: 'cancelled',
                    metadata: Object.assign(Object.assign({}, order.metadata), { providerError: providerResult.message, failedAt: new Date() })
                });
                // Refund credits
                await this.refundCreditsForFailedOrder(userId, order);
                throw new Error(providerResult.message || 'Provider API failed');
            }
            // Get eSIM profile details for activation
            let profileData = null;
            try {
                profileData = await eSimService_1.eSimApiService.getESimProfile(providerResult.transactionId);
            }
            catch (error) {
                firebase_functions_1.logger.warn('Failed to get eSIM profile immediately after purchase:', error);
            }
            // Update order with provider details
            await orderRef.update({
                status: 'provisioned',
                providerTransactionId: providerResult.transactionId,
                activationCode: profileData === null || profileData === void 0 ? void 0 : profileData.activationCode,
                // Note: QR code would need to be generated from activation code if needed
                expiresAt: new Date(Date.now() + order.durationDays * 24 * 60 * 60 * 1000),
                metadata: Object.assign(Object.assign({}, order.metadata), { provisionedAt: new Date(), providerResponse: providerResult, profileData: profileData })
            });
            return { success: true };
        }
        catch (error) {
            firebase_functions_1.logger.error('Failed to process eSIM with provider:', error);
            return {
                success: false,
                error: error.message || 'Failed to process eSIM'
            };
        }
    }
    // Refund credits for failed order
    async refundCreditsForFailedOrder(userId, order) {
        await this.db.runTransaction(async (transaction) => {
            const userCreditsRef = this.db.collection('users').doc(userId).collection('credits').doc('balance');
            const userCreditsDoc = await transaction.get(userCreditsRef);
            if (!userCreditsDoc.exists) {
                throw new Error('User credits not found for refund');
            }
            const userCredits = userCreditsDoc.data();
            // Refund credits
            const updatedCredits = Object.assign(Object.assign({}, userCredits), { totalCredits: userCredits.totalCredits + order.creditCost, lifetimeSpent: userCredits.lifetimeSpent - order.creditCost, lastUpdated: new Date() });
            // Create refund transaction
            const transactionRef = this.db.collection('users').doc(userId).collection('credit_transactions').doc();
            const creditTransaction = {
                id: transactionRef.id,
                userId,
                type: 'refund',
                amount: order.creditCost,
                esimOrderId: order.orderId,
                platform: 'web',
                status: 'completed',
                metadata: {
                    reason: 'Provider API failure',
                    originalOrderId: order.orderId
                },
                createdAt: new Date(),
                updatedAt: new Date()
            };
            transaction.set(userCreditsRef, updatedCredits);
            transaction.set(transactionRef, creditTransaction);
        });
    }
    // Top up existing eSIM
    async topupESim(userId, request) {
        try {
            return await this.db.runTransaction(async (transaction) => {
                // 1. Verify eSIM order exists and is active
                const orderRef = this.db.collection('users').doc(userId).collection('esim_orders').doc(request.esimOrderId);
                const orderDoc = await transaction.get(orderRef);
                if (!orderDoc.exists) {
                    throw new Error('eSIM order not found');
                }
                const order = orderDoc.data();
                if (order.status !== 'provisioned' && order.status !== 'activated') {
                    throw new Error('eSIM is not active for top-up');
                }
                // 2. Check user's credit balance
                const userCreditsRef = this.db.collection('users').doc(userId).collection('credits').doc('balance');
                const userCreditsDoc = await transaction.get(userCreditsRef);
                if (!userCreditsDoc.exists) {
                    throw new Error('User credits not found');
                }
                const userCredits = userCreditsDoc.data();
                if (userCredits.totalCredits < request.creditCost) {
                    throw new Error(`Insufficient credits for top-up. Required: ${request.creditCost}, Available: ${userCredits.totalCredits}`);
                }
                // 3. Create top-up record
                const topupRef = this.db.collection('users').doc(userId).collection('esim_topups').doc();
                const topupId = topupRef.id;
                const esimTopup = {
                    topupId,
                    userId,
                    esimOrderId: request.esimOrderId,
                    dataInGb: request.dataInGb,
                    creditCost: request.creditCost,
                    status: 'pending',
                    createdAt: new Date()
                };
                // 4. Deduct credits
                const updatedCredits = Object.assign(Object.assign({}, userCredits), { totalCredits: userCredits.totalCredits - request.creditCost, lifetimeSpent: userCredits.lifetimeSpent + request.creditCost, lastUpdated: new Date() });
                // 5. Create credit transaction
                const transactionRef = this.db.collection('users').doc(userId).collection('credit_transactions').doc();
                const creditTransaction = {
                    id: transactionRef.id,
                    userId,
                    type: 'topup',
                    amount: -request.creditCost,
                    esimOrderId: request.esimOrderId,
                    topupOrderId: topupId,
                    platform: 'web',
                    status: 'completed',
                    metadata: {
                        dataInGb: request.dataInGb
                    },
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                // 6. Commit all changes
                transaction.set(topupRef, esimTopup);
                transaction.set(userCreditsRef, updatedCredits);
                transaction.set(transactionRef, creditTransaction);
                return { success: true, topupId };
            });
        }
        catch (error) {
            firebase_functions_1.logger.error('Failed to top up eSIM:', error);
            return {
                success: false,
                error: error.message || 'Failed to top up eSIM'
            };
        }
    }
}
exports.ESimRedemptionService = ESimRedemptionService;
// Singleton instance
let eSimRedemptionService;
function getESimRedemptionService() {
    if (!eSimRedemptionService) {
        eSimRedemptionService = new ESimRedemptionService();
    }
    return eSimRedemptionService;
}
exports.getESimRedemptionService = getESimRedemptionService;
// Callable endpoints
exports.redeemCreditsForESim = functions.https.onCall(async (data, context) => {
    try {
        // Authentication: prefer context.auth.uid, fallback to provided userId
        const userId = (context && context.auth && context.auth.uid) || (data === null || data === void 0 ? void 0 : data.userId);
        if (!userId) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const result = await getESimRedemptionService().redeemCreditsForESim(userId, data);
        if (result.success && result.orderId) {
            // Process with provider in background
            getESimRedemptionService().processESimWithProvider(userId, result.orderId)
                .catch(error => firebase_functions_1.logger.error('Background eSIM processing failed:', error));
        }
        return result;
    }
    catch (error) {
        firebase_functions_1.logger.error('Error in redeemCreditsForESim (callable):', error);
        throw new functions.https.HttpsError('internal', (error === null || error === void 0 ? void 0 : error.message) || 'Internal server error');
    }
});
exports.topupESimCredits = functions.https.onCall(async (data, context) => {
    try {
        const userId = (context && context.auth && context.auth.uid) || (data === null || data === void 0 ? void 0 : data.userId);
        if (!userId) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const result = await getESimRedemptionService().topupESim(userId, data);
        return result;
    }
    catch (error) {
        firebase_functions_1.logger.error('Error in topupESim (callable):', error);
        throw new functions.https.HttpsError('internal', (error === null || error === void 0 ? void 0 : error.message) || 'Internal server error');
    }
});
exports.getUserESimOrdersHttp = functions.https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        try {
            // Verify authentication
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.status(401).json({ error: 'Authentication required' });
                return;
            }
            const idToken = authHeader.split('Bearer ')[1];
            const decodedToken = await admin.auth().verifyIdToken(idToken);
            const userId = decodedToken.uid;
            const db = admin.firestore();
            const ordersSnapshot = await db
                .collection('users')
                .doc(userId)
                .collection('esim_orders')
                .orderBy('purchasedAt', 'desc')
                .get();
            const orders = ordersSnapshot.docs.map(doc => {
                var _a, _b, _c;
                return (Object.assign(Object.assign({}, doc.data()), { purchasedAt: doc.data().purchasedAt.toDate(), activatedAt: (_a = doc.data().activatedAt) === null || _a === void 0 ? void 0 : _a.toDate(), expiresAt: (_b = doc.data().expiresAt) === null || _b === void 0 ? void 0 : _b.toDate(), lastUsageUpdate: (_c = doc.data().lastUsageUpdate) === null || _c === void 0 ? void 0 : _c.toDate() }));
            });
            res.status(200).json({ success: true, orders });
        }
        catch (error) {
            firebase_functions_1.logger.error('Error in getUserESimOrdersHttp:', error);
            res.status(500).json({ error: error.message || 'Internal server error' });
        }
    });
});
// Alias for frontend compatibility
exports.getUserESimOrders = exports.getUserESimOrdersHttp;
exports.getUserESimTopupsHttp = functions.https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        try {
            // Verify authentication
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.status(401).json({ error: 'Authentication required' });
                return;
            }
            const idToken = authHeader.split('Bearer ')[1];
            const decodedToken = await admin.auth().verifyIdToken(idToken);
            const userId = decodedToken.uid;
            const db = admin.firestore();
            const topupsSnapshot = await db
                .collection('users')
                .doc(userId)
                .collection('esim_topups')
                .orderBy('createdAt', 'desc')
                .get();
            const topups = topupsSnapshot.docs.map(doc => {
                var _a;
                return (Object.assign(Object.assign({}, doc.data()), { createdAt: doc.data().createdAt.toDate(), completedAt: (_a = doc.data().completedAt) === null || _a === void 0 ? void 0 : _a.toDate() }));
            });
            res.status(200).json({ success: true, topups });
        }
        catch (error) {
            firebase_functions_1.logger.error('Error in getUserESimTopupsHttp:', error);
            res.status(500).json({ error: error.message || 'Internal server error' });
        }
    });
});
// Alias for frontend compatibility
exports.getUserESimTopups = exports.getUserESimTopupsHttp;
exports.getESimOrderDetails = functions.https.onCall(async (data, context) => {
    var _a, _b, _c;
    try {
        const userId = (context && context.auth && context.auth.uid) || (data === null || data === void 0 ? void 0 : data.userId);
        if (!userId) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const { orderId } = data;
        if (!orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
        }
        const db = admin.firestore();
        const orderDoc = await db
            .collection('users')
            .doc(userId)
            .collection('esim_orders')
            .doc(orderId)
            .get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        const order = Object.assign(Object.assign({}, orderData), { purchasedAt: orderData === null || orderData === void 0 ? void 0 : orderData.purchasedAt.toDate(), activatedAt: (_a = orderData === null || orderData === void 0 ? void 0 : orderData.activatedAt) === null || _a === void 0 ? void 0 : _a.toDate(), expiresAt: (_b = orderData === null || orderData === void 0 ? void 0 : orderData.expiresAt) === null || _b === void 0 ? void 0 : _b.toDate(), lastUsageUpdate: (_c = orderData === null || orderData === void 0 ? void 0 : orderData.lastUsageUpdate) === null || _c === void 0 ? void 0 : _c.toDate() });
        return { success: true, order };
    }
    catch (error) {
        firebase_functions_1.logger.error('Error in getESimOrderDetails (callable):', error);
        throw new functions.https.HttpsError('internal', (error === null || error === void 0 ? void 0 : error.message) || 'Internal server error');
    }
});
//# sourceMappingURL=eSimRedemptionService.js.map