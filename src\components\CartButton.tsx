import React, { useState } from 'react';
import {
  IonButton,
  IonIcon,
  IonBadge
} from '@ionic/react';
import { bagOutline } from 'ionicons/icons';
import { useCart } from '../contexts/CartContext';
import Cart from './Cart';
import './CartButton.css';

const CartButton: React.FC = () => {
  const { state } = useCart();
  const [isCartOpen, setIsCartOpen] = useState(false);

  const handleCartClick = () => {
    setIsCartOpen(true);
  };

  const handleCartClose = () => {
    setIsCartOpen(false);
  };

  return (
    <>
      <IonButton
        fill="clear"
        onClick={handleCartClick}
        className="cart-button"
      >
        <div className="cart-icon-container">
          <IonIcon icon={bagOutline} />
          {state.totalItems > 0 && (
            <IonBadge className="cart-badge" color="danger">
              {state.totalItems > 99 ? '99+' : state.totalItems}
            </IonBadge>
          )}
        </div>
      </IonButton>

      <Cart isOpen={isCartOpen} onClose={handleCartClose} />
    </>
  );
};

export default CartButton;
