"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCreditPackages = void 0;
const functions = require("firebase-functions");
const stripe_1 = require("stripe");
const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2025-08-27.basil',
});
exports.getCreditPackages = functions.https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type');
    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
    }
    try {
        const products = await stripe.products.list({ limit: 10 });
        const packages = [];
        for (const product of products.data) {
            const prices = await stripe.prices.list({
                product: product.id,
                active: true,
                limit: 1
            });
            const price = prices.data[0];
            if (price && price.unit_amount) {
                const priceUsd = price.unit_amount / 100;
                packages.push({
                    id: product.name.toLowerCase().replace(/\s+/g, '_'),
                    name: product.name,
                    price: priceUsd,
                    credits: priceUsd,
                    vibe: product.description || 'Credit package',
                    useCase: 'Perfect for your needs',
                    icon: 'MINNOW',
                    color: 'primary',
                    stripePriceId: price.id,
                    stripeProductId: product.id,
                    active: product.active
                });
            }
        }
        packages.sort((a, b) => a.price - b.price);
        res.json(packages);
    }
    catch (error) {
        console.error('Stripe error:', error);
        res.status(500).json({ error: 'Failed to fetch packages' });
    }
});
//# sourceMappingURL=getCreditPackages.js.map