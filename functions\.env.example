# Firebase Cloud Functions Environment Variables
# Copy this file to .env and fill in your actual values

# Stripe Configuration (Required for Credits System)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# Optional: Set to 'true' for test mode, 'false' for production
STRIPE_TEST_MODE=true

# Firebase Project Configuration (usually auto-detected)
# FIREBASE_PROJECT_ID=esim-numero

# Optional: Logging level (debug, info, warn, error)
LOG_LEVEL=info

# Instructions:
# 1. Copy this file: cp .env.example .env
# 2. Get your Stripe keys from: https://dashboard.stripe.com/apikeys
# 3. Set up webhook endpoint: https://dashboard.stripe.com/webhooks
# 4. Replace placeholder values with your actual Stripe keys
# 5. Never commit .env file to version control
