import { CartItem } from '../contexts/CartContext';

export interface CartSummary {
  subtotal: number;
  tax: number;
  total: number;
  itemCount: number;
  savings: number;
}

export interface CheckoutData {
  items: CartItem[];
  summary: CartSummary;
  customerInfo?: {
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
  };
  paymentMethod?: {
    type: 'card' | 'apple_pay' | 'google_pay' | 'in_app_purchase';
    details?: any;
  };
}

export class CartService {
  private static readonly TAX_RATE = 0.0; // No tax for digital products in most jurisdictions
  private static readonly BULK_DISCOUNT_THRESHOLD = 3;
  private static readonly BULK_DISCOUNT_RATE = 0.1; // 10% discount for 3+ items

  static calculateCartSummary(items: CartItem[]): CartSummary {
    const subtotal = items.reduce((sum, item) => {
      return sum + (parseFloat(item.priceUsd) * item.quantity);
    }, 0);

    const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
    
    // Apply bulk discount if applicable
    const bulkDiscount = itemCount >= this.BULK_DISCOUNT_THRESHOLD 
      ? subtotal * this.BULK_DISCOUNT_RATE 
      : 0;

    const discountedSubtotal = subtotal - bulkDiscount;
    const tax = discountedSubtotal * this.TAX_RATE;
    const total = discountedSubtotal + tax;

    return {
      subtotal,
      tax,
      total,
      itemCount,
      savings: bulkDiscount,
    };
  }

  static formatPrice(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  }

  static validateCart(items: CartItem[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (items.length === 0) {
      errors.push('Cart is empty');
    }

    // Check for invalid quantities
    items.forEach((item, index) => {
      if (item.quantity <= 0) {
        errors.push(`Item ${index + 1} has invalid quantity`);
      }
      if (item.quantity > 10) {
        errors.push(`Item ${index + 1} exceeds maximum quantity (10)`);
      }
    });

    // Check for invalid prices
    items.forEach((item, index) => {
      const price = parseFloat(item.priceUsd);
      if (isNaN(price) || price <= 0) {
        errors.push(`Item ${index + 1} has invalid price`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  static groupItemsByCountry(items: CartItem[]): Record<string, CartItem[]> {
    return items.reduce((groups, item) => {
      const country = item.countryName;
      if (!groups[country]) {
        groups[country] = [];
      }
      groups[country].push(item);
      return groups;
    }, {} as Record<string, CartItem[]>);
  }

  static getRecommendedItems(currentItems: CartItem[], allPlans: any[]): any[] {
    // Simple recommendation logic - suggest plans from countries not in cart
    const countriesInCart = new Set(currentItems.map(item => item.countryCode));
    
    return allPlans
      .filter(plan => !countriesInCart.has(plan.countryCode))
      .slice(0, 3); // Return top 3 recommendations
  }

  static estimateDeliveryTime(items: CartItem[]): string {
    // eSIMs are delivered instantly
    return 'Instant delivery after payment';
  }

  static generateOrderId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `ESIM-${timestamp}-${random.toUpperCase()}`;
  }

  static prepareCheckoutData(
    items: CartItem[],
    customerInfo?: CheckoutData['customerInfo'],
    paymentMethod?: CheckoutData['paymentMethod']
  ): CheckoutData {
    const summary = this.calculateCartSummary(items);
    
    return {
      items,
      summary,
      customerInfo,
      paymentMethod,
    };
  }

  static saveCartToStorage(items: CartItem[]): void {
    try {
      localStorage.setItem('esim-cart', JSON.stringify(items));
      localStorage.setItem('esim-cart-timestamp', Date.now().toString());
    } catch (error) {
      console.error('Failed to save cart to storage:', error);
    }
  }

  static loadCartFromStorage(): CartItem[] {
    try {
      const cartData = localStorage.getItem('esim-cart');
      const timestamp = localStorage.getItem('esim-cart-timestamp');
      
      if (!cartData || !timestamp) {
        return [];
      }

      // Check if cart is older than 24 hours
      const cartAge = Date.now() - parseInt(timestamp);
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      
      if (cartAge > maxAge) {
        this.clearCartFromStorage();
        return [];
      }

      const items: CartItem[] = JSON.parse(cartData);
      return items.map(item => ({
        ...item,
        addedAt: new Date(item.addedAt),
      }));
    } catch (error) {
      console.error('Failed to load cart from storage:', error);
      return [];
    }
  }

  static clearCartFromStorage(): void {
    try {
      localStorage.removeItem('esim-cart');
      localStorage.removeItem('esim-cart-timestamp');
    } catch (error) {
      console.error('Failed to clear cart from storage:', error);
    }
  }

  static getCartItemKey(planId: number, countryCode: string): string {
    return `${planId}-${countryCode}`;
  }

  static isValidCartItem(item: any): item is CartItem {
    return (
      typeof item === 'object' &&
      item !== null &&
      typeof item.id === 'string' &&
      typeof item.planId === 'number' &&
      typeof item.countryCode === 'string' &&
      typeof item.countryName === 'string' &&
      typeof item.dataInGb === 'number' &&
      typeof item.duration === 'number' &&
      typeof item.priceUsd === 'string' &&
      typeof item.speed === 'string' &&
      typeof item.ip === 'string' &&
      typeof item.network === 'string' &&
      typeof item.quantity === 'number' &&
      item.addedAt instanceof Date
    );
  }

  static sanitizeCartItems(items: any[]): CartItem[] {
    return items.filter(this.isValidCartItem);
  }

  static calculateItemTotal(item: CartItem): number {
    return parseFloat(item.priceUsd) * item.quantity;
  }

  static getUniqueCountries(items: CartItem[]): string[] {
    const countries = new Set(items.map(item => item.countryName));
    return Array.from(countries).sort();
  }

  static getTotalDataAllocation(items: CartItem[]): number {
    return items.reduce((total, item) => {
      return total + (item.dataInGb * item.quantity);
    }, 0);
  }

  static getAveragePlanDuration(items: CartItem[]): number {
    if (items.length === 0) return 0;
    
    const totalDuration = items.reduce((sum, item) => {
      return sum + (item.duration * item.quantity);
    }, 0);
    
    const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);
    
    return Math.round(totalDuration / totalQuantity);
  }
}
