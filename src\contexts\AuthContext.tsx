import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, onAuthStateChanged, signOut } from 'firebase/auth';
import { auth } from '../firebase';
import { Capacitor } from '@capacitor/core';
import { CapacitorAuthService, AuthUser } from '../services/capacitorAuthService';

interface AuthContextType {
  currentUser: User | AuthUser | null;
  loading: boolean;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType);

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  const logout = async () => {
    if (Capacitor.isNativePlatform()) {
      await CapacitorAuthService.signOut();
    } else {
      await signOut(auth);
    }
  };

  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    if (Capacitor.isNativePlatform()) {
      // Use Capacitor Firebase Auth for native platforms
      CapacitorAuthService.initialize().then(() => {
        // Get initial user
        CapacitorAuthService.getCurrentUser().then((user) => {
          console.log('AuthContext - Initial user from Capacitor:', user);
          console.log('AuthContext - User type:', typeof user);
          console.log('AuthContext - User uid:', user?.uid);
          setCurrentUser(user);
          setLoading(false);
        });

        // Listen for auth state changes
        CapacitorAuthService.addAuthStateListener((user) => {
          console.log('AuthContext - Auth state changed:', user);
          console.log('AuthContext - User type:', typeof user);
          console.log('AuthContext - User uid:', user?.uid);
          setCurrentUser(user);
          setLoading(false);
        }).then((removeListener) => {
          unsubscribe = removeListener;
        });
      });
    } else {
      // Use standard Firebase Auth for web
      unsubscribe = onAuthStateChanged(auth, (user) => {
        setCurrentUser(user);
        setLoading(false);
      });
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  const value = {
    currentUser,
    loading,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};