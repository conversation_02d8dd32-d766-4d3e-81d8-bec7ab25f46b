import React, { useState, useEffect } from 'react';
import {
  IonAlert,
  IonButton,
  IonIcon
} from '@ionic/react';
import { star, close, time } from 'ionicons/icons';

interface AppRatingProps {
  trigger?: string; // Event that triggered the rating prompt
}

const AppRating: React.FC<AppRatingProps> = ({ trigger = 'general' }) => {
  const [showRatingAlert, setShowRatingAlert] = useState(false);

  useEffect(() => {
    checkShouldShowRating();
  }, [trigger]);

  const checkShouldShowRating = () => {
    const ratingPreference = localStorage.getItem('appRatingPreference');
    const lastPromptDate = localStorage.getItem('lastRatingPrompt');
    const appUsageCount = parseInt(localStorage.getItem('appUsageCount') || '0');

    // Don't show if user selected "Never"
    if (ratingPreference === 'never') {
      return;
    }

    // Don't show if user selected "Later" and it's been less than 7 days
    if (ratingPreference === 'later' && lastPromptDate) {
      const daysSinceLastPrompt = (Date.now() - parseInt(lastPromptDate)) / (1000 * 60 * 60 * 24);
      if (daysSinceLastPrompt < 7) {
        return;
      }
    }

    // Show rating prompt based on usage patterns
    const shouldShow =
      (trigger === 'purchase' && Math.random() < 0.3) || // 30% chance after purchase
      (trigger === 'general' && appUsageCount > 5 && Math.random() < 0.1); // 10% chance after 5+ uses

    if (shouldShow) {
      // Increment usage count
      localStorage.setItem('appUsageCount', (appUsageCount + 1).toString());
      setShowRatingAlert(true);
    }
  };

  const handleRateNow = () => {
    localStorage.setItem('appRatingPreference', 'rated');
    localStorage.setItem('lastRatingPrompt', Date.now().toString());

    // Open app store
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const url = isIOS
      ? 'https://apps.apple.com/app/id123456789'
      : 'https://play.google.com/store/apps/details?id=com.esimnumero.app';

    window.open(url, '_blank');
    setShowRatingAlert(false);
  };

  const handleLater = () => {
    localStorage.setItem('appRatingPreference', 'later');
    localStorage.setItem('lastRatingPrompt', Date.now().toString());
    setShowRatingAlert(false);
  };

  const handleNever = () => {
    localStorage.setItem('appRatingPreference', 'never');
    setShowRatingAlert(false);
  };

  return (
    <IonAlert
      isOpen={showRatingAlert}
      onDidDismiss={() => setShowRatingAlert(false)}
      header="Enjoying KrakenSim?"
      message="If you're enjoying our app, would you mind taking a moment to rate it? It really helps us improve!"
      buttons={[
        {
          text: 'Never',
          handler: handleNever
        },
        {
          text: 'Later',
          handler: handleLater
        },
        {
          text: 'Rate Now',
          handler: handleRateNow
        }
      ]}
    />
  );
};

export default AppRating;