﻿
> Configure project :app
WARNING: Using flatDir should be avoided because it doesn't support any meta-data formats.

> Configure project :capacitor-cordova-android-plugins
WARNING: Using flatDir should be avoided because it doesn't support any meta-data formats.

> Task :app:preBuild UP-TO-DATE
> Task :app:preDebugBuild UP-TO-DATE
> Task :app:mergeDebugNativeDebugMetadata NO-SOURCE
> Task :app:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :capacitor-android:preBuild UP-TO-DATE
> Task :capacitor-android:preDebugBuild UP-TO-DATE
> Task :capacitor-android:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-app:preBuild UP-TO-DATE
> Task :capacitor-app:preDebugBuild UP-TO-DATE
> Task :capacitor-app:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-community-stripe:preBuild UP-TO-DATE
> Task :capacitor-community-stripe:preDebugBuild UP-TO-DATE
> Task :capacitor-community-stripe:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-cordova-android-plugins:preBuild UP-TO-DATE
> Task :capacitor-cordova-android-plugins:preDebugBuild UP-TO-DATE
> Task :capacitor-cordova-android-plugins:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-firebase-app:preBuild UP-TO-DATE
> Task :capacitor-firebase-app:preDebugBuild UP-TO-DATE
> Task :capacitor-firebase-app:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-firebase-authentication:preBuild UP-TO-DATE
> Task :capacitor-firebase-authentication:preDebugBuild UP-TO-DATE
> Task :capacitor-firebase-authentication:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-haptics:preBuild UP-TO-DATE
> Task :capacitor-haptics:preDebugBuild UP-TO-DATE
> Task :capacitor-haptics:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-keyboard:preBuild UP-TO-DATE
> Task :capacitor-keyboard:preDebugBuild UP-TO-DATE
> Task :capacitor-keyboard:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-push-notifications:preBuild UP-TO-DATE
> Task :capacitor-push-notifications:preDebugBuild UP-TO-DATE
> Task :capacitor-push-notifications:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-status-bar:preBuild UP-TO-DATE
> Task :capacitor-status-bar:preDebugBuild UP-TO-DATE
> Task :capacitor-status-bar:writeDebugAarMetadata UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:preBuild UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:preDebugBuild UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:writeDebugAarMetadata UP-TO-DATE
> Task :app:checkDebugAarMetadata UP-TO-DATE
> Task :app:generateDebugResValues UP-TO-DATE
> Task :app:processDebugGoogleServices UP-TO-DATE
> Task :capacitor-android:generateDebugResValues UP-TO-DATE
> Task :capacitor-android:generateDebugResources UP-TO-DATE
> Task :capacitor-android:packageDebugResources UP-TO-DATE
> Task :capacitor-app:generateDebugResValues UP-TO-DATE
> Task :capacitor-app:generateDebugResources UP-TO-DATE
> Task :capacitor-app:packageDebugResources UP-TO-DATE
> Task :capacitor-community-stripe:generateDebugResValues UP-TO-DATE
> Task :capacitor-community-stripe:generateDebugResources UP-TO-DATE
> Task :capacitor-community-stripe:packageDebugResources UP-TO-DATE
> Task :capacitor-cordova-android-plugins:generateDebugResValues UP-TO-DATE
> Task :capacitor-cordova-android-plugins:generateDebugResources UP-TO-DATE
> Task :capacitor-cordova-android-plugins:packageDebugResources UP-TO-DATE
> Task :capacitor-firebase-app:generateDebugResValues UP-TO-DATE
> Task :capacitor-firebase-app:generateDebugResources UP-TO-DATE
> Task :capacitor-firebase-app:packageDebugResources UP-TO-DATE
> Task :capacitor-firebase-authentication:generateDebugResValues UP-TO-DATE
> Task :capacitor-firebase-authentication:generateDebugResources UP-TO-DATE
> Task :capacitor-firebase-authentication:packageDebugResources UP-TO-DATE
> Task :capacitor-haptics:generateDebugResValues UP-TO-DATE
> Task :capacitor-haptics:generateDebugResources UP-TO-DATE
> Task :capacitor-haptics:packageDebugResources UP-TO-DATE
> Task :capacitor-keyboard:generateDebugResValues UP-TO-DATE
> Task :capacitor-keyboard:generateDebugResources UP-TO-DATE
> Task :capacitor-keyboard:packageDebugResources UP-TO-DATE
> Task :capacitor-push-notifications:generateDebugResValues UP-TO-DATE
> Task :capacitor-push-notifications:generateDebugResources UP-TO-DATE
> Task :capacitor-push-notifications:packageDebugResources UP-TO-DATE
> Task :capacitor-status-bar:generateDebugResValues UP-TO-DATE
> Task :capacitor-status-bar:generateDebugResources UP-TO-DATE
> Task :capacitor-status-bar:packageDebugResources UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:generateDebugResValues UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:generateDebugResources UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:packageDebugResources UP-TO-DATE
> Task :app:mapDebugSourceSetPaths UP-TO-DATE
> Task :app:generateDebugResources UP-TO-DATE

> Task :app:mergeDebugResources FAILED
cmd : ERROR: [color/i
c_launcher_background
] C:\Users\<USER>\sour
ce\repos\esim-numero\
android\app\src\main\
res\values\ic_launche
r_background.xml [col
or/ic_launcher_backgr
ound] C:\Users\<USER>\
source\repos\esim-num
ero\android\app\src\m
ain\res\values\ic_lau
ncher_v2_background.x
ml: Resource and 
asset merger: 
Duplicate resources
At line:1 char:1
+ cmd /c gradlew 
assembleDebug > 
build_log.txt 2>&1
+ ~~~~~~~~~~~~~~~~~~~
~~~~~~~~~~~~~~~~~~~~~
~~~~~~~~~
    + CategoryInfo   
           : 
NotSpecifi    ed: 
(ERROR: [color    
/i...icate resourc   
 es:String) [], Rem  
  oteException
    + 
FullyQualifiedEr    
rorId : NativeComm   
 andError
 
[Incubating] Problems report is available at: file:///C:/Users/<USER>/source/repos/esim-numero/android/build/reports/problems/problems-report.html

FAILURE: Build 
failed with an 
exception.

* What went wrong:
Execution failed for 
task ':app:mergeDebug
Resources'.
> [color/ic_launcher_
background] C:\Users\<USER>\source\repos\es
im-numero\android\app
\src\main\res\values\
ic_launcher_backgroun
d.xml	[color/ic_launc
her_background] C:\Us
ers\colli\source\repo
s\esim-numero\android
\app\src\main\res\val
ues\ic_launcher_v2_ba
ckground.xml: Error: 
Duplicate resources

* Try:
> Run with 
--stacktrace option 
to get the stack 
trace.
> Run with --info or 
--debug option to 
get more log output.
> Run with --scan to 
get full insights.
> Get more help at ht
tps://help.gradle.org
.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.11.1/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD FAILED in 4s
50 actionable tasks: 1 executed, 49 up-to-date
