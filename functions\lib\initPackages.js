"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initPackages = void 0;
const functions = require("firebase-functions");
const cors = require("cors");
// import { initializeCreditPackages } from './initializeCreditPackages';
const corsHandler = cors({ origin: true });
exports.initPackages = functions.https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        try {
            // await initializeCreditPackages();
            res.status(200).json({
                success: true,
                message: 'Credit packages initialized successfully'
            });
        }
        catch (error) {
            console.error('Error initializing credit packages:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to initialize credit packages'
            });
        }
    });
});
//# sourceMappingURL=initPackages.js.map