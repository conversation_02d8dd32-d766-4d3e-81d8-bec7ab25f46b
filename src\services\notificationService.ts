import { getToken, onMessage, Messaging } from 'firebase/messaging';
import { messaging } from '../firebase';
import { Capacitor } from '@capacitor/core';
import { PushNotifications, Token, PushNotificationSchema, ActionPerformed } from '@capacitor/push-notifications';

export class NotificationService {
  private vapidKey = 'YOUR_VAPID_KEY'; // Get this from Firebase Console

  async initializeNotifications(): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      await this.initializeNativeNotifications();
    } else {
      await this.initializeWebNotifications();
    }
  }

  // Web notifications (PWA)
  private async initializeWebNotifications(): Promise<void> {
    try {
      // Request permission
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        console.log('Notification permission denied');
        return;
      }

      // Get FCM token
      const token = await getToken(messaging, { vapidKey: this.vapidKey });
      if (token) {
        console.log('FCM Token:', token);
        await this.saveTokenToServer(token);
      }

      // Listen for foreground messages
      onMessage(messaging, (payload) => {
        console.log('Message received in foreground:', payload);
        this.showNotification(payload);
      });

    } catch (error) {
      console.error('Error initializing web notifications:', error);
    }
  }

  // Native notifications (iOS/Android)
  private async initializeNativeNotifications(): Promise<void> {
    try {
      // Request permission
      let permStatus = await PushNotifications.checkPermissions();
      if (permStatus.receive === 'prompt') {
        permStatus = await PushNotifications.requestPermissions();
      }

      if (permStatus.receive !== 'granted') {
        console.log('Push notification permission denied');
        return;
      }

      // Register for push notifications
      await PushNotifications.register();

      // Listen for token registration
      PushNotifications.addListener('registration', (token: Token) => {
        console.log('Push registration success, token: ' + token.value);
        this.saveTokenToServer(token.value);
      });

      // Listen for registration errors
      PushNotifications.addListener('registrationError', (error: any) => {
        console.error('Error on registration: ' + JSON.stringify(error));
      });

      // Listen for push notifications
      PushNotifications.addListener('pushNotificationReceived', (notification: PushNotificationSchema) => {
        console.log('Push notification received: ', notification);
        this.handleNotificationReceived(notification);
      });

      // Listen for notification actions
      PushNotifications.addListener('pushNotificationActionPerformed', (notification: ActionPerformed) => {
        console.log('Push notification action performed', notification);
        this.handleNotificationAction(notification);
      });

    } catch (error) {
      console.error('Error initializing native notifications:', error);
    }
  }

  private async saveTokenToServer(token: string): Promise<void> {
    try {
      const user = JSON.parse(localStorage.getItem('currentUser') || '{}');
      if (user.uid) {
        // Call your Firebase function to save the token
        const response = await fetch('https://us-central1-esim-numero.cloudfunctions.net/saveNotificationToken', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${await user.getIdToken()}`
          },
          body: JSON.stringify({
            token,
            platform: Capacitor.getPlatform(),
            userId: user.uid
          })
        });

        if (!response.ok) {
          throw new Error('Failed to save token');
        }
      }
    } catch (error) {
      console.error('Error saving token to server:', error);
    }
  }

  private showNotification(payload: any): void {
    const { title, body, icon } = payload.notification || {};
    
    if ('serviceWorker' in navigator && 'Notification' in window) {
      navigator.serviceWorker.ready.then((registration) => {
        registration.showNotification(title || 'KrakenSim', {
          body: body || 'You have a new notification',
          icon: icon || '/assets/icon/favicon.png',
          badge: '/assets/icon/favicon.png',
          tag: 'esim-notification',
          requireInteraction: true
        });
      });
    }
  }

  private handleNotificationReceived(notification: PushNotificationSchema): void {
    // Handle notification received in foreground
    console.log('Notification received:', notification);
  }

  private handleNotificationAction(notification: ActionPerformed): void {
    // Handle notification tap/action
    console.log('Notification action:', notification);
    
    // Navigate based on notification data
    if (notification.notification.data?.route) {
      window.location.href = notification.notification.data.route;
    }
  }

  // Update notification preferences
  async updateNotificationPreferences(enabled: boolean): Promise<void> {
    try {
      localStorage.setItem('notifications', JSON.stringify(enabled));
      
      if (!enabled) {
        // Unregister from notifications
        if (Capacitor.isNativePlatform()) {
          await PushNotifications.removeAllListeners();
        }
      } else {
        // Re-initialize notifications
        await this.initializeNotifications();
      }
    } catch (error) {
      console.error('Error updating notification preferences:', error);
    }
  }
}

export const notificationService = new NotificationService();