import * as admin from "firebase-admin";
import * as functions from 'firebase-functions';
import * as dotenv from 'dotenv';
import * as cors from 'cors';
import { ESim, ESimProfile, ESimPurchase, ESimTopUp } from "./types/eSim";

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

// CORS handler for all HTTP functions
const corsHandler = cors({ origin: true });

// Helper function to verify authentication
async function verifyAuth(req: any): Promise<string> {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Authentication required');
  }
  const idToken = authHeader.split('Bearer ')[1];
  const decodedToken = await admin.auth().verifyIdToken(idToken);
  return decodedToken.uid;
}

// Helper function to handle HTTP responses with CORS
function handleHttpResponse(req: any, res: any, handler: () => Promise<any>) {
  return corsHandler(req, res, async () => {
    try {
      const result = await handler();
      res.status(200).json(result);
    } catch (error: any) {
      console.error('HTTP function error:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });
}

class ESimPurchaseService {
  private readonly apiKey: string;
  private readonly baseUrl: string;

  constructor() {
    // Load environment variables when service is actually instantiated
    dotenv.config();
    
    this.apiKey = process.env.SMSPOOL_API_KEY || "";
    this.baseUrl = process.env.SMSPOOL_BASE_URL || "https://api.smspool.net";

    if (!this.apiKey) {
      throw new Error("SMSPOOL_API_KEY is required");
    }
  }

  async purchaseESim(plan: number): Promise<{ success: number; message: string; transactionId: string; }> {
    // Create URL-encoded form data for Node.js
    const formData = new URLSearchParams();
    formData.append("key", this.apiKey);
    formData.append("plan", String(plan));

    const response = await fetch(`${this.baseUrl}/esim/purchase`, {
      method: "POST",
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`eSIM purchase API error: ${response.status} - ${errorText}`);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('eSIM purchase result:', result);
    return result;
  }

  async getESimProfile(transactionId: string): Promise<ESimProfile> {
    const formData = new URLSearchParams();
    formData.append("key", this.apiKey);
    formData.append("transactionId", transactionId);

    try {
      const response = await fetch(`${this.baseUrl}/esim/profile`, {
        method: "POST",
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('SMSPool API error:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      }

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('Error fetching eSIM profile:', error?.message || error);
      throw error;
    }
  }

  async topUpESim(transactionId: string, plan: number): Promise<{ success: number; message: string; }> {
    const formData = new URLSearchParams();
    formData.append("key", this.apiKey);
    formData.append("transactionId", transactionId);
    formData.append("plan", String(plan));

    const response = await fetch(`${this.baseUrl}/esim/topup`, {
      method: "POST",
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString(),
    });
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`eSIM topup API error: ${response.status} - ${errorText}`);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }
    return response.json();
  }

  async deleteESim(transactionId: string): Promise<{ success: number; message: string; }> {
    const formData = new URLSearchParams();
    formData.append("key", this.apiKey);
    formData.append("transactionId", transactionId);

    const response = await fetch(`${this.baseUrl}/esim/delete`, {
      method: "POST",
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString(),
    });
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`eSIM delete API error: ${response.status} - ${errorText}`);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }
    return response.json();
  }

  async getESimHistory(start: string, length: string, search: string): Promise<{ data: ESim[]; rows: number; page: number; limit: number; }> {
    const formData = new URLSearchParams();
    formData.append("key", this.apiKey);
    formData.append("start", start);
    formData.append("length", length);
    formData.append("search", search);

    const response = await fetch(`${this.baseUrl}/esim/history`, {
      method: "POST",
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString(),
    });
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`eSIM history API error: ${response.status} - ${errorText}`);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }
    return response.json();
  }
}

let eSimPurchaseService: ESimPurchaseService;

function getESimPurchaseService(): ESimPurchaseService {
  if (!eSimPurchaseService) {
    eSimPurchaseService = new ESimPurchaseService();
  }
  return eSimPurchaseService;
}

// Export the service for use in other modules
export const eSimApiService = getESimPurchaseService();

export const purchaseESim = functions.https.onRequest((req, res) => {
  handleHttpResponse(req, res, async () => {
    const userId = await verifyAuth(req);
    const { plan, credits } = req.body;

    if (!plan || !credits) {
      throw new Error('Plan and credits are required');
    }

    const purchaseResult = await getESimPurchaseService().purchaseESim(plan);

    if (purchaseResult.success === 1) {
      const eSimPurchase: ESimPurchase = {
        userId,
        plan,
        transactionId: purchaseResult.transactionId,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      };
      await db.collection('esimPurchases').doc(purchaseResult.transactionId).set(eSimPurchase);

      // Deduct credits using the new credits system structure
      const creditsRef = db.collection('users').doc(userId).collection('credits').doc('balance');

      // Check if credits document exists, if not create it first
      const creditsDoc = await creditsRef.get();
      if (!creditsDoc.exists) {
        // Initialize user credits
        const initialCredits = {
          userId,
          totalCredits: 0,
          lifetimeCredits: 0,
          lifetimeSpent: 0,
          pendingCredits: 0,
          lastUpdated: new Date(),
          createdAt: new Date()
        };
        await creditsRef.set(initialCredits);
      }

      await creditsRef.update({
        totalCredits: admin.firestore.FieldValue.increment(-credits),
        lifetimeSpent: admin.firestore.FieldValue.increment(credits),
        lastUpdated: new Date()
      });
    }

    return purchaseResult;
  });
});

export const getESimProfile = functions.https.onCall(async (request) => {
  try {
    const { transactionId } = request.data as { transactionId: string };

    if (!transactionId) {
      throw new functions.https.HttpsError('invalid-argument', 'transactionId is required');
    }

    const profile = await getESimPurchaseService().getESimProfile(transactionId);
    return profile;
  } catch (error: any) {
    console.error('Error in getESimProfile:', error?.message || 'Unknown error');
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});

export const topUpESim = functions.https.onCall(async (data: any, context: any) => {
  try {
    // Get userId from authentication context
    const uid = context && context.auth && context.auth.uid;
    if (!uid) {
      throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }

    const { transactionId, plan, credits } = data as { transactionId: string; plan: number; credits: number };
    if (!transactionId || !plan) {
      throw new functions.https.HttpsError('invalid-argument', 'transactionId and plan are required');
    }

    const topUpResult = await getESimPurchaseService().topUpESim(transactionId, plan);

    if (topUpResult.success === 1) {
      const eSimTopUp: ESimTopUp = {
        userId: uid,
        transactionId,
        plan,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      };
      await db.collection('esimTopUps').add(eSimTopUp);
      // Deduct credits using the new credits system structure
      const creditsRef = db.collection('users').doc(uid).collection('credits').doc('balance');

      // Check if credits document exists, if not create it first
      const creditsDoc = await creditsRef.get();
      if (!creditsDoc.exists) {
        // Initialize user credits
        const initialCredits = {
          userId: uid,
          totalCredits: 0,
          lifetimeCredits: 0,
          lifetimeSpent: 0,
          pendingCredits: 0,
          lastUpdated: new Date(),
          createdAt: new Date()
        };
        await creditsRef.set(initialCredits);
      }

      await creditsRef.update({
        totalCredits: admin.firestore.FieldValue.increment(-credits),
        lifetimeSpent: admin.firestore.FieldValue.increment(credits),
        lastUpdated: new Date()
      });
    }
    return topUpResult;
  } catch (error: any) {
    console.error('Error in topUpESim (callable):', error);
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});

export const deleteESim = functions.https.onCall(async (data: any, context: any) => {
  try {
    const { transactionId } = data as { transactionId: string };
    if (!transactionId) {
      throw new functions.https.HttpsError('invalid-argument', 'transactionId is required');
    }

    const deleteResult = await getESimPurchaseService().deleteESim(transactionId);

    if (deleteResult.success === 1) {
      await db.collection('esimPurchases').doc(transactionId).update({
        status: 'deleted',
      });
    }

    return deleteResult;
  } catch (error: any) {
    console.error('Error in deleteESim (callable):', error);
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});

export const getESimHistory = functions.https.onCall(async (data: any, context: any) => {
  try {
    const { start = '', length = '20', search = '' } = data as { start?: string; length?: string; search?: string };
    const historyResult = await getESimPurchaseService().getESimHistory(start, length, search);
    return historyResult;
  } catch (error: any) {
    console.error('Error in getESimHistory (callable):', error);
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});

export const getMyESims = functions.https.onRequest((req, res) => {
  handleHttpResponse(req, res, async () => {
    const userId = await verifyAuth(req);

    try {
      // Simplified query to avoid index issues
      const snapshot = await db.collection('esimPurchases')
        .where('userId', '==', userId)
        .get();

      // Filter out deleted items in JavaScript instead of Firestore
      const esims = snapshot.docs
        .map(doc => doc.data())
        .filter(esim => esim.status !== 'deleted');

      console.log(`Found ${esims.length} eSIMs for user ${userId}`);
      return esims;
    } catch (error: any) {
      console.error('Error in getMyESims:', error);
      throw new Error(error?.message || 'Failed to fetch eSIMs');
    }
  });
});