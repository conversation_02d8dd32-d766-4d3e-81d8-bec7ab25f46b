# Credits System Implementation Guide

## 🎯 Overview

This guide walks you through completing the migration from country-specific eSIM plans to a flexible credits-based system with animal-themed packages and LemonSqueezy checkout overlay.

## ✅ What's Already Implemented

### 1. Database Schema ✅
- New Firestore collections designed and documented
- TypeScript interfaces created for all data models
- Transaction management with atomic operations

### 2. Credit Packages ✅
- 6 animal-themed packages (Minnow to Whale)
- Price range: $5-$75 with equivalent credits
- Beautiful UI with responsive design

### 3. LemonSqueezy Integration ✅
- Checkout overlay implementation with `createLemonSqueezy()`
- Custom data tracking for user and package identification
- React hooks for easy component integration

### 4. Backend Services ✅
- Complete CreditsService with purchase/redemption logic
- 8 new Cloud Functions for credits operations
- Webhook integration ready for payment confirmations

### 5. Frontend Credits Page ✅
- Modern UI with animal-themed packages
- User dashboard with balance and transaction history
- Real-time updates and responsive design

### 6. App Routing ✅
- Credits page added to navigation
- Sidebar updated with Credits link

## 🚀 Next Steps to Complete

### Step 1: Initialize Database
```bash
# Run the initialization script
node scripts/initializeCreditsSystem.js
```

This will:
- Create credit packages in Firestore
- Set up sample eSIM credit costs
- Check for existing data to avoid duplicates

### Step 2: Set Up LemonSqueezy Products

1. **Create Products in LemonSqueezy Dashboard:**
   - Go to your LemonSqueezy store
   - Create 6 products for each credit package:
     - 🐟 Minnow Credits - $5
     - 🐟 Tuna Credits - $10
     - 🐬 Dolphin Credits - $20
     - 🐙 Octopus Credits - $35
     - 🦈 Shark Credits - $50
     - 🐋 Whale Credits - $75

2. **Update Firestore with Product IDs:**
```javascript
// Update each credit package with LemonSqueezy IDs
await db.collection('credit_packages').doc('minnow').update({
  lemonSqueezyVariantId: 'YOUR_MINNOW_VARIANT_ID',
  lemonSqueezyProductId: 'YOUR_MINNOW_PRODUCT_ID',
  updatedAt: new Date()
});
// Repeat for all packages...
```

### Step 3: Configure LemonSqueezy Webhooks

1. **Set up webhook endpoint:**
   - URL: `https://your-project.cloudfunctions.net/lemonSqueezyWebhook`
   - Events: `order_created`, `order_refunded`

2. **Test webhook integration:**
   - Make a test purchase
   - Verify credits are added to user account
   - Check transaction history

### Step 4: Build and Deploy

```bash
# Build the frontend
npm run build

# Deploy Cloud Functions
cd functions
npm run deploy

# Or deploy specific functions
firebase deploy --only functions:getCreditPackages,functions:processCreditPurchase
```

### Step 5: Test Complete Flow

1. **Purchase Credits:**
   - Navigate to `/credits`
   - Select a credit package
   - Complete LemonSqueezy checkout
   - Verify credits added to account

2. **Redeem Credits:**
   - Browse available eSIMs
   - Select plan to purchase
   - Confirm credit redemption
   - Verify credits deducted

## 🔧 Configuration Files

### Environment Variables
Add to your `.env` files:

```bash
# LemonSqueezy Configuration
LEMONSQUEEZY_API_KEY=your_api_key
LEMONSQUEEZY_STORE_ID=your_store_id
LEMONSQUEEZY_WEBHOOK_SECRET=your_webhook_secret

# Firebase Configuration (already configured)
FIREBASE_PROJECT_ID=esim-numero
```

### Firebase Security Rules
Update Firestore rules to allow credits operations:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Credit packages (public read)
    match /credit_packages/{packageId} {
      allow read: if true;
      allow write: if false; // Only admin can modify
    }
    
    // User credits (user can read their own)
    match /users/{userId}/credits/{document} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // User credit transactions (user can read their own)
    match /users/{userId}/credit_transactions/{transactionId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if false; // Only backend can create
    }
    
    // eSIM credit costs (public read)
    match /esim_credit_costs/{costId} {
      allow read: if true;
      allow write: if false; // Only admin can modify
    }
    
    // Credit orders (admin only)
    match /credit_orders/{orderId} {
      allow read, write: if false; // Only backend can access
    }
  }
}
```

## 🎨 Customization Options

### Theme Integration
The Credits page automatically supports your existing theme system:
- Light/Dark mode compatibility
- Consistent color variables
- Responsive design

### Package Customization
To modify credit packages:

1. Update `CREDIT_PACKAGES` in `src/types/credits.ts`
2. Run initialization script to update database
3. Create corresponding LemonSqueezy products
4. Update variant IDs in Firestore

### Pricing Strategy
Current pricing: 1 credit ≈ $1 USD

To adjust:
1. Modify package prices in credit packages
2. Update eSIM credit costs accordingly
3. Consider bulk discounts for larger packages

## 🔍 Monitoring & Analytics

### Key Metrics to Track
- Credit purchase conversion rates
- Popular package selections
- Credit redemption patterns
- User lifetime value

### Logging
All credit operations are logged with:
- User identification
- Transaction amounts
- Success/failure status
- Error details for debugging

## 🚨 Troubleshooting

### Common Issues

1. **LemonSqueezy Checkout Not Opening:**
   - Verify `createLemonSqueezy()` is called
   - Check browser console for errors
   - Ensure variant IDs are correct

2. **Credits Not Added After Purchase:**
   - Check webhook endpoint is receiving data
   - Verify webhook secret configuration
   - Review Cloud Function logs

3. **Insufficient Credits Error:**
   - Verify user credit balance calculation
   - Check eSIM credit cost configuration
   - Ensure transaction atomicity

### Debug Commands

```bash
# Check Cloud Function logs
firebase functions:log

# Test webhook locally
curl -X POST http://localhost:5002/esim-numero/us-central1/lemonSqueezyWebhook \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Verify Firestore data
firebase firestore:get credit_packages
```

## 🎉 Success Criteria

The migration is complete when:
- ✅ Users can purchase credits via LemonSqueezy overlay
- ✅ Credits are automatically added to user accounts
- ✅ Users can redeem credits for eSIMs
- ✅ Transaction history is properly tracked
- ✅ All existing functionality still works
- ✅ Mobile and desktop experiences are smooth

## 📞 Support

For implementation support:
1. Check the troubleshooting section above
2. Review Cloud Function logs for errors
3. Test with small amounts first
4. Verify all configuration steps are complete

The credits system provides a much more flexible and user-friendly experience compared to the previous country-specific pricing model!
