.checkout-content {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  background: var(--app-surface) !important;
  color: var(--app-text-primary) !important;
}

.checkout-container {
  max-width: 800px;
  margin: 0 auto;
}

.checkout-section {
  margin-bottom: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 20px var(--app-shadow);
  background: var(--app-background) !important;
  border: 2px solid var(--app-border);
  overflow: hidden;
}

/* Cart items styling with theme support */
.cart-item {
  background: var(--app-background) !important;
  color: var(--app-text-primary) !important;
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid var(--app-border);
  padding: 12px;
}

.cart-item ion-label h2 {
  color: var(--app-text-primary) !important;
  font-weight: 600;
  margin-bottom: 4px;
}

.cart-item ion-label p {
  color: var(--app-text-secondary) !important;
  margin: 2px 0;
}

.cart-item .price {
  color: var(--ion-color-primary) !important;
  font-weight: 700;
  font-size: 1.1em;
}

.checkout-section ion-card-title {
  font-size: 22px;
  font-weight: 700;
  color: var(--app-text-primary) !important;
  margin: 0;
  letter-spacing: -0.5px;
}

.checkout-section ion-card-header {
  padding: 20px 20px 16px 20px;
  border-bottom: 2px solid var(--app-border);
  background: var(--app-surface) !important;
  margin-bottom: 0;
}

.checkout-section ion-card-content {
  padding: 20px;
  background: var(--app-background) !important;
}

.checkout-section ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 12px;
}

.checkout-section ion-input {
  --padding-start: 12px;
  --padding-end: 12px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --background: var(--app-surface);
  --color: var(--app-text-primary);
  --border-radius: 8px;
  --border: 1px solid var(--app-border);
}

.terms-checkbox {
  margin-top: 20px;
  --padding-start: 0;
  --background: var(--app-surface) !important;
  --color: var(--app-text-primary) !important;
  border-radius: 12px;
  padding: 16px;
  border: 2px solid var(--app-border);
  box-shadow: 0 2px 8px var(--app-shadow-light);
}

.terms-checkbox ion-label {
  color: var(--app-text-primary) !important;
  font-weight: 500;
}

.terms-checkbox a {
  color: var(--ion-color-primary) !important;
  text-decoration: none;
}

.terms-checkbox a:hover {
  color: var(--ion-color-primary-shade) !important;
  text-decoration: underline;
}

.terms-checkbox ion-label {
  font-size: 14px;
  color: var(--ion-color-medium);
}

.terms-checkbox a {
  color: var(--ion-color-primary);
  text-decoration: none;
}

.terms-checkbox a:hover {
  text-decoration: underline;
}

.payment-form {
  margin-top: 20px;
}

.payment-form ion-item {
  --background: var(--app-background);
  --color: var(--app-text-primary);
  --border-color: var(--app-border);
  --border-style: solid;
  --border-width: 1px;
  --border-radius: 8px;
  margin-bottom: 12px;
}

.payment-form ion-input {
  --background: var(--app-surface);
  --color: var(--app-text-primary);
  --placeholder-color: var(--app-text-tertiary);
  --padding-start: 12px;
  --padding-end: 12px;
  --padding-top: 12px;
  --padding-bottom: 12px;
}

.security-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px;
  background: var(--ion-color-success-tint);
  border-radius: 8px;
  border: 1px solid var(--ion-color-success);
}

.security-notice ion-icon {
  font-size: 20px;
}

.order-summary {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: white;
  border: 2px solid var(--ion-color-primary);
}

.order-summary ion-card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--ion-color-primary);
  margin: 0;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.summary-row:not(:last-child) {
  border-bottom: 1px solid var(--ion-color-light);
}

.summary-row.savings {
  color: var(--ion-color-success);
  font-weight: 600;
}

.summary-row.total {
  font-size: 18px;
  font-weight: 700;
  color: var(--ion-color-dark);
  padding-top: 12px;
  border-top: 2px solid var(--ion-color-primary);
  margin-top: 8px;
}

.checkout-actions {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  position: sticky;
  bottom: 0;
  background: var(--ion-background-color);
  border-top: 1px solid var(--ion-color-light);
  margin: 0 -16px -16px -16px;
  padding: 16px;
}

.back-btn {
  flex: 0 0 auto;
  --color: var(--app-text-primary) !important;
  --background: var(--app-surface) !important;
  --border: 2px solid var(--app-border) !important;
  --border-radius: 16px;
  font-weight: 600;
  height: 56px;
  min-width: 120px;
  margin-right: 16px;
}

.back-btn:hover {
  --background: var(--app-surface-variant) !important;
  --color: var(--app-text-primary) !important;
  --border: 2px solid var(--ion-color-primary) !important;
  transform: translateY(-1px);
}

.next-btn {
  flex: 1;
  --background: var(--ion-color-primary) !important;
  --color: var(--ion-color-primary-contrast) !important;
  --border-radius: 16px;
  --box-shadow: 0 6px 20px rgba(var(--ion-color-primary-rgb), 0.3);
  font-weight: 700;
  height: 56px;
  font-size: 16px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.next-btn:hover:not([disabled]) {
  --background: var(--ion-color-primary-shade) !important;
  --box-shadow: 0 8px 24px rgba(var(--ion-color-primary-rgb), 0.4);
  transform: translateY(-3px);
}

.next-btn[disabled] {
  --background: var(--app-surface-variant) !important;
  --color: var(--app-text-tertiary) !important;
  --box-shadow: none;
  opacity: 0.5;
  transform: none;
}

.confirmation {
  text-align: center;
  padding: 40px 20px;
}

.confirmation-icon {
  font-size: 80px;
  margin-bottom: 24px;
}

.confirmation h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--ion-color-success);
  margin: 0 0 16px 0;
}

.confirmation p {
  font-size: 16px;
  color: var(--ion-color-medium);
  margin: 0 0 24px 0;
  line-height: 1.5;
}

/* Segment styling */
ion-segment {
  --background: var(--ion-color-light);
  --border-radius: 12px;
  margin-bottom: 20px;
}

ion-segment-button {
  --color: var(--ion-color-medium);
  --color-checked: var(--ion-color-primary);
  --background-checked: white;
  --border-radius: 8px;
  margin: 4px;
  min-height: 60px;
}

ion-segment-button ion-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

ion-segment-button ion-label {
  font-size: 12px;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .checkout-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }
  
  .checkout-actions {
    flex-direction: column;
  }
  
  .back-btn,
  .next-btn {
    width: 100%;
  }
  
  ion-segment-button {
    min-height: 50px;
  }
  
  ion-segment-button ion-icon {
    font-size: 20px;
  }
  
  ion-segment-button ion-label {
    font-size: 11px;
  }
}

/* Animation for checkout steps */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.checkout-section {
  animation: slideInUp 0.5s ease-out;
}

.checkout-section:nth-child(2) {
  animation-delay: 0.1s;
}

.checkout-section:nth-child(3) {
  animation-delay: 0.2s;
}

/* Loading states */
.next-btn ion-spinner {
  --color: white;
}

/* Form validation styling */
ion-item.ion-invalid {
  --border-color: var(--ion-color-danger);
  --border-width: 2px;
}

ion-item.ion-valid {
  --border-color: var(--ion-color-success);
  --border-width: 2px;
}

/* Hover effects */
.checkout-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* Focus states */
ion-input:focus-within {
  --border-color: var(--ion-color-primary);
  --border-width: 2px;
}

/* Success animations */
.confirmation {
  animation: fadeInScale 0.6s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.confirmation-icon {
  animation: bounceIn 0.8s ease-out 0.2s both;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Additional styling for better contrast and readability */
.checkout-section ion-card-content {
  background: #ffffff;
  color: #333333;
}

.checkout-section ion-item {
  --background: #ffffff;
  --color: #333333;
}

.checkout-section ion-label {
  color: #333333 !important;
}

.checkout-section ion-text {
  color: #333333 !important;
}

/* Clean payment method segment styling */
.checkout-section .payment-methods {
  margin-bottom: 24px;
}

.checkout-section .payment-methods h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--app-text-primary) !important;
  margin-bottom: 12px;
  letter-spacing: 0.5px;
}

.checkout-section ion-segment {
  --background: var(--app-surface) !important;
  border-radius: 16px;
  padding: 8px;
  border: 2px solid var(--app-border);
  margin-bottom: 24px;
  box-shadow: 0 2px 12px var(--app-shadow-light);
}

.checkout-section ion-segment-button {
  --background: transparent !important;
  --background-checked: var(--ion-color-primary) !important;
  --color: var(--app-text-primary) !important;
  --color-checked: var(--ion-color-primary-contrast) !important;
  --border-radius: 12px;
  margin: 4px;
  font-weight: 600;
  min-height: 64px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.checkout-section ion-segment-button:hover:not(.segment-button-checked) {
  --background: var(--app-surface-variant) !important;
  --color: var(--app-text-primary) !important;
  border-color: var(--app-border);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--app-shadow);
}

.checkout-section ion-segment-button.segment-button-checked {
  border-color: var(--ion-color-primary);
  box-shadow: 0 0 0 3px rgba(var(--ion-color-primary-rgb), 0.2), 0 4px 16px var(--app-shadow);
  transform: translateY(-2px);
}

/* Order summary styling with better visual hierarchy */
.order-summary {
  background: var(--app-surface) !important;
  border-radius: 16px;
  padding: 24px;
  margin-top: 20px;
  border: 2px solid var(--app-border);
  box-shadow: 0 2px 12px var(--app-shadow-light);
}

.order-summary .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  color: var(--app-text-primary) !important;
  font-weight: 500;
  font-size: 16px;
}

.order-summary .summary-row.total {
  border-top: 3px solid var(--ion-color-primary);
  margin-top: 16px;
  padding-top: 16px;
  font-weight: 700 !important;
  color: var(--ion-color-primary) !important;
  font-size: 1.2em;
}

/* Payment info styling with theme support */
.payment-info {
  background: var(--ion-color-secondary-tint) !important;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  border: 1px solid var(--ion-color-secondary);
  border-left: 4px solid var(--ion-color-secondary);
}

.payment-info p {
  margin: 0;
  color: var(--app-text-primary) !important;
  line-height: 1.5;
  font-weight: 500;
}

.payment-info strong {
  color: var(--app-text-primary) !important;
}

/* Theme-aware styling for checkout page */
.checkout-content {
  --ion-background-color: var(--app-surface);
  --ion-text-color: var(--app-text-primary);
}

/* Card sections with theme support */
.checkout-section {
  background: var(--app-background) !important;
  color: var(--app-text-primary) !important;
  border: 1px solid var(--app-border);
}

.checkout-section ion-card-header {
  background: var(--app-surface-variant) !important;
  border-bottom: 1px solid var(--app-border);
}

.checkout-section ion-card-title {
  color: var(--app-text-primary) !important;
  font-weight: 600;
}

.checkout-section ion-card-content {
  background: var(--app-background) !important;
  color: var(--app-text-primary) !important;
}

/* Form inputs with better contrast */
.checkout-section ion-input,
.checkout-section ion-textarea,
.checkout-section ion-select,
.payment-form ion-input,
.payment-form ion-textarea,
.payment-form ion-select {
  --background: var(--app-background) !important;
  --color: var(--app-text-primary) !important;
  --placeholder-color: var(--app-text-tertiary) !important;
  border: 2px solid var(--app-border) !important;
  border-radius: 8px !important;
  padding: 12px !important;
  font-size: 16px !important;
}

.checkout-section ion-input:focus-within,
.checkout-section ion-textarea:focus-within,
.checkout-section ion-select:focus-within,
.payment-form ion-input:focus-within,
.payment-form ion-textarea:focus-within,
.payment-form ion-select:focus-within {
  --background: var(--app-background) !important;
  border-color: var(--ion-color-primary) !important;
  box-shadow: 0 0 0 3px rgba(var(--ion-color-primary-rgb), 0.2) !important;
  outline: none !important;
}

/* List items with theme support */
ion-item {
  --background: var(--app-background) !important;
  --color: var(--app-text-primary) !important;
  --border-color: var(--app-border) !important;
  border-bottom: 1px solid var(--app-border) !important;
}

ion-item:last-child {
  border-bottom: none !important;
}

/* Labels with theme support */
ion-label {
  --color: var(--app-text-primary) !important;
}

ion-label h2 {
  color: var(--app-text-primary) !important;
  font-weight: 600 !important;
}

ion-label p {
  color: var(--app-text-secondary) !important;
}

/* Checkbox styling with theme support */
ion-checkbox {
  --background: var(--app-background) !important;
  --border-color: var(--app-border) !important;
  --checkmark-color: var(--ion-color-primary) !important;
  --background-checked: var(--ion-color-primary) !important;
}

/* Price text with theme support */
.price,
ion-text[color="primary"] {
  color: var(--ion-color-primary) !important;
  font-weight: 600 !important;
}

/* Responsive design for mobile */
@media (max-width: 768px) {
  .checkout-section .form-row {
    flex-direction: column;
    gap: 0;
  }

  .checkout-section .form-row .form-field {
    margin-bottom: 20px;
  }

  .checkout-section ion-segment {
    padding: 6px;
  }

  .checkout-section ion-segment-button {
    min-height: 56px;
    font-size: 12px;
  }

  .checkout-section .form-field ion-item {
    --min-height: 52px !important;
    --padding-top: 14px !important;
    --padding-bottom: 14px !important;
  }

  .checkout-section .form-field label {
    font-size: 13px;
  }
}

/* Focus and hover states for better UX */
.checkout-section .form-field ion-item:hover:not(:focus-within) {
  --border-color: var(--app-text-secondary) !important;
  transform: translateY(-1px);
}

/* Improved checkbox styling */
.terms-checkbox ion-checkbox {
  --size: 20px;
  --checkbox-background-checked: var(--ion-color-primary);
  --border-color-checked: var(--ion-color-primary);
  --checkmark-color: var(--ion-color-primary-contrast);
  margin-right: 12px;
}

.terms-checkbox ion-label {
  color: var(--app-text-primary) !important;
  font-size: 14px;
  line-height: 1.4;
}

.terms-checkbox ion-label a {
  color: var(--ion-color-primary) !important;
  text-decoration: none;
  font-weight: 600;
}

.terms-checkbox ion-label a:hover {
  text-decoration: underline;
}

/* Modern form field styling */
.checkout-section .form-field {
  margin-bottom: 20px;
  position: relative;
}

.checkout-section .form-field label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--app-text-primary) !important;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.checkout-section .form-field ion-item {
  --background: var(--app-background) !important;
  --color: var(--app-text-primary) !important;
  --border-color: var(--app-border) !important;
  --border-width: 2px !important;
  --border-style: solid !important;
  --border-radius: 12px !important;
  --padding-start: 16px !important;
  --padding-end: 16px !important;
  --padding-top: 16px !important;
  --padding-bottom: 16px !important;
  --min-height: 56px !important;
  margin: 0 !important;
  box-shadow: 0 2px 8px var(--app-shadow-light) !important;
  transition: all 0.3s ease !important;
}

.checkout-section .form-field ion-item:focus-within {
  --border-color: var(--ion-color-primary) !important;
  box-shadow: 0 0 0 4px rgba(var(--ion-color-primary-rgb), 0.15), 0 4px 12px var(--app-shadow) !important;
  transform: translateY(-2px) !important;
}

/* Enhanced input field styling */
.checkout-section ion-input {
  --color: var(--app-text-primary) !important;
  --placeholder-color: var(--app-text-tertiary) !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.checkout-section ion-input input {
  color: var(--app-text-primary) !important;
  background: transparent !important;
}

/* Form row layout for side-by-side fields */
.checkout-section .form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.checkout-section .form-row .form-field {
  flex: 1;
  margin-bottom: 0;
}

/* Card number field with monospace font */
.checkout-section .card-number-field ion-input {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
  font-size: 18px !important;
  letter-spacing: 2px !important;
  font-weight: 600 !important;
}

/* Icon styling for form fields */
.checkout-section .form-field ion-icon {
  color: var(--app-text-secondary) !important;
  font-size: 20px !important;
  margin-right: 12px !important;
}

/* Security notice styling */
.security-notice {
  background: var(--ion-color-success-tint) !important;
  color: var(--app-text-primary) !important;
  border: 2px solid var(--ion-color-success) !important;
  border-radius: 12px;
  padding: 16px;
  margin-top: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 8px rgba(var(--ion-color-success-rgb), 0.2);
}

.security-notice ion-icon {
  color: var(--ion-color-success) !important;
  font-size: 24px;
  flex-shrink: 0;
}

.security-notice ion-text {
  color: var(--app-text-primary) !important;
  font-weight: 500;
}

/* Payment info sections */
.payment-info {
  background: var(--app-surface-variant) !important;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  border: 2px solid var(--app-border);
}

.payment-info ion-text {
  color: var(--app-text-primary) !important;
}

.payment-info p {
  margin: 0;
  line-height: 1.5;
}

.payment-info strong {
  color: var(--ion-color-primary) !important;
  font-weight: 700;
}
