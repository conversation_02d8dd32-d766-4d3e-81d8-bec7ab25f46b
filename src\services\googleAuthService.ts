import { GoogleAuth } from '@codetrix-studio/capacitor-google-auth';
import { Capacitor } from '@capacitor/core';
import { signInWithCredential, GoogleAuthProvider } from 'firebase/auth';
import { auth } from '../firebase';

export interface GoogleAuthResult {
  success: boolean;
  user?: any;
  error?: string;
}

export class GoogleAuthService {

  /**
   * Initialize Google Auth
   */
  static async initialize(): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      try {
        // Initialize with minimal configuration
        await GoogleAuth.initialize({
          scopes: ['profile', 'email'],
          grantOfflineAccess: true,
        });
        console.log('Google Auth initialized for native platform');
      } catch (error) {
        console.error('Google Auth initialization error:', error);
        // Continue anyway - sometimes it works even if initialization fails
      }
    } else {
      console.log('Running on web - using standard Firebase Auth');
    }
  }

  /**
   * Sign in with Google
   */
  static async signInWithGoogle(): Promise<GoogleAuthResult> {
    try {
      if (Capacitor.isNativePlatform()) {
        console.log('Starting Google sign-in...');

        // Use Capacitor Google Auth for native platforms
        const result = await GoogleAuth.signIn();

        console.log('Google Auth result:', result);

        if (result && result.authentication && result.authentication.idToken) {
          console.log('Creating Firebase credential...');

          // Create Firebase credential from Google Auth result
          const credential = GoogleAuthProvider.credential(
            result.authentication.idToken,
            result.authentication.accessToken
          );

          console.log('Signing in to Firebase...');

          // Sign in to Firebase with the credential
          const firebaseResult = await signInWithCredential(auth, credential);

          console.log('Firebase sign-in successful:', firebaseResult.user.uid);

          return {
            success: true,
            user: firebaseResult.user
          };
        } else {
          console.error('Google Auth result missing authentication data:', result);
          return {
            success: false,
            error: 'Google sign-in was cancelled or authentication data is missing'
          };
        }
      } else {
        // For web, this should not be called - use standard Firebase Auth
        return {
          success: false,
          error: 'Use standard Firebase Auth for web platforms'
        };
      }
    } catch (error: any) {
      console.error('Google sign-in error:', error);
      return {
        success: false,
        error: error.message || 'Google sign-in failed'
      };
    }
  }

  /**
   * Sign out from Google
   */
  static async signOut(): Promise<{ success: boolean; error?: string }> {
    try {
      if (Capacitor.isNativePlatform()) {
        await GoogleAuth.signOut();
      }
      return { success: true };
    } catch (error: any) {
      console.error('Google sign-out error:', error);
      return {
        success: false,
        error: error.message || 'Google sign-out failed'
      };
    }
  }

  /**
   * Refresh Google Auth token
   */
  static async refresh(): Promise<{ success: boolean; error?: string }> {
    try {
      if (Capacitor.isNativePlatform()) {
        await GoogleAuth.refresh();
      }
      return { success: true };
    } catch (error: any) {
      console.error('Google refresh error:', error);
      return {
        success: false,
        error: error.message || 'Google refresh failed'
      };
    }
  }
}
