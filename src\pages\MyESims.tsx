import React, { useEffect, useState } from 'react';
import { IonContent, IonHeader, IonPage, IonTitle, IonToolbar, IonList, IonItem, IonLabel, IonButton, IonIcon, IonRefresher, IonRefresherContent, IonSpinner, IonText, IonButtons, IonModal, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonBadge, IonProgressBar, IonChip, IonImg } from '@ionic/react';
import { eSimApiService } from '../services/eSimApiService';
import { eSimRedemptionService } from '../services/eSimRedemptionService';
import { CountryService } from '../services/countryService';
import { ESim, ESimProfile } from '../types/eSim';
import { ESimOrder } from '../types/credits';
import { eye, trash, reload, add, cellular, time, checkmarkCircle, alertCircle, closeCircle } from 'ionicons/icons';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import AppBar from '../components/AppBar';
import './MyESims.css';
import { useHistory, useLocation } from 'react-router';

const MyESims: React.FC = () => {
    const { t } = useTranslation();
    const { currentUser } = useAuth();
    const [esims, setEsims] = useState<ESim[]>([]);
    const [eSimOrders, setESimOrders] = useState<ESimOrder[]>([]);
    const [loading, setLoading] = useState(true);
    const [showProfile, setShowProfile] = useState<ESimProfile | null>(null);
    const [selectedOrder, setSelectedOrder] = useState<ESimOrder | null>(null);
    const history = useHistory();
    const location = useLocation();

    const fetchMyESims = async () => {
        try {
            setLoading(true);

            // Fetch purchased eSIMs from esimPurchases collection
            const purchasedESims = await eSimApiService.getMyESims();

            // Enhance eSIMs with profile data
            const enhancedESims = await Promise.all(
                purchasedESims.map(async (esim) => {
                    try {
                        const profile = await eSimApiService.getESimProfile(esim.transactionId);
                        return {
                            ...esim,
                            countryCode: profile.countryCode,
                            countryName: CountryService.getCountryName(profile.countryCode),
                            profileData: profile
                        };
                    } catch (error) {
                        return {
                            ...esim,
                            countryCode: esim.countryCode || 'unknown',
                            countryName: esim.name || `Plan ${esim.plan}`,
                            profileData: null
                        };
                    }
                })
            );

            setEsims(enhancedESims);

            // Fetch credit-redeemed eSIM orders
            if (currentUser) {
                const orders = await eSimRedemptionService.getUserESimOrders();
                setESimOrders(orders);
            }
        } catch (error) {
            console.error("Failed to fetch my eSIMs", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchMyESims();
    }, []);

    const handleViewProfile = async (transactionId: string) => {
        try {
            setLoading(true);
            const profile = await eSimApiService.getESimProfile(transactionId);
            setShowProfile(profile);
        } catch (error) {
            console.error("Failed to fetch eSIM profile", error);
            alert("Failed to load eSIM profile. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async (transactionId: string) => {
        try {
            await eSimApiService.deleteESim(transactionId);
            fetchMyESims();
        } catch (error) {
            console.error("Failed to delete eSIM", error);
        }
    };

    const handleTopUp = (planId: number, countryName: string) => {
        history.push(`/plans/${planId}?country=${encodeURIComponent(countryName)}`);
    }

    const handleOrderTopUp = async (order: ESimOrder) => {
        const topupCredits = 50; 
        history.push(`/credits?topup=${order.orderId}&needed=${topupCredits}`);
    }

    const handleViewOrderDetails = async (order: ESimOrder) => {
        setSelectedOrder(order);
        if (order.providerTransactionId && !order.activationCode) {
            try {
                const profile = await eSimApiService.getESimProfile(order.providerTransactionId);
                setSelectedOrder({
                    ...order,
                    activationCode: profile.activationCode,
                    metadata: {
                        ...order.metadata,
                        profileData: profile
                    }
                });
            } catch (error) {
                console.error("Failed to fetch eSIM profile for order:", error);
            }
        }
    }

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending': return time;
            case 'provisioned': return checkmarkCircle;
            case 'activated': return cellular;
            case 'expired': return alertCircle;
            case 'cancelled': return closeCircle;
            default: return checkmarkCircle;
        }
    }

    const getStatusColor = (status: string) => {
        return eSimRedemptionService.getStatusColor(status);
    }

    // Helper to get formatted date safely
    const getFormattedDate = (timestamp: any) => {
        try {
            let date: Date;
            if (timestamp instanceof Date) {
                date = timestamp;
            } else if (timestamp?._seconds) {
                date = new Date(timestamp._seconds * 1000);
            } else if (timestamp?.seconds) {
                date = new Date(timestamp.seconds * 1000);
            } else if (timestamp?.toDate) {
                date = timestamp.toDate();
            } else {
                date = new Date(timestamp);
            }
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (e) {
            return 'Unknown Date';
        }
    };

    const generateQRCodeUrl = (activationCode: string) => {
        let lpaCode = activationCode.trim();
        if (!lpaCode || lpaCode.length < 10) {
            return 'data:image/svg+xml;base64,...'; // Placeholder for invalid code
        }
        const encodedData = encodeURIComponent(lpaCode);
        return `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodedData}&ecc=M&margin=1`;
    }

    const refresh = async (e: CustomEvent) => {
        await fetchMyESims();
        e.detail.complete();
    };

    // Calculate progress bar color class
    const getProgressClass = (percentage: number) => {
        if (percentage > 90) return 'danger';
        if (percentage > 70) return 'warning';
        return 'primary';
    };

    return (
        <IonPage>
            <AppBar title={t('nav.my_esims')} />
            <IonContent className="ion-background-color">
                <IonRefresher slot="fixed" onIonRefresh={refresh}>
                    <IonRefresherContent></IonRefresherContent>
                </IonRefresher>

                {loading ? (
                    <div className="loading-container">
                        <IonSpinner name="crescent" color="primary" />
                        <IonText>{t('common.loading')}</IonText>
                    </div>
                ) : esims.length === 0 && eSimOrders.length === 0 ? (
                    <div className="empty-state">
                        <IonIcon icon={cellular} />
                        <h2>{t('my_esims.no_esims')}</h2>
                        <p>{t('my_esims.no_esims_description')}</p>
                        <IonButton 
                            mode="ios" 
                            shape="round" 
                            onClick={() => history.push('/countries')}
                            style={{ width: '200px', "--box-shadow": "0 4px 14px rgba(var(--ion-color-primary-rgb), 0.4)" }}
                        >
                            {t('my_esims.browse_plans')}
                        </IonButton>
                    </div>
                ) : (
                    <div className="content-wrapper">
                        {/* New eSIM Orders */}
                        {eSimOrders.length > 0 && (
                            <div className="esim-section">
                                <div className="section-title">
                                    <h2>{t('my_esims.section_title')}</h2>
                                </div>
                                <div className="esim-grid">
                                    {eSimOrders.map(order => {
                                        const usage = formatDataUsage(order.dataUsed, order.dataInGb);
                                        return (
                                            <div key={order.orderId} className="esim-card">
                                                <div className="esim-card-header">
                                                    <div className="esim-card-header-content">
                                                        <div>
                                                            <h3 className="esim-card-title">{order.countryName}</h3>
                                                            <div className="esim-card-subtitle">
                                                                {eSimRedemptionService.formatDataAmount(order.dataInGb)} • {eSimRedemptionService.formatDuration(order.durationDays)}
                                                            </div>
                                                        </div>
                                                        <div className="status-badge">
                                                            <IonIcon icon={getStatusIcon(order.status)} />
                                                            {order.status}
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div className="esim-card-body">
                                                    <div className="data-usage-section">
                                                        <div className="usage-header">
                                                            <span>{t('my_esims.data_usage')}</span>
                                                            <span className="usage-amount">{usage.text}</span>
                                                        </div>
                                                        <div className="usage-track">
                                                            <div 
                                                                className={`usage-fill ${getProgressClass(usage.percentage)}`} 
                                                                style={{ width: `${usage.percentage}%` }}
                                                            ></div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div className="info-grid">
                                                        <div className="info-item">
                                                            <span className="info-label">{t('my_esims.expires')}</span>
                                                            <span className="info-value">
                                                                {order.expiresAt ? getFormattedDate(order.expiresAt) : t('my_esims.not_available')}
                                                            </span>
                                                        </div>
                                                        <div className="info-item">
                                                            <span className="info-label">{t('my_esims.plan')}</span>
                                                            <span className="info-value">{eSimRedemptionService.formatDataAmount(order.dataInGb)}</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="esim-card-actions">
                                                    <IonButton 
                                                        fill="outline" 
                                                        className="action-btn"
                                                        onClick={() => handleViewOrderDetails(order)}
                                                    >
                                                        <IonIcon icon={eye} />
                                                        {t('my_esims.details')}
                                                    </IonButton>
                                                    {(order.status === 'provisioned' || order.status === 'activated') && (
                                                        <IonButton 
                                                            fill="solid" 
                                                            color="secondary"
                                                            className="action-btn"
                                                            onClick={() => handleOrderTopUp(order)}
                                                        >
                                                            <IonIcon icon={reload} />
                                                            {t('my_esims.top_up')}
                                                        </IonButton>
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        )}

                        {/* Traditional Purchased eSIMs List - kept for legacy/compatibility */}
                        {esims.length > 0 && (
                            <div className="esim-section" style={{ marginTop: '32px' }}>
                                <div className="section-title">
                                    <h2>{t('my_esims.purchased_contracts')}</h2>
                                </div>
                                <div className="esim-grid">
                                    {esims.map(esim => (
                                        <div key={esim.transactionId} className="esim-card">
                                              <div className="esim-card-header">
                                                <div className="esim-card-header-content">
                                                    <div>
                                                        <h3 className="esim-card-title">{esim.countryName || esim.name || `Plan ${esim.plan}`}</h3>
                                                        <div className="esim-card-subtitle">
                                                            ID: {esim.transactionId}
                                                        </div>
                                                    </div>
                                                    <div className="status-badge">
                                                        <IonIcon icon={checkmarkCircle} />
                                                        {t('my_esims.active')}
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div className="esim-card-body">
                                                <div className="info-grid">
                                                     <div className="info-item">
                                                        <span className="info-label">{t('my_esims.purchased')}</span>
                                                        <span className="info-value">{getFormattedDate(esim.timestamp)}</span>
                                                    </div>
                                                    <div className="info-item">
                                                        <span className="info-label">{t('my_esims.type')}</span>
                                                        <span className="info-value">{t('my_esims.one_time')}</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="esim-card-actions">
                                                <IonButton 
                                                    fill="outline" 
                                                    className="action-btn"
                                                    onClick={() => handleViewProfile(esim.transactionId)}
                                                >
                                                    <IonIcon icon={eye} />
                                                    {t('my_esims.install')}
                                                </IonButton>
                                                <IonButton 
                                                    fill="solid"
                                                    color="secondary" 
                                                    className="action-btn"
                                                    onClick={() => handleTopUp(esim.plan, esim.countryName || esim.name || `Plan ${esim.plan}`)}
                                                >
                                                    <IonIcon icon={reload} />
                                                    {t('my_esims.top_up')}
                                                </IonButton>
                                                <IonButton 
                                                    fill="clear" 
                                                    color="danger" 
                                                    className="action-btn full-width"
                                                    onClick={() => handleDelete(esim.transactionId)}
                                                >
                                                    <IonIcon icon={trash} />
                                                    {t('my_esims.remove_plan')}
                                                </IonButton>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </IonContent>

            {/* Modal - Reusing existing simple structures but with better classes could be done here. 
                For brevity and safety, I'll keep the Modal content mostly functional but clean up a bit. 
            */}
            <IonModal isOpen={!!showProfile} onDidDismiss={() => setShowProfile(null)}>
                <IonHeader>
                    <IonToolbar>
                        <IonTitle>{t('my_esims.install_esim')}</IonTitle>
                        <IonButtons slot="end">
                            <IonButton onClick={() => setShowProfile(null)}>{t('common.close')}</IonButton>
                        </IonButtons>
                    </IonToolbar>
                </IonHeader>
                <IonContent className="ion-padding">
                    {showProfile && (
                        <div className="qr-container">
                             {(showProfile.ac || showProfile.activationCode) && (
                                <>
                                    <div className="qr-wrapper">
                                        <IonImg 
                                            src={generateQRCodeUrl(showProfile.ac || showProfile.activationCode)} 
                                            className="qr-code-img"
                                        />
                                    </div>
                                    <p style={{textAlign: 'center', color: 'var(--ion-color-medium)', marginTop: '16px'}}>
                                        {t('my_esims.scan_camera')}
                                    </p>
                                    <div className="manual-code-box">
                                        <div className="code-text">{showProfile.ac || showProfile.activationCode}</div>
                                         <p style={{fontSize: '12px', color: '#999', marginTop: '8px'}}>{t('my_esims.manual_code')}</p>
                                    </div>
                                </>
                             )}
                             
                             <div style={{marginTop: '24px', width: '100%'}}>
                                <h3>{t('my_esims.details')}</h3>
                                <div className="detail-row">
                                    <span>{t('my_esims.apn')}</span>
                                    <span style={{fontWeight: 600}}>{showProfile.apn || 'saily'}</span>
                                </div>
                                <div className="detail-row">
                                    <span>{t('plans.data')}</span>
                                    <span style={{fontWeight: 600}}>{showProfile.totalData}</span>
                                </div>
                             </div>
                        </div>
                    )}
                </IonContent>
            </IonModal>

             <IonModal isOpen={!!selectedOrder} onDidDismiss={() => setSelectedOrder(null)}>
                 <IonHeader>
                        <IonToolbar>
                            <IonTitle>{t('my_esims.plan_details')}</IonTitle>
                            <IonButtons slot="end">
                                <IonButton onClick={() => setSelectedOrder(null)}>{t('common.close')}</IonButton>
                            </IonButtons>
                        </IonToolbar>
                    </IonHeader>
                    <IonContent className="ion-padding">
                        {selectedOrder && (
                            <div className="qr-container">
                                <h2>{selectedOrder.countryName}</h2>
                                <IonChip color={getStatusColor(selectedOrder.status)}>{selectedOrder.status}</IonChip>
                                
                                { (selectedOrder.activationCode || selectedOrder.metadata?.profileData?.ac) && (
                                    <>
                                        <div className="qr-wrapper" style={{marginTop: '20px'}}>
                                            <IonImg 
                                                src={generateQRCodeUrl(selectedOrder.activationCode || selectedOrder.metadata?.profileData?.ac || '')}
                                                className="qr-code-img"
                                            />
                                        </div>
                                         <div className="manual-code-box">
                                            <div className="code-text">{selectedOrder.activationCode || selectedOrder.metadata?.profileData?.ac}</div>
                                        </div>
                                    </>
                                )}

                                <div style={{width: '100%', marginTop: '24px'}}>
                                     <div className="detail-row">
                                        <span>{t('plans.data')}</span>
                                        <span style={{fontWeight: 600}}>{eSimRedemptionService.formatDataAmount(selectedOrder.dataInGb)}</span>
                                    </div>
                                      <div className="detail-row">
                                        <span>{t('my_esims.expires')}</span>
                                        <span>{selectedOrder.expiresAt ? getFormattedDate(selectedOrder.expiresAt) : t('my_esims.not_available')}</span>
                                    </div>
                                </div>
                            </div>
                        )}
                    </IonContent>
            </IonModal>
        </IonPage>
    );
};

// Helper for data usage (copied from original to ensure scope)
const formatDataUsage = (used: number, total: number) => {
    const percentage = total > 0 ? (used / total) * 100 : 0;
    return {
        percentage: Math.min(percentage, 100),
        text: `${eSimRedemptionService.formatDataAmount(used)} / ${eSimRedemptionService.formatDataAmount(total)}`
    };
}


export default MyESims;