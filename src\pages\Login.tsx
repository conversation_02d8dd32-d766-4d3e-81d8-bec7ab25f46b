import React, { useState } from 'react';
import {
  IonContent,
  IonPage,
  IonItem,
  IonLabel,
  IonInput,
  IonButton,
  IonText,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonSpinner
} from '@ionic/react';
import { logoGoogle, mail, lockClosed } from 'ionicons/icons';
import { signInWithEmailAndPassword, signInWithPopup, createUserWithEmailAndPassword } from 'firebase/auth';
import { auth } from '../firebase';
import { useHistory } from 'react-router-dom';
import { Capacitor } from '@capacitor/core';
import { CapacitorAuthService } from '../services/capacitorAuthService';
import './Auth.css';

const getFriendlyErrorMessage = (error: any) => {
  const code = error?.code || '';
  const message = error?.message || '';

  if (code === 'auth/invalid-credential' || code === 'auth/wrong-password' || code === 'auth/user-not-found') {
    return 'Invalid email or password. Please check your credentials and try again.';
  }

  if (code === 'auth/email-already-in-use') {
    return 'This email is already associated with an account. Please sign in instead.';
  }

  if (code === 'auth/weak-password') {
    return 'Password should be at least 6 characters.';
  }

  if (code === 'auth/invalid-email') {
    return 'Please enter a valid email address.';
  }

  if (code === 'auth/too-many-requests') {
    return 'Access to this account has been temporarily disabled due to many failed login attempts. Please reset your password or try again later.';
  }

  if (message.includes('Firebase:')) {
    return message.replace('Firebase: ', '').replace(/\(auth\/.*\)\.?/, '').trim();
  }

  return message || 'Authentication failed. Please try again.';
};

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const history = useHistory();

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      let result;
      if (Capacitor.isNativePlatform()) {
        if (isSignUp) {
          result = await CapacitorAuthService.createUserWithEmailAndPassword(email, password);
        } else {
          result = await CapacitorAuthService.signInWithEmailAndPassword(email, password);
        }

        if (result.success) {
          history.push('/countries');
        } else {
          setError(getFriendlyErrorMessage({ code: result.code, message: result.error }));
        }
      } else {
        if (isSignUp) {
          await createUserWithEmailAndPassword(auth, email, password);
        } else {
          await signInWithEmailAndPassword(auth, email, password);
        }
        history.push('/countries');
      }
    } catch (error: any) {
      setError(getFriendlyErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    setLoading(true);
    setError('');

    try {
      if (Capacitor.isNativePlatform()) {
        const capacitorResult = await CapacitorAuthService.signInWithGoogle();

        if (capacitorResult.success) {
          history.push('/countries');
        } else {
          setError(getFriendlyErrorMessage({ code: capacitorResult.code, message: capacitorResult.error }));
        }
      } else {
        const { googleProvider } = await import('../firebase');
        await signInWithPopup(auth, googleProvider);
        history.push('/countries');
      }
    } catch (error: any) {
      setError(getFriendlyErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  const continueAsGuest = () => {
    history.push('/countries');
  };

  return (
    <IonPage className="auth-page">
      <IonContent fullscreen className="auth-content">
        <div className="auth-wrapper auth-fade-in">
          <div className="auth-sidebar">
            <div className="sidebar-content">
              <div className="sidebar-logo">KrakenSim</div>
              <h2 className="sidebar-tagline">
                Global Connectivity <br />
                Zero Hassle.
              </h2>
              <p className="sidebar-description">
                Join thousands of travelers who use KrakenSim to stay connected in over 150 countries without the worry of roaming fees.
              </p>
            </div>
          </div>

          <div className="auth-container">
            <IonCard className="auth-card" style={{ width: '100%', maxWidth: '440px', boxShadow: 'none', background: 'transparent', border: 'none' }}>
              <IonCardHeader className="auth-header">
                <div style={{ textAlign: 'center' }}>
                  <IonCardTitle className="auth-title">
                    {isSignUp ? 'Create Account' : 'Welcome Back'}
                  </IonCardTitle>
                  <p className="auth-subtitle">
                    {isSignUp ? 'Start your journey with KrakenSim' : 'Login to manage your global connectivity'}
                  </p>
                </div>
              </IonCardHeader>

              <IonCardContent>
                <form onSubmit={handleEmailAuth} className="auth-form">
                  <IonItem className="auth-item" lines="none">
                    <IonIcon icon={mail} slot="start" />
                    <IonLabel position="stacked">Email</IonLabel>
                    <IonInput
                      type="email"
                      value={email}
                      onIonInput={(e) => setEmail(e.detail.value!)}
                      required
                      placeholder="<EMAIL>"
                    />
                  </IonItem>

                  <IonItem className="auth-item" lines="none">
                    <IonIcon icon={lockClosed} slot="start" />
                    <IonLabel position="stacked">Password</IonLabel>
                    <IonInput
                      type="password"
                      value={password}
                      onIonInput={(e) => setPassword(e.detail.value!)}
                      required
                      placeholder="••••••••"
                    />
                  </IonItem>

                  {error && (
                    <div className="auth-error">
                      {error}
                    </div>
                  )}

                  <IonButton
                    expand="block"
                    type="submit"
                    disabled={loading}
                    className="auth-button-main"
                  >
                    {loading ? <IonSpinner name="crescent" /> : (isSignUp ? 'Sign Up' : 'Sign In')}
                  </IonButton>
                </form>

                <IonButton
                  expand="block"
                  fill="clear"
                  onClick={handleGoogleAuth}
                  disabled={loading}
                  className="auth-button-google"
                >
                  <IonIcon icon={logoGoogle} />
                  Continue with Google
                </IonButton>

                <IonButton
                  expand="block"
                  fill="clear"
                  onClick={continueAsGuest}
                  className="auth-button-guest"
                >
                  Continue as Guest
                </IonButton>

                <div className="auth-footer">
                  <p>
                    <span
                      className="auth-link"
                      onClick={() => history.push('/forgot-password')}
                    >
                      Forgot Password?
                    </span>
                  </p>
                  <p>
                    {isSignUp ? 'Already have an account?' : "Don't have an account?"}{' '}
                    <span
                      className="auth-link"
                      onClick={() => history.push(isSignUp ? '/login' : '/register')}
                    >
                      {isSignUp ? 'Sign In' : 'Sign Up'}
                    </span>
                  </p>
                </div>
              </IonCardContent>
            </IonCard>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default Login;
