import React, { useState, useEffect } from 'react';
import {
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonText,
  IonSpinner,
  IonAlert,
  IonBadge,
  IonItem,
  IonLabel,
  IonList
} from '@ionic/react';
import {
  flash,
  checkmark,
  warning,
  card,
  wallet
} from 'ionicons/icons';
import { useAuth } from '../contexts/AuthContext';
import { creditsApiService } from '../services/creditsApiService';
import { UserCredits, ESimCreditCost } from '../types/credits';

interface ESimRedemptionProps {
  planId: number;
  countryCode: string;
  countryName: string;
  dataInGb: number;
  originalPrice: number;
  onRedemptionComplete?: (success: boolean, creditsSpent?: number) => void;
}

const ESimRedemption: React.FC<ESimRedemptionProps> = ({
  planId,
  countryCode,
  countryName,
  dataInGb,
  originalPrice,
  onRedemptionComplete
}) => {
  const { currentUser } = useAuth();
  
  const [userCredits, setUserCredits] = useState<UserCredits | null>(null);
  const [esimCost, setESimCost] = useState<ESimCreditCost | null>(null);
  const [loading, setLoading] = useState(true);
  const [redeeming, setRedeeming] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertHeader, setAlertHeader] = useState('');
  const [canAfford, setCanAfford] = useState(false);
  const [shortfall, setShortfall] = useState(0);

  useEffect(() => {
    loadRedemptionData();
  }, [planId, countryCode]);

  const loadRedemptionData = async () => {
    try {
      setLoading(true);
      
      const [credits, cost] = await Promise.all([
        creditsApiService.getUserCredits(),
        creditsApiService.getESimCreditCost(planId, countryCode)
      ]);

      setUserCredits(credits);
      setESimCost(cost);

      if (credits && cost) {
        const affordable = credits.totalCredits >= cost.creditCost;
        setCanAfford(affordable);
        if (!affordable) {
          setShortfall(cost.creditCost - credits.totalCredits);
        }
      }
    } catch (error) {
      console.error('Error loading redemption data:', error);
      setAlertHeader('Error');
      setAlertMessage('Failed to load redemption information. Please try again.');
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  };

  const handleRedemption = async () => {
    if (!currentUser || !esimCost || !userCredits) {
      setAlertHeader('Error');
      setAlertMessage('Missing required information for redemption.');
      setShowAlert(true);
      return;
    }

    if (!canAfford) {
      setAlertHeader('Insufficient Credits');
      setAlertMessage(`You need ${shortfall} more credits to purchase this eSIM. Please buy more credits first.`);
      setShowAlert(true);
      return;
    }

    try {
      setRedeeming(true);

      const result = await creditsApiService.redeemCreditsForESim({
        userId: currentUser.uid,
        planId,
        countryCode,
        creditsToSpend: esimCost.creditCost
      });

      if (result.success) {
        setAlertHeader('Success!');
        setAlertMessage(`Successfully redeemed ${result.creditsSpent} credits for ${countryName} ${dataInGb}GB eSIM. Your remaining balance is ${result.remainingCredits} credits.`);
        setShowAlert(true);
        
        // Update local state
        if (userCredits && result.creditsSpent) {
          setUserCredits({
            ...userCredits,
            totalCredits: userCredits.totalCredits - result.creditsSpent,
            lifetimeSpent: userCredits.lifetimeSpent + result.creditsSpent
          });
        }

        // Notify parent component
        onRedemptionComplete?.(true, result.creditsSpent);
      } else {
        setAlertHeader('Redemption Failed');
        setAlertMessage(result.error || 'Failed to redeem credits. Please try again.');
        setShowAlert(true);
        onRedemptionComplete?.(false);
      }
    } catch (error) {
      console.error('Error redeeming credits:', error);
      setAlertHeader('Error');
      setAlertMessage('An error occurred during redemption. Please try again.');
      setShowAlert(true);
      onRedemptionComplete?.(false);
    } finally {
      setRedeeming(false);
    }
  };

  if (loading) {
    return (
      <IonCard>
        <IonCardContent>
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <IonSpinner name="crescent" />
            <IonText>
              <p>Loading redemption information...</p>
            </IonText>
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  if (!esimCost) {
    return (
      <IonCard>
        <IonCardContent>
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <IonIcon icon={warning} color="warning" style={{ fontSize: '48px' }} />
            <IonText>
              <h3>Not Available</h3>
              <p>This eSIM is not available for credit redemption at the moment.</p>
            </IonText>
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  return (
    <>
      <IonCard className="esim-redemption-card">
        <IonCardHeader>
          <IonCardTitle>
            <IonIcon icon={flash} /> Redeem with Credits
          </IonCardTitle>
        </IonCardHeader>
        <IonCardContent>
          <IonList>
            <IonItem>
              <IonIcon icon={card} slot="start" color="primary" />
              <IonLabel>
                <h3>eSIM Plan</h3>
                <p>{countryName} - {dataInGb}GB</p>
              </IonLabel>
              <IonText slot="end" color="primary">
                <strong>{esimCost.creditCost} credits</strong>
              </IonText>
            </IonItem>

            <IonItem>
              <IonIcon icon={wallet} slot="start" color={canAfford ? 'success' : 'warning'} />
              <IonLabel>
                <h3>Your Balance</h3>
                <p>{userCredits?.totalCredits || 0} credits available</p>
              </IonLabel>
              {canAfford ? (
                <IonBadge color="success" slot="end">
                  <IonIcon icon={checkmark} /> Sufficient
                </IonBadge>
              ) : (
                <IonBadge color="warning" slot="end">
                  Need {shortfall} more
                </IonBadge>
              )}
            </IonItem>

            {canAfford && (
              <IonItem>
                <IonLabel>
                  <h3>After Purchase</h3>
                  <p>Remaining balance: {(userCredits?.totalCredits || 0) - esimCost.creditCost} credits</p>
                </IonLabel>
              </IonItem>
            )}
          </IonList>

          <div style={{ marginTop: '20px' }}>
            {canAfford ? (
              <IonButton
                expand="block"
                color="primary"
                onClick={handleRedemption}
                disabled={redeeming}
              >
                {redeeming ? (
                  <>
                    <IonSpinner name="crescent" />
                    <span style={{ marginLeft: '8px' }}>Redeeming...</span>
                  </>
                ) : (
                  <>
                    <IonIcon icon={flash} slot="start" />
                    Redeem {esimCost.creditCost} Credits
                  </>
                )}
              </IonButton>
            ) : (
              <IonButton
                expand="block"
                color="warning"
                fill="outline"
                routerLink="/credits"
              >
                <IonIcon icon={card} slot="start" />
                Buy {shortfall} More Credits
              </IonButton>
            )}
          </div>

          <div style={{ marginTop: '16px', textAlign: 'center' }}>
            <IonText color="medium">
              <small>
                Original price: ${originalPrice.toFixed(2)} USD
                {esimCost.creditCost < originalPrice && (
                  <span style={{ color: 'var(--ion-color-success)' }}>
                    {' '}• Save ${(originalPrice - esimCost.creditCost).toFixed(2)}!
                  </span>
                )}
              </small>
            </IonText>
          </div>
        </IonCardContent>
      </IonCard>

      <IonAlert
        isOpen={showAlert}
        onDidDismiss={() => setShowAlert(false)}
        header={alertHeader}
        message={alertMessage}
        buttons={['OK']}
      />
    </>
  );
};

export default ESimRedemption;
