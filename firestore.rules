rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Credit packages (public read, admin write only)
    match /credit_packages/{packageId} {
      allow read: if true;
      allow write: if false; // Only backend functions can modify
    }
    
    // User credits (users can read/write their own)
    match /users/{userId}/credits/{document} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // User credit transactions (users can read their own, only backend can write)
    match /users/{userId}/credit_transactions/{transactionId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if false; // Only backend functions can create transactions
    }
    
    // eSIM credit costs (public read, admin write only)
    match /esim_credit_costs/{costId} {
      allow read: if true;
      allow write: if false; // Only backend functions can modify
    }
    
    // Credit orders (backend only)
    match /credit_orders/{orderId} {
      allow read, write: if false; // Only backend functions can access
    }
    
    // eSIM purchases (users can read/write their own)
    match /esimPurchases/{purchaseId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // User profiles (users can read/write their own)
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Plans (public read)
    match /plans/{planId} {
      allow read: if true;
      allow write: if false; // Only admin can modify
    }
    
    // Countries (public read)
    match /countries/{countryId} {
      allow read: if true;
      allow write: if false; // Only admin can modify
    }
    
    // Orders (users can read/write their own)
    match /orders/{orderId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Default deny all other documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
