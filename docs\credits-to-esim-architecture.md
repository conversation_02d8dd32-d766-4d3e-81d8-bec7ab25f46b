# Credits-to-eSIM Complete Architecture & User Experience

## 🎯 Overview

This document outlines the complete user experience and backend architecture for the credits-based eSIM marketplace, from initial credit purchase through eSIM management and top-ups.

## 🗄️ Enhanced Database Schema

### Core Collections

#### 1. `users/{userId}`
```typescript
interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  createdAt: Date;
  lastLoginAt: Date;
  preferences: {
    language: string;
    currency: string;
    notifications: boolean;
  };
  metadata: {
    totalOrders: number;
    lifetimeSpent: number;
    preferredCountries: string[];
  };
}
```

#### 2. `users/{userId}/credits/balance`
```typescript
interface UserCredits {
  userId: string;
  totalCredits: number;          // Current available credits
  lifetimeCredits: number;       // Total credits ever purchased
  lifetimeSpent: number;         // Total credits ever spent
  pendingCredits: number;        // Credits from pending payments
  lastUpdated: Date;
  createdAt: Date;
}
```

#### 3. `users/{userId}/credit_transactions/{transactionId}`
```typescript
interface CreditTransaction {
  id: string;
  userId: string;
  type: 'purchase' | 'redemption' | 'refund' | 'bonus' | 'topup';
  amount: number;                // Credits added (+) or spent (-)
  
  // Purchase details
  packageId?: string;            // Credit package purchased
  stripePaymentIntentId?: string;
  stripePriceId?: string;
  
  // Redemption details
  esimOrderId?: string;          // Related eSIM order
  esimPlanId?: string;
  esimCountryCode?: string;
  
  // Top-up details
  topupOrderId?: string;
  
  platform: 'web' | 'ios' | 'android';
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 4. `users/{userId}/esim_orders/{orderId}`
```typescript
interface ESimOrder {
  orderId: string;
  userId: string;
  
  // eSIM Details
  planId: number;                // Provider plan ID
  countryCode: string;
  countryName: string;
  dataInGb: number;
  durationDays: number;
  
  // Pricing
  creditCost: number;            // Credits spent
  originalPriceUsd: number;      // Reference price
  
  // Provider Integration
  providerTransactionId?: string; // Provider's transaction ID
  activationCode?: string;
  qrCode?: string;
  
  // Status & Lifecycle
  status: 'pending' | 'provisioned' | 'activated' | 'expired' | 'cancelled';
  purchasedAt: Date;
  activatedAt?: Date;
  expiresAt?: Date;
  
  // Usage Tracking
  dataUsed: number;              // GB used
  lastUsageUpdate: Date;
  
  metadata: Record<string, any>;
}
```

#### 5. `users/{userId}/esim_topups/{topupId}`
```typescript
interface ESimTopup {
  topupId: string;
  userId: string;
  esimOrderId: string;           // Parent eSIM order
  
  // Top-up Details
  dataInGb: number;              // Additional data
  creditCost: number;            // Credits spent
  
  // Provider Integration
  providerTopupId?: string;
  
  status: 'pending' | 'completed' | 'failed';
  createdAt: Date;
  completedAt?: Date;
}
```

#### 6. `esim_pricing/{countryCode}`
```typescript
interface ESimPricing {
  countryCode: string;
  countryName: string;
  plans: Array<{
    planId: number;
    dataInGb: number;
    durationDays: number;
    creditCost: number;
    originalPriceUsd: number;
    topupOptions: Array<{
      dataInGb: number;
      creditCost: number;
    }>;
  }>;
  active: boolean;
  lastUpdated: Date;
}
```

## 🔄 Complete User Flow

### 1. Discovery & Planning
**Countries.tsx** → **Plans.tsx**
- User browses available countries
- Views data plans and credit costs
- Sees credit requirements vs. current balance

### 2. Credit Management
**Plans.tsx** → **Credits.tsx** → **Plans.tsx**
- Insufficient credits trigger redirect to Credits page
- User purchases credits via Capacitor Stripe
- Returns to Plans with updated balance

### 3. eSIM Purchase
**Plans.tsx** → **MyESims.tsx**
- User redeems credits for eSIM
- eSIM is provisioned via provider API
- User receives activation details

### 4. eSIM Management
**MyESims.tsx**
- View all purchased eSIMs
- Monitor data usage
- Purchase top-ups
- Manage activation/deactivation

## 🔧 Backend Architecture

### Stripe Webhook Flow
```
Stripe Payment → Webhook → Credit Award → User Notification
```

### eSIM Provisioning Flow
```
Credit Redemption → Provider API → eSIM Provisioning → Activation Details
```

### Real-time Updates
```
Firestore Listeners → UI Updates → User Experience
```

## 📱 Enhanced UI Components

### Credit Balance Widget
- Always visible credit balance
- Quick top-up button
- Recent transaction history

### eSIM Status Cards
- Real-time usage tracking
- Expiration warnings
- Quick top-up actions

### Smart Recommendations
- Suggest credit packages based on usage
- Recommend plans based on travel history
- Proactive top-up suggestions

## 🔐 Security & Validation

### Authentication Flow
- Firebase Auth integration
- Token validation on all endpoints
- User email/ID verification

### Transaction Integrity
- Idempotent operations
- Atomic credit deductions
- Rollback mechanisms

### Provider Integration Security
- API key management
- Request signing
- Response validation

## 📊 Analytics & Monitoring

### User Metrics
- Credit purchase patterns
- eSIM usage analytics
- Conversion funnels

### System Health
- Provider API reliability
- Payment success rates
- Error tracking

## 🚀 Implementation Priority

1. **Database Schema** - Foundation for all features
2. **Stripe Webhooks** - Real-time credit updates
3. **eSIM Redemption** - Core value proposition
4. **User Experience** - Seamless navigation
5. **Management Features** - Advanced functionality

This architecture provides a scalable, secure, and user-friendly foundation for the complete credits-to-eSIM experience.
