import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonPage,
  IonSpinner,
  IonText,
  IonCard,
  IonButton,
  IonIcon,
  IonChip,
  IonGrid,
  IonRow,
  IonCol,
  IonRippleEffect,
  IonAlert
} from '@ionic/react';
import {
  wifi,
  speedometer,
  time,
  globe,
  card
} from 'ionicons/icons';
import { useParams, useLocation, useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { apiService } from '../services/api';
import { eSimApiService } from '../services/eSimApiService';
import { useAuth } from '../contexts/AuthContext';
import { CreditsConversionService } from '../services/creditsConversionService';
import { eSimRedemptionService } from '../services/eSimRedemptionService';
import { creditsApiService } from '../services/creditsApiService';
import AppBar from '../components/AppBar';
import './Plans.css';

interface DataPlan {
  ID: number;
  extendable: number;
  dataInGb: number;
  duration: number;
  priceUsd: string;
  speed: string;
  ip: string;
  network: string; // JSON string containing operator info
}

interface NetworkProvider {
  operatorName: string;
  networkType: string;
}

const Plans: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const history = useHistory();
  const { currentUser } = useAuth();
  
  const [plans, setPlans] = useState<DataPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState<string | null>(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [hasESims, setHasESims] = useState<boolean | null>(null);
  const [checkingESims, setCheckingESims] = useState(true);

  const countryName = new URLSearchParams(location.search).get('country') || t('common.unknown');

  const parseNetworkProviders = (networkJson: string): NetworkProvider[] => {
    try {
      const parsed = JSON.parse(networkJson);
      const uniqueProviders = new Set<string>();
      const result: NetworkProvider[] = [];

      if (Array.isArray(parsed)) {
        const allNetworks = parsed.flatMap(country => country.network || []);
        for (const net of allNetworks) {
          const operatorName = net.operatorName || t('common.unknown');
          const networkType = net.networkType || t('common.unknown');
          if (!uniqueProviders.has(operatorName)) {
            uniqueProviders.add(operatorName);
            result.push({
              operatorName: operatorName,
              networkType: networkType
            });
          }
        }
        return result;
      }
      return [];
    } catch (error) {
      console.error('Error parsing network data:', error);
      return [];
    }
  };

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const data = await apiService.getDataPlans(id);
      setPlans(data || []);
    } catch (error) {
      console.error('Error fetching plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkUserESims = async () => {
    if (!currentUser) {
      setHasESims(false);
      setCheckingESims(false);
      return;
    }

    try {
      setCheckingESims(true);
      let legacyESims: any[] = [];
      let eSimOrders: any[] = [];

      try {
        legacyESims = await eSimApiService.getMyESims();
      } catch (error) {
        console.warn('Could not fetch legacy eSIMs:', error);
        legacyESims = [];
      }

      try {
        eSimOrders = await eSimRedemptionService.getUserESimOrders();
      } catch (error) {
        console.warn('Could not fetch eSIM orders:', error);
        eSimOrders = [];
      }

      const currentPlanId = parseInt(id);
      const hasLegacyESimsForPlan = legacyESims && legacyESims.length > 0 &&
        legacyESims.some(esim => esim.plan === currentPlanId);
      const hasESimOrdersForPlan = eSimOrders && eSimOrders.length > 0 &&
        eSimOrders.some(order => order.planId === currentPlanId);

      const hasESimsForThisPlan = hasLegacyESimsForPlan || hasESimOrdersForPlan;
      setHasESims(hasESimsForThisPlan);
    } catch (error) {
      console.error('Error checking user eSIMs:', error);
      setHasESims(false);
    } finally {
      setCheckingESims(false);
    }
  };

  const handlePurchaseWithCredits = async (plan: DataPlan) => {
    if (!currentUser) {
      history.push('/login');
      return;
    }

    try {
      setPurchasing(plan.ID.toString());
      const creditsNeeded = CreditsConversionService.convertUsdToCredits(plan.priceUsd);

      let creditCheck;
      try {
        creditCheck = await eSimRedemptionService.checkSufficientCredits(creditsNeeded);
      } catch (error) {
        try {
          await creditsApiService.initializeUserCredits();
          creditCheck = await eSimRedemptionService.checkSufficientCredits(creditsNeeded);
        } catch (initError) {
          history.push(`/credits?needed=${creditsNeeded}&error=initialization_failed`);
          return;
        }
      }

      if (!creditCheck.sufficient) {
        history.push(`/credits?needed=${creditsNeeded}&shortfall=${creditCheck.shortfall}&plan=${plan.ID}&country=${id}`);
        return;
      }

      const redemptionRequest = {
        planId: plan.ID,
        countryCode: id || '',
        countryName: countryName || '',
        dataInGb: plan.dataInGb,
        durationDays: plan.duration,
        creditCost: creditsNeeded,
        originalPriceUsd: parseFloat(plan.priceUsd)
      };

      const result = await eSimRedemptionService.redeemCreditsForESim(redemptionRequest);

      if (result.success) {
        setHasESims(true);
        const isFirstESim = hasESims === false;
        const successMessage = isFirstESim
          ? t('plans.congrats_first')
          : t('plans.esim_purchased_redirect');

        setAlertMessage(successMessage);
        setShowAlert(true);

        setTimeout(() => {
          if (isFirstESim) {
            history.push(`/credits?welcome=true&first_esim=${result.orderId}`);
          } else {
            history.push(`/my-esims?new=${result.orderId}&success=true`);
          }
        }, 2000);
      } else {
        const errorMsg = result.error || t('common.unknown');
        setAlertMessage(t('plans.purchase_failed', { error: errorMsg }));
        setShowAlert(true);
      }

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : t('common.unknown');
      setAlertMessage(t('plans.purchase_failed', { error: errorMsg }));
      setShowAlert(true);
    } finally {
      setPurchasing(null);
    }
  };

  const handlePurchaseEsim = async (planId: number, credits: number) => {
    if(!currentUser) {
        history.push('/login');
        return;
    }
    setPurchasing(planId.toString());
    try {
        const result = await eSimApiService.purchaseESim(planId, credits);
        if (result.success) {
            setAlertMessage(t('plans.purchase_success', { id: result.transactionId }));
            setShowAlert(true);
            checkUserESims();
        } else {
            setAlertMessage(t('plans.purchase_failed_simple'));
            setShowAlert(true);
        }
    } catch (error) {
        const errorMsg = error instanceof Error ? error.message : t('common.unknown');
        setAlertMessage(t('plans.purchase_failed', { error: errorMsg }));
        setShowAlert(true);
    } finally {
        setPurchasing(null);
    }
  };

  const getMostPopularPlan = () => {
    if (plans.length === 0) return null;
    return plans.reduce((best, current) => {
      const bestValue = best.dataInGb / parseFloat(best.priceUsd);
      const currentValue = current.dataInGb / parseFloat(current.priceUsd);
      return currentValue > bestValue ? current : best;
    });
  };

  const formatDuration = (days: number) => {
    if (days === 1) return t('plans.day', { count: 1 });
    if (days < 30) return t('plans.days', { count: days });
    if (days === 30) return t('plans.month', { count: 1 });
    return t('plans.months', { count: Math.floor(days / 30) });
  };

  useEffect(() => {
    fetchPlans();
    checkUserESims();
  }, [id, currentUser]);

  const mostPopularPlan = getMostPopularPlan();

  // --- RENDERING START ---
  return (
    <IonPage>
      <AppBar
        title={t('plans.title', { country: countryName })}
        showBackButton={true}
        backButtonHref="/countries"
        showMenuButton={false}
      />

      <IonContent fullscreen className="plans-container">
        <div className="plans-header">
          <IonText>
            <h1>{countryName}</h1>
            <p>{t('plans.subtitle')}</p>
          </IonText>
        </div>

        {/* Status Section (Required eSIM Warning or Success) */}
        <div className="status-section">
          {currentUser && hasESims === false && !checkingESims && (
            <IonCard className="status-card warning" style={{ margin: '0 16px 20px', background: 'rgba(255, 179, 0, 0.1)', border: '1px solid #ffb300' }}>
              <div className="status-content" style={{ padding: '16px', textAlign: 'center', color: '#ffd54f' }}>
                <IonIcon icon={card} style={{ fontSize: '24px', marginBottom: '8px' }} />
                <h3 style={{ margin: '0 0 4px', fontSize: '1rem', color: '#ffb300' }}>{t('plans.device_required')}</h3>
                <p style={{ fontSize: '0.85rem', margin: '0 0 12px', color: '#ffe082' }}>
                  {t('plans.device_required_desc')}
                </p>
                <IonButton
                  fill="solid"
                  color="warning"
                  expand="block"
                  size="small"
                  onClick={() => {
                    if (plans.length > 0) {
                      handlePurchaseEsim(plans[0].ID, CreditsConversionService.convertUsdToCredits(plans[0].priceUsd));
                    }
                  }}
                  disabled={plans.length === 0 || purchasing === plans[0]?.ID.toString()}
                >
                  {purchasing === plans[0]?.ID.toString() ? t('plans.processing') : t('plans.get_device_first')}
                </IonButton>
              </div>
            </IonCard>
          )}

          {currentUser && hasESims === true && (
            <IonCard style={{ margin: '0 16px 20px', background: 'rgba(52, 211, 153, 0.1)', border: '1px solid #059669' }}>
               <div style={{ padding: '12px', display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px', color: '#34d399' }}>
                <IonIcon icon={card} />
                <span style={{ fontSize: '0.9rem', fontWeight: 600 }}>{t('plans.esim_active')}</span>
              </div>
            </IonCard>
          )}
        </div>

        {/* Main Content */}
        {(loading || checkingESims) ? (
          <div className="loading-view">
            <IonSpinner name="crescent" color="primary" />
            <p style={{ marginTop: '16px' }}>{t('plans.loadingPlans')}</p>
          </div>
        ) : plans.length === 0 ? (
          <div className="empty-view">
            <IonIcon icon={globe} />
            <h2>{t('plans.noPlans')}</h2>
          </div>
        ) : (
          <IonGrid fixed>
            <IonRow>
              {plans.map((plan) => {
                const isPopular = mostPopularPlan?.ID === plan.ID;
                const creditCost = CreditsConversionService.convertUsdToCredits(plan.priceUsd);
                const formattedCredits = CreditsConversionService.formatCredits(creditCost); 

                return (
                  <IonCol size="12" sizeMd="6" sizeLg="4" key={plan.ID}>
                    <div className={`plan-card ${isPopular ? 'popular' : ''}`}>
                      <IonRippleEffect />
                      
                      {isPopular && <div className="popular-badge">{t('plans.best_value')}</div>}

                      {/* Card Header: Data & Price */}
                      <div className="plan-header">
                        <div className="data-amount">
                          {plan.dataInGb}<span className="data-unit">GB</span>
                        </div>
                        <div className="plan-credits-pill">
                          <span className="credits-text">
                              {formattedCredits} {String(formattedCredits).toLowerCase().includes('credit') ? '' : 'credits'}
                          </span>
                          {/* <span className="usd-text">~${plan.priceUsd}</span> */}
                        </div>
                      </div>

                      {/* Card Content: Features & Button */}
                      <div className="plan-content">
                        <div className="features-grid">
                          <div className="feature-row">
                            <div className="feature-icon-wrapper"><IonIcon icon={time} /></div>
                            <span className="feature-label">{t('plans.duration')}</span>
                            <span className="feature-value">{formatDuration(plan.duration)}</span>
                          </div>

                          <div className="feature-row">
                            <div className="feature-icon-wrapper"><IonIcon icon={speedometer} /></div>
                            <span className="feature-label">{t('plans.speed')}</span>
                            <span className="feature-value">{plan.speed}</span>
                          </div>

                          <div className="feature-row">
                            <div className="feature-icon-wrapper"><IonIcon icon={globe} /></div>
                            <span className="feature-label">{t('plans.location')}</span>
                            <span className="feature-value">{plan.ip}</span>
                          </div>
                          
                          <div className="feature-row">
                            <div className="feature-icon-wrapper"><IonIcon icon={wifi} /></div>
                            <span className="feature-label">{t('plans.network')}</span>
                            <span className="feature-value" style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', maxWidth: '80px' }}>
                                {parseNetworkProviders(plan.network)[0]?.operatorName || 'Multi'}
                            </span>
                          </div>
                        </div>

                        {/* Action Button */}
                        <div className="plan-actions">
                          {!currentUser ? (
                            <IonButton
                              className="action-btn btn-purchase"
                              expand="block"
                              onClick={() => history.push('/login')}
                            >
                              {t('plans.requires_login')}
                            </IonButton>
                          ) : hasESims === false ? (
                            <IonButton
                              className="action-btn btn-locked"
                              fill="clear" 
                              expand="block"
                              disabled={true}
                            >
                              {t('plans.device_required')}
                            </IonButton>
                          ) : (
                            <IonButton
                              className="action-btn btn-purchase"
                              expand="block"
                              onClick={() => handlePurchaseWithCredits(plan)}
                              disabled={purchasing === plan.ID.toString()}
                            >
                              {purchasing === plan.ID.toString() ? (
                                <IonSpinner name="dots" />
                              ) : (
                                t('plans.purchase')
                              )}
                            </IonButton>
                          )}
                        </div>
                      </div>
                    </div>
                  </IonCol>
                );
              })}
            </IonRow>
          </IonGrid>
        )}

        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header={t('plans.esim_purchase_title')}
          message={alertMessage}
          buttons={['OK']}
        />
      </IonContent>
    </IonPage>
  );
};

export default Plans;