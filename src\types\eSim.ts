export interface ESim {
    id: string;
    userId: string;
    transactionId: string;
    countryCode: string;
    cost: string;
    plan: number;
    name: string;
    timestamp: string;
    expiration: string;
    dataInGb: number;
    status: number;
    label: string;
    // Enhanced fields from profile data
    countryName?: string;
    profileData?: ESimProfile | null;
}

export interface ESimProfile {
    activated: number;
    ac: string; // LPA activation code string
    success: number;
    pin: string;
    topup: number;
    apn: string;
    puk: string;
    smdp: string;
    activationCode: string;
    countryCode: string;
    transactionId: string;
    plan: number;
    label: string | null;
    remainingData: string;
    totalData: string;
}