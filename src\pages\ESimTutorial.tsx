import React from 'react';
import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonButton,
  IonText,
  IonList,
  IonItem,
  IonLabel,
  IonAccordion,
  IonAccordionGroup
} from '@ionic/react';
import {
  phonePortrait,
  qrCode,
  settings,
  cellular,
  checkmarkCircle,
  informationCircle,
  warning,
  globe
} from 'ionicons/icons';
import { useTranslation } from 'react-i18next';
import AppBar from '../components/AppBar';
import './ESimTutorial.css';

const ESimTutorial: React.FC = () => {
  const { t } = useTranslation();

  return (
    <IonPage>
      <AppBar
        title={t('tutorial.title')}
        showBackButton={true}
        backButtonHref="/settings"
        showMenuButton={false}
      />
      
      <IonContent>
        {/* Introduction */}
        <IonCard className="intro-card">
          <IonCardHeader>
            <IonCardTitle>
              <IonIcon icon={informationCircle} /> {t('tutorial.intro.title')}
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <p>
              {t('tutorial.intro.description')}
            </p>
          </IonCardContent>
        </IonCard>

        {/* Step-by-step Guide */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>
              <IonIcon icon={phonePortrait} /> {t('tutorial.steps.title')}
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonAccordionGroup>
              {/* Step 1: Check Compatibility */}
              <IonAccordion value="compatibility">
                <IonItem slot="header">
                  <IonIcon icon={checkmarkCircle} slot="start" color="success" />
                  <IonLabel>
                    <h2>{t('tutorial.steps.compatibility.title')}</h2>
                    <p>{t('tutorial.steps.compatibility.subtitle')}</p>
                  </IonLabel>
                </IonItem>
                <div className="ion-padding" slot="content">
                  <h3>{t('tutorial.steps.compatibility.listTitle')}</h3>
                  <ul>
                    <li><strong>iPhone:</strong> {t('tutorial.steps.compatibility.iphone')}</li>
                    <li><strong>Samsung:</strong> {t('tutorial.steps.compatibility.samsung')}</li>
                    <li><strong>Google:</strong> {t('tutorial.steps.compatibility.google')}</li>
                    <li><strong>Other:</strong> {t('tutorial.steps.compatibility.other')}</li>
                  </ul>
                  <p><strong>{t('tutorial.steps.compatibility.check')}</strong></p>
                </div>
              </IonAccordion>

              {/* Step 2: Purchase eSIM */}
              <IonAccordion value="purchase">
                <IonItem slot="header">
                  <IonIcon icon={globe} slot="start" color="primary" />
                  <IonLabel>
                    <h2>{t('tutorial.steps.purchase.title')}</h2>
                    <p>{t('tutorial.steps.purchase.subtitle')}</p>
                  </IonLabel>
                </IonItem>
                <div className="ion-padding" slot="content">
                  <ol>
                    <li>{t('tutorial.steps.purchase.step1')}</li>
                    <li>{t('tutorial.steps.purchase.step2')}</li>
                    <li>{t('tutorial.steps.purchase.step3')}</li>
                    <li>{t('tutorial.steps.purchase.step4')}</li>
                    <li>{t('tutorial.steps.purchase.step5')}</li>
                  </ol>
                </div>
              </IonAccordion>

              {/* Step 3: Install eSIM */}
              <IonAccordion value="install">
                <IonItem slot="header">
                  <IonIcon icon={qrCode} slot="start" color="warning" />
                  <IonLabel>
                    <h2>{t('tutorial.steps.install.title')}</h2>
                    <p>{t('tutorial.steps.install.subtitle')}</p>
                  </IonLabel>
                </IonItem>
                <div className="ion-padding" slot="content">
                  <h3>{t('tutorial.steps.install.iphoneTitle')}</h3>
                  <ol>
                    <li>{t('tutorial.steps.install.iphoneStep1')}</li>
                    <li>{t('tutorial.steps.install.iphoneStep2')}</li>
                    <li>{t('tutorial.steps.install.iphoneStep3')}</li>
                    <li>{t('tutorial.steps.install.iphoneStep4')}</li>
                  </ol>
                  
                  <h3>{t('tutorial.steps.install.androidTitle')}</h3>
                  <ol>
                    <li>{t('tutorial.steps.install.androidStep1')}</li>
                    <li>{t('tutorial.steps.install.androidStep2')}</li>
                    <li>{t('tutorial.steps.install.androidStep3')}</li>
                    <li>{t('tutorial.steps.install.androidStep4')}</li>
                  </ol>
                </div>
              </IonAccordion>

              {/* Step 4: Configure */}
              <IonAccordion value="configure">
                <IonItem slot="header">
                  <IonIcon icon={settings} slot="start" color="secondary" />
                  <IonLabel>
                    <h2>{t('tutorial.steps.configure.title')}</h2>
                    <p>{t('tutorial.steps.configure.subtitle')}</p>
                  </IonLabel>
                </IonItem>
                <div className="ion-padding" slot="content">
                  <h3>{t('tutorial.steps.configure.listTitle')}</h3>
                  <ul>
                    <li>{t('tutorial.steps.configure.step1')}</li>
                    <li>{t('tutorial.steps.configure.step2')}</li>
                    <li>{t('tutorial.steps.configure.step3')}</li>
                    <li>{t('tutorial.steps.configure.step4')}</li>
                  </ul>
                  
                  <div className="warning-box">
                    <IonIcon icon={warning} color="warning" />
                    <p>{t('tutorial.steps.configure.warning')}</p>
                  </div>
                </div>
              </IonAccordion>

              {/* Step 5: Activate */}
              <IonAccordion value="activate">
                <IonItem slot="header">
                  <IonIcon icon={cellular} slot="start" color="success" />
                  <IonLabel>
                    <h2>{t('tutorial.steps.activate.title')}</h2>
                    <p>{t('tutorial.steps.activate.subtitle')}</p>
                  </IonLabel>
                </IonItem>
                <div className="ion-padding" slot="content">
                  <ol>
                    <li>{t('tutorial.steps.activate.step1')}</li>
                    <li>{t('tutorial.steps.activate.step2')}</li>
                    <li>{t('tutorial.steps.activate.step3')}</li>
                    <li>{t('tutorial.steps.activate.step4')}</li>
                    <li>{t('tutorial.steps.activate.step5')}</li>
                  </ol>
                  
                  <h3>{t('tutorial.steps.activate.troubleshootingTitle')}</h3>
                  <ul>
                    <li>{t('tutorial.steps.activate.trouble1')}</li>
                    <li>{t('tutorial.steps.activate.trouble2')}</li>
                    <li>{t('tutorial.steps.activate.trouble3')}</li>
                  </ul>
                </div>
              </IonAccordion>
            </IonAccordionGroup>
          </IonCardContent>
        </IonCard>

        {/* Tips & Best Practices */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>
              <IonIcon icon={checkmarkCircle} /> {t('tutorial.tips.title')}
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonList>
              <IonItem>
                <IonLabel>
                  <h3>{t('tutorial.tips.tip1Title')}</h3>
                  <p>{t('tutorial.tips.tip1Desc')}</p>
                </IonLabel>
              </IonItem>
              
              <IonItem>
                <IonLabel>
                  <h3>{t('tutorial.tips.tip2Title')}</h3>
                  <p>{t('tutorial.tips.tip2Desc')}</p>
                </IonLabel>
              </IonItem>
              
              <IonItem>
                <IonLabel>
                  <h3>{t('tutorial.tips.tip3Title')}</h3>
                  <p>{t('tutorial.tips.tip3Desc')}</p>
                </IonLabel>
              </IonItem>
              
              <IonItem>
                <IonLabel>
                  <h3>{t('tutorial.tips.tip4Title')}</h3>
                  <p>{t('tutorial.tips.tip4Desc')}</p>
                </IonLabel>
              </IonItem>
            </IonList>
          </IonCardContent>
        </IonCard>

        {/* Support */}
        <IonCard>
          <IonCardContent>
            <IonText color="medium">
              <p className="text-center">
                {t('tutorial.support.text')}
              </p>
            </IonText>
            <div className="text-center">
              <IonButton fill="outline" routerLink="/settings">
                <IonIcon icon={settings} slot="start" />
                {t('tutorial.support.button')}
              </IonButton>
            </div>
          </IonCardContent>
        </IonCard>
      </IonContent>
    </IonPage>
  );
};

export default ESimTutorial;
