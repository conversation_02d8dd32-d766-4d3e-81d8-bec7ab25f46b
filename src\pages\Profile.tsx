import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonAvatar,
  IonItem,
  IonLabel,
  IonInput,
  IonButton,
  IonIcon,
  IonList,
  IonText,
  IonSpinner,
  IonAlert,
  IonButtons,
  IonMenuButton
} from '@ionic/react';
import { person, mail, calendar, save, camera } from 'ionicons/icons';
import { useAuth } from '../contexts/AuthContext';
import { updateProfile, User } from 'firebase/auth';
import { AuthUser, CapacitorAuthService } from '../services/capacitorAuthService';
import { useTranslation } from 'react-i18next';
import { apiService } from '../services/api';
import './Profile.css';

interface Order {
  id: string;
  planName: string;
  country: string;
  amount: string;
  date: string;
  status: string;
}

interface OrdersResponse {
  orders: Order[];
}

const Profile: React.FC = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const [displayName, setDisplayName] = useState('');
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<Order[]>([]);
  const [ordersLoading, setOrdersLoading] = useState(true);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    if (currentUser) {
      setDisplayName(currentUser.displayName || '');
      fetchOrders();
    }
  }, [currentUser]);

  const fetchOrders = async () => {
    try {
      setOrdersLoading(true);

      if (!currentUser) {
        setOrders([]);
        return;
      }

      // Use the same service as MyESims page
      let idToken: string | null;
      if ('getIdToken' in currentUser) {
        idToken = await currentUser.getIdToken();
      } else {
        idToken = await CapacitorAuthService.getIdToken();
      }

      if (!idToken) {
        console.error('Failed to fetch orders: No ID token available.');
        setOrders([]);
        setOrdersLoading(false);
        return;
      }
      const response = await fetch('https://us-central1-esim-numero.cloudfunctions.net/getMyESims', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        },
        body: JSON.stringify({})
      });

      if (response.ok) {
        const esims = await response.json();
        // Convert eSIM purchases to order format
        const orderData = esims.map((esim: any) => ({
          id: esim.transactionId,
          planName: `eSIM Plan ${esim.plan}`,
          country: 'Various', // You might want to add country info to esimPurchases
          amount: 'N/A', // You might want to add amount info to esimPurchases
          date: esim.timestamp,
          status: 'Active'
        }));
        setOrders(orderData);
      } else {
        console.error('Failed to fetch orders:', response.status, response.statusText);
        setOrders([]);
      }
    } catch (error) {
      console.error('Failed to fetch orders:', error);
      setOrders([]);
    } finally {
      setOrdersLoading(false);
    }
  };

  const handleUpdateProfile = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      // Only update profile if it's a Firebase User (not AuthUser from Capacitor)
      if ('metadata' in currentUser) {
        await updateProfile(currentUser as User, {
          displayName: displayName
        });
      }
      setAlertMessage(t('profile.profileUpdated'));
      setShowAlert(true);
    } catch (error) {
      setAlertMessage(t('profile.profileUpdateFailed'));
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (!currentUser) {
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonButtons slot="start">
              <IonMenuButton />
            </IonButtons>
            <IonTitle>Profile</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent className="ion-padding">
          <IonText>
            <p>{t('profile.loginRequired')}</p>
          </IonText>
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonMenuButton />
          </IonButtons>
          <IonTitle>Profile</IonTitle>
        </IonToolbar>
      </IonHeader>
      
      <IonContent>
        <div className="content-wrapper">
          {/* Profile Information Card */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>{t('profile.profileInformation')}</IonCardTitle>
          </IonCardHeader>
          
          <IonCardContent>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>
              <IonAvatar style={{ width: '80px', height: '80px', marginRight: '20px' }}>
                <img src={currentUser.photoURL || '/assets/icon/default-avatar.svg'} alt="Profile" />
              </IonAvatar>
              
            </div>

            <IonItem>
              <IonIcon icon={person} slot="start" />
              <IonLabel position="stacked">{t('profile.displayName')}</IonLabel>
              <IonInput
                value={displayName}
                onIonInput={(e) => setDisplayName(e.detail.value!)}
                placeholder={t('profile.enterName')}
              />
            </IonItem>

            <IonItem>
              <IonIcon icon={mail} slot="start" />
              <IonLabel position="stacked">{t('profile.email')}</IonLabel>
              <IonInput value={currentUser.email} readonly />
            </IonItem>

            <IonItem>
              <IonIcon icon={calendar} slot="start" />
              <IonLabel position="stacked">{t('profile.memberSince')}</IonLabel>
              <IonInput
                value={
                  'metadata' in currentUser && currentUser.metadata?.creationTime
                    ? formatDate(currentUser.metadata.creationTime)
                    : t('profile.unknown')
                }
                readonly
              />
            </IonItem>

            <IonButton
              expand="block"
              onClick={handleUpdateProfile}
              disabled={loading}
              style={{ marginTop: '20px' }}
            >
              {loading ? <IonSpinner /> : <><IonIcon icon={save} slot="start" />{t('profile.updateProfile')}</>}
            </IonButton>
          </IonCardContent>
        </IonCard>

        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header={t('profile.profileUpdate')}
          message={alertMessage}
          buttons={['OK']}
        />
        </div>
      </IonContent>
    </IonPage>
  );
};

export default Profile;