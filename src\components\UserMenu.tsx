import React from 'react';
import {
  IonButton,
  IonIcon,
  IonPopover,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonAvatar
} from '@ionic/react';
import { person, logOut } from 'ionicons/icons';
import { useAuth } from '../contexts/AuthContext';
import { useHistory } from 'react-router-dom';

const UserMenu: React.FC = () => {
  const { currentUser, logout } = useAuth();
  const history = useHistory();

  const handleLogout = async () => {
    try {
      await logout();
      history.push('/login');
    } catch (error) {
      console.error('Failed to log out', error);
    }
  };

  const handleLogin = () => {
    history.push('/login');
  };

  if (!currentUser) {
    return (
      <IonButton fill="clear" onClick={handleLogin}>
        <IonIcon icon={person} />
      </IonButton>
    );
  }

  return (
    <>
      <IonButton id="user-menu-trigger" fill="clear">
        <IonAvatar style={{ width: '32px', height: '32px' }}>
          <img src={currentUser.photoURL || '/assets/icon/default-avatar.svg'} alt="User" />
        </IonAvatar>
      </IonButton>
      
      <IonPopover trigger="user-menu-trigger" triggerAction="click">
        <IonContent>
          <IonList>
            <IonItem>
              <IonLabel>
                <h2>{currentUser.displayName || 'User'}</h2>
                <p>{currentUser.email}</p>
              </IonLabel>
            </IonItem>
            <IonItem button onClick={handleLogout}>
              <IonIcon icon={logOut} slot="start" />
              <IonLabel>Logout</IonLabel>
            </IonItem>
          </IonList>
        </IonContent>
      </IonPopover>
    </>
  );
};

export default UserMenu;