/* Plans.css */

/* --- Page Container --- */
.plans-container {
  --background: #000000; /* True black for better contrast with cards */
  --padding-top: 10px;
  --padding-bottom: 80px;
}

.plans-header {
  text-align: center;
  margin-bottom: 20px;
  padding: 0 16px;
}

.plans-header h1 {
  font-size: 1.8rem;
  font-weight: 800;
  margin-bottom: 4px;
  color: #ffffff;
}

.plans-header p {
  color: #888888;
  font-size: 0.9rem;
  margin: 0;
}

/* --- The Card --- */
.plan-card {
  margin: 0 8px 16px 8px; /* Tighter margins */
  border-radius: 20px;
  /* Darker, blended background */
  background: linear-gradient(145deg, #1a1a1a 0%, #111111 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.6);
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: visible;
}

/* Hover Effect (Desktop only) */
@media (min-width: 769px) {
  .plan-card:hover {
    transform: translateY(-4px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.8);
  }
}

/* --- Most Popular Styling --- */
.plan-card.popular {
  border: 1px solid rgba(56, 128, 255, 0.3);
  background: linear-gradient(145deg, rgba(56, 128, 255, 0.08) 0%, #0d0d0d 100%);
}

.popular-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: #3880ff;
  color: white;
  padding: 2px 12px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(56, 128, 255, 0.3);
  z-index: 10;
}

/* --- Card Header (Data & Price) --- */
.plan-header {
  padding: 20px 16px 10px; /* Reduced top/bottom padding */
  text-align: center;
}

.data-amount {
  font-size: 3rem; /* Slightly smaller */
  font-weight: 800;
  line-height: 1;
  color: #ffffff;
  letter-spacing: -1px;
}

.data-unit {
  font-size: 1.2rem;
  font-weight: 600;
  color: #888888;
  margin-left: 2px;
}

.plan-credits-pill {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.06);
  padding: 4px 12px;
  border-radius: 50px;
  margin-top: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.credits-text {
  color: #2dd4bf; 
  font-weight: 700;
  font-size: 0.95rem;
}

.usd-text {
  color: #666;
  font-size: 0.75rem;
  margin-left: 8px;
  padding-left: 8px;
  border-left: 1px solid #333;
}

/* --- Features Grid --- */
.plan-content {
  padding: 12px 16px 20px; /* Tighter padding */
  flex: 1;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px; /* Reduced gap */
  margin-bottom: 20px;
}

.feature-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 4px;
}

.feature-icon-wrapper {
  /* Smaller icons */
  background: rgba(255, 255, 255, 0.04);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
  color: #a5b4fc;
  font-size: 16px; /* Icon font size */
}

.feature-label {
  font-size: 0.65rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 1px;
}

.feature-value {
  font-size: 0.85rem;
  font-weight: 600;
  color: #e0e0e0;
}

/* --- Buttons --- */
.plan-actions {
  margin-top: auto;
}

.action-btn {
  --border-radius: 12px;
  height: 44px; /* Compact button height */
  font-weight: 600;
  font-size: 0.95rem;
  margin: 0;
  width: 100%;
  letter-spacing: 0.3px;
}

.btn-purchase {
  --background: #3880ff;
  --background-hover: #3171e0;
  --box-shadow: 0 4px 12px rgba(56, 128, 255, 0.25);
}

.btn-locked {
  --background: rgba(255, 255, 255, 0.05);
  --color: #666;
  --border-color: rgba(255, 255, 255, 0.1);
  --border-width: 1px;
  --border-style: solid;
  opacity: 1; /* Override default disabled opacity */
}

/* --- Loading & Empty --- */
.loading-view, .empty-view {
  min-height: 40vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
}