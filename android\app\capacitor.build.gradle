// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-stripe')
    implementation project(':capacitor-firebase-app')
    implementation project(':capacitor-firebase-authentication')
    implementation project(':capacitor-app')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-push-notifications')
    implementation project(':capacitor-status-bar')
    implementation project(':codetrix-studio-capacitor-google-auth')
    implementation "com.android.billingclient:billing:7.1.1"
}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
