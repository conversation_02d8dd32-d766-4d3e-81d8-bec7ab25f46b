// eSIM Plans Response (from /esim/pricing endpoint)
export interface ESimPlansResponse {
    data: ESimPlan[];
    rows: number;
}

export interface ESimPlan {
    ID: number;
    speed: string;
    network: string; // JSON string
    countryCode: string;
    price: string;
    originalPrice?: string; // Cost price
    dataInGb: number;
    extendable: number;
    name: string;
    priceUsd?: string; // Calculated price
}

// Data Plans Response (from /esim/topup_plans endpoint)
export interface DataPlansResponse extends Array<DataPlan> {}

export interface DataPlan {
    ID: number;
    extendable: number;
    dataInGb: number;
    network: string; // JSON string
    duration: number;
    price: string;
    speed: string;
    ip: string;
    originalPrice?: string; // Cost price
    priceUsd?: string; // Calculated price
}

// Shared network data structures
export interface NetworkProvider {
    operatorName: string;
    networkType: string;
}

export interface CountryNetwork {
    country: string;
    network: NetworkProvider[];
}

// Parsed versions with network data as objects
export interface ParsedESimPlan extends Omit<ESimPlan, 'networkProviders'> {
    networkProviders: CountryNetwork[];
}

export interface ParsedDataPlan extends Omit<DataPlan, 'networkProviders'> {
    networkProviders: CountryNetwork[];
}


