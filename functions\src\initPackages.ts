import * as functions from 'firebase-functions';
import * as cors from 'cors';
// import { initializeCreditPackages } from './initializeCreditPackages';

const corsHandler = cors({ origin: true });

export const initPackages = functions.https.onRequest((req, res) => {
  corsHandler(req, res, async () => {
    try {
      // await initializeCreditPackages();
      res.status(200).json({
        success: true,
        message: 'Credit packages initialized successfully'
      });
    } catch (error) {
      console.error('Error initializing credit packages:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to initialize credit packages'
      });
    }
  });
});