import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonBackButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonButton,
  IonIcon,
  IonText,
  IonList,
  IonItem,
  IonLabel,
  IonInput,

  IonCheckbox,
  IonSegment,
  IonSegmentButton,
  IonSpinner,
  IonAlert
} from '@ionic/react';
import {
  card,
  logoApple,
  logoGoogle,
  shield,
  checkmarkCircle,
  lockClosed,
  mail,
  person,
  call
} from 'ionicons/icons';
import { useCart } from '../contexts/CartContext';
import { CartService } from '../services/cartService';
import { useAuth } from '../contexts/AuthContext';
import { useHistory } from 'react-router-dom';
import { inAppPurchaseService, InAppPurchaseService } from '../services/inAppPurchaseService';
// LemonSqueezy service removed - using Stripe instead
import { apiService } from '../services/api';
import { Capacitor } from '@capacitor/core';
import './Checkout.css';

type PaymentMethod = 'card' | 'apple_pay' | 'google_pay' | 'in_app_purchase' | 'web_purchase';

interface CustomerInfo {
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
}

interface PaymentInfo {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
}

const Checkout: React.FC = () => {
  const { state, clearCart } = useCart();
  const { currentUser } = useAuth();
  const history = useHistory();
  
  const [currentStep, setCurrentStep] = useState<'review' | 'payment' | 'confirmation'>('review');
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('card');
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    email: currentUser?.email || '',
    firstName: '',
    lastName: '',
    phone: ''
  });
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo>({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: ''
  });
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [inAppPurchaseAvailable, setInAppPurchaseAvailable] = useState(false);
  const [webPurchaseAvailable, setWebPurchaseAvailable] = useState(false);

  const summary = CartService.calculateCartSummary(state.items);

  useEffect(() => {
    if (state.items.length === 0) {
      history.push('/countries');
    }
  }, [state.items, history]);

  useEffect(() => {
    // Initialize payment methods based on platform
    const initializePaymentMethods = async () => {
      if (Capacitor.isNativePlatform()) {
        // Initialize in-app purchases for native platforms
        const productIds = state.items.map(item =>
          InAppPurchaseService.createProductIdentifier(item.planId, item.countryCode)
        );

        const initialized = await inAppPurchaseService.initialize(productIds);
        setInAppPurchaseAvailable(initialized);
        if (initialized) {
          setPaymentMethod('in_app_purchase');
        }
      } else {
        // Web purchases now use credits system instead
        const webInitialized = false; // Disabled - use credits system
        setWebPurchaseAvailable(webInitialized);
        if (webInitialized) {
          setPaymentMethod('web_purchase');
        }
      }
    };

    initializePaymentMethods();
  }, [currentUser, state.items]);

  const handleCustomerInfoChange = (field: keyof CustomerInfo, value: string) => {
    setCustomerInfo(prev => ({ ...prev, [field]: value }));
  };

  const handlePaymentInfoChange = (field: keyof PaymentInfo, value: string) => {
    setPaymentInfo(prev => ({ ...prev, [field]: value }));
  };

  const validateCustomerInfo = (): boolean => {
    const { email, firstName, lastName } = customerInfo;
    return !!(email && firstName && lastName && acceptTerms);
  };

  const validatePaymentInfo = (): boolean => {
    if (paymentMethod === 'card') {
      const { cardNumber, expiryDate, cvv, cardholderName } = paymentInfo;
      return !!(cardNumber && expiryDate && cvv && cardholderName);
    }
    if (paymentMethod === 'in_app_purchase') {
      return inAppPurchaseAvailable;
    }
    if (paymentMethod === 'web_purchase') {
      return webPurchaseAvailable;
    }
    return true; // Other payment methods don't need validation here
  };

  const handleNextStep = () => {
    if (currentStep === 'review' && validateCustomerInfo()) {
      setCurrentStep('payment');
    } else if (currentStep === 'payment' && validatePaymentInfo()) {
      handleProcessPayment();
    }
  };

  const handleProcessPayment = async () => {
    setProcessing(true);

    try {
      if (paymentMethod === 'in_app_purchase' && inAppPurchaseAvailable) {
        // Process in-app purchase
        let allPurchasesSuccessful = true;
        const failedPurchases: string[] = [];

        for (const item of state.items) {
          const productId = InAppPurchaseService.createProductIdentifier(item.planId, item.countryCode);
          const purchaseResult = await inAppPurchaseService.purchaseProduct(productId);

          if (!purchaseResult.success) {
            allPurchasesSuccessful = false;
            failedPurchases.push(`${item.countryName} ${item.dataInGb}GB plan`);
            continue;
          }

          // Validate purchase with backend
          try {
            const platform = Capacitor.getPlatform() === 'ios' ? 'ios' : 'android';
            if (platform === 'ios') {
              await apiService.validateApplePurchase(
                purchaseResult.transactionId!,
                productId,
                item.planId.toString(),
                item.countryCode
              );
            } else {
              await apiService.validateGooglePlayPurchase(
                purchaseResult.transactionId!,
                productId,
                item.planId.toString(),
                item.countryCode
              );
            }
          } catch (validationError) {
            console.error('Backend validation failed:', validationError);
            allPurchasesSuccessful = false;
            failedPurchases.push(`${item.countryName} ${item.dataInGb}GB plan (validation failed)`);
          }
        }

        if (allPurchasesSuccessful) {
          clearCart();
          setCurrentStep('confirmation');
          setAlertMessage('In-app purchase successful! Your eSIMs will be delivered shortly.');
          setShowAlert(true);
        } else {
          throw new Error(`Failed to purchase: ${failedPurchases.join(', ')}`);
        }
      } else if (paymentMethod === 'web_purchase' && webPurchaseAvailable) {
        // Process web purchase with LemonSqueezy
        const webPurchaseResult = await inAppPurchaseService.purchaseOnWeb(
          state.items.map(item => ({
            planId: item.planId,
            countryCode: item.countryCode,
            countryName: item.countryName,
            dataInGb: item.dataInGb,
            price: parseFloat(item.priceUsd) || 0, // Convert price from string
          })),
          {
            email: customerInfo.email,
            name: `${customerInfo.firstName} ${customerInfo.lastName}`,
            userId: currentUser?.uid,
          }
        );

        if (webPurchaseResult.success && webPurchaseResult.checkoutUrl) {
          // Redirect to LemonSqueezy checkout
          window.open(webPurchaseResult.checkoutUrl, '_blank');

          setAlertMessage('Redirecting to secure checkout. Complete your purchase and return to this page.');
          setShowAlert(true);
        } else {
          throw new Error(webPurchaseResult.error || 'Failed to create web checkout');
        }
      } else {
        // Simulate regular payment processing (card, Apple Pay, Google Pay)
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Clear cart and show confirmation
        clearCart();
        setCurrentStep('confirmation');

        setAlertMessage('Payment successful! Your eSIMs will be delivered shortly.');
        setShowAlert(true);
      }
    } catch (error: any) {
      console.error('Payment failed:', error);
      setAlertMessage(error.message || 'Payment failed. Please try again.');
      setShowAlert(true);
    } finally {
      setProcessing(false);
    }
  };

  const formatDuration = (days: number) => {
    if (days === 1) return '1 day';
    if (days < 30) return `${days} days`;
    if (days === 30) return '1 month';
    return `${Math.floor(days / 30)} months`;
  };

  const renderReviewStep = () => (
    <>
      <IonCard className="checkout-section">
        <IonCardHeader>
          <IonCardTitle>Order Review</IonCardTitle>
        </IonCardHeader>
        <IonCardContent>
          <IonList>
            {state.items.map((item) => (
              <IonItem key={item.id}>
                <IonLabel>
                  <h2>{item.countryName} - {item.dataInGb}GB</h2>
                  <p>{formatDuration(item.duration)} • {item.speed}</p>
                  <p>Quantity: {item.quantity}</p>
                </IonLabel>
                <IonText slot="end" color="primary">
                  <strong>{CartService.formatPrice(parseFloat(item.priceUsd) * item.quantity)}</strong>
                </IonText>
              </IonItem>
            ))}
          </IonList>
        </IonCardContent>
      </IonCard>

      <IonCard className="checkout-section">
        <IonCardHeader>
          <IonCardTitle>Customer Information</IonCardTitle>
        </IonCardHeader>
        <IonCardContent>
          <div className="form-row">
            <div className="form-field">
              <label>First Name</label>
              <IonItem>
                <IonIcon icon={person} slot="start" />
                <IonInput
                  placeholder="Enter first name"
                  value={customerInfo.firstName}
                  onIonInput={(e) => handleCustomerInfoChange('firstName', e.detail.value!)}
                  required
                />
              </IonItem>
            </div>
            <div className="form-field">
              <label>Last Name</label>
              <IonItem>
                <IonIcon icon={person} slot="start" />
                <IonInput
                  placeholder="Enter last name"
                  value={customerInfo.lastName}
                  onIonInput={(e) => handleCustomerInfoChange('lastName', e.detail.value!)}
                  required
                />
              </IonItem>
            </div>
          </div>

          <div className="form-row">
            <div className="form-field">
              <label>Email</label>
              <IonItem>
                <IonIcon icon={mail} slot="start" />
                <IonInput
                  type="email"
                  placeholder="Enter email"
                  value={customerInfo.email}
                  onIonInput={(e) => handleCustomerInfoChange('email', e.detail.value!)}
                  required
                />
              </IonItem>
            </div>
            <div className="form-field">
              <label>Phone (Optional)</label>
              <IonItem>
                <IonIcon icon={call} slot="start" />
                <IonInput
                  type="tel"
                  placeholder="Enter phone number"
                  value={customerInfo.phone}
                  onIonInput={(e) => handleCustomerInfoChange('phone', e.detail.value!)}
                />
              </IonItem>
            </div>
          </div>
          
          <IonItem lines="none" className="terms-checkbox">
            <IonCheckbox
              checked={acceptTerms}
              onIonChange={(e) => setAcceptTerms(e.detail.checked)}
            />
            <IonLabel className="ion-margin-start">
              I agree to the <a href="/terms">Terms & Conditions</a> and <a href="/privacy">Privacy Policy</a>
            </IonLabel>
          </IonItem>
        </IonCardContent>
      </IonCard>
    </>
  );

  const renderPaymentStep = () => (
    <>
      <IonCard className="checkout-section">
        <IonCardHeader>
          <IonCardTitle>Payment Method</IonCardTitle>
        </IonCardHeader>
        <IonCardContent>
          <div className="payment-methods">
            <IonSegment
              value={paymentMethod}
              onIonChange={(e) => setPaymentMethod(e.detail.value as PaymentMethod)}
            >
            {inAppPurchaseAvailable && (
              <IonSegmentButton value="in_app_purchase">
                <IonIcon icon={card} />
                <IonLabel>In-App</IonLabel>
              </IonSegmentButton>
            )}
            {webPurchaseAvailable && (
              <IonSegmentButton value="web_purchase">
                <IonIcon icon={card} />
                <IonLabel>Web</IonLabel>
              </IonSegmentButton>
            )}
            <IonSegmentButton value="card">
              <IonIcon icon={card} />
              <IonLabel>Card</IonLabel>
            </IonSegmentButton>
            <IonSegmentButton value="apple_pay">
              <IonIcon icon={logoApple} />
              <IonLabel>Apple Pay</IonLabel>
            </IonSegmentButton>
            <IonSegmentButton value="google_pay">
              <IonIcon icon={logoGoogle} />
              <IonLabel>Google Pay</IonLabel>
            </IonSegmentButton>
          </IonSegment>
          </div>

          {paymentMethod === 'in_app_purchase' && (
            <div className="payment-info">
              <IonText color="primary">
                <p>
                  <strong>In-App Purchase</strong><br />
                  Your purchase will be processed through the app store and charged to your account.
                  You can manage your subscriptions in your device settings.
                </p>
              </IonText>
            </div>
          )}

          {paymentMethod === 'web_purchase' && (
            <div className="payment-info">
              <IonText color="primary">
                <p>
                  <strong>Secure Web Checkout</strong><br />
                  You'll be redirected to our secure payment processor (LemonSqueezy) to complete your purchase.
                  All major payment methods are accepted including credit cards, PayPal, and more.
                </p>
              </IonText>
            </div>
          )}

          {paymentMethod === 'card' && (
            <div className="payment-form">
              <div className="form-field card-number-field">
                <label>Card Number</label>
                <IonItem>
                  <IonInput
                    placeholder="1234 5678 9012 3456"
                    value={paymentInfo.cardNumber}
                    onIonInput={(e) => handlePaymentInfoChange('cardNumber', e.detail.value!)}
                    required
                  />
                </IonItem>
              </div>

              <div className="form-row">
                <div className="form-field">
                  <label>Expiry Date</label>
                  <IonItem>
                    <IonInput
                      placeholder="MM/YY"
                      value={paymentInfo.expiryDate}
                      onIonInput={(e) => handlePaymentInfoChange('expiryDate', e.detail.value!)}
                      required
                    />
                  </IonItem>
                </div>
                <div className="form-field">
                  <label>CVV</label>
                  <IonItem>
                    <IonInput
                      placeholder="123"
                      value={paymentInfo.cvv}
                      onIonInput={(e) => handlePaymentInfoChange('cvv', e.detail.value!)}
                      required
                    />
                  </IonItem>
                </div>
              </div>

              <div className="form-field">
                <label>Cardholder Name</label>
                <IonItem>
                  <IonInput
                    placeholder="John Doe"
                    value={paymentInfo.cardholderName}
                    onIonInput={(e) => handlePaymentInfoChange('cardholderName', e.detail.value!)}
                    required
                  />
                </IonItem>
              </div>
            </div>
          )}

          <div className="security-notice">
            <IonIcon icon={shield} color="success" />
            <IonText color="medium">
              <small>Your payment information is encrypted and secure</small>
            </IonText>
          </div>
        </IonCardContent>
      </IonCard>
    </>
  );

  const renderConfirmationStep = () => (
    <IonCard className="checkout-section confirmation">
      <IonCardContent className="ion-text-center">
        <IonIcon icon={checkmarkCircle} color="success" className="confirmation-icon" />
        <h1>Order Confirmed!</h1>
        <p>Thank you for your purchase. Your eSIMs will be delivered to your email shortly.</p>
        <IonButton fill="solid" onClick={() => history.push('/profile')}>
          View Orders
        </IonButton>
      </IonCardContent>
    </IonCard>
  );

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/countries" />
          </IonButtons>
          <IonTitle>Checkout</IonTitle>
        </IonToolbar>
      </IonHeader>

      <IonContent className="checkout-content">
        <div className="checkout-container">
          {currentStep === 'review' && renderReviewStep()}
          {currentStep === 'payment' && renderPaymentStep()}
          {currentStep === 'confirmation' && renderConfirmationStep()}

          {currentStep !== 'confirmation' && (
            <>
              <IonCard className="order-summary">
                <IonCardHeader>
                  <IonCardTitle>Order Summary</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  <div className="summary-row">
                    <span>Subtotal ({summary.itemCount} items)</span>
                    <span>{CartService.formatPrice(summary.subtotal)}</span>
                  </div>
                  {summary.savings > 0 && (
                    <div className="summary-row savings">
                      <span>Bulk Discount</span>
                      <span>-{CartService.formatPrice(summary.savings)}</span>
                    </div>
                  )}
                  <div className="summary-row total">
                    <span>Total</span>
                    <span>{CartService.formatPrice(summary.total)}</span>
                  </div>
                </IonCardContent>
              </IonCard>

              <div className="checkout-actions">
                {currentStep === 'payment' && (
                  <IonButton
                    fill="clear"
                    onClick={() => setCurrentStep('review')}
                    className="back-btn"
                  >
                    Back to Review
                  </IonButton>
                )}
                <IonButton
                  fill="solid"
                  onClick={handleNextStep}
                  disabled={
                    processing ||
                    (currentStep === 'review' && !validateCustomerInfo()) ||
                    (currentStep === 'payment' && !validatePaymentInfo())
                  }
                  className="next-btn"
                >
                  {processing ? (
                    <IonSpinner />
                  ) : (
                    <>
                      <IonIcon icon={currentStep === 'payment' ? lockClosed : card} slot="start" />
                      {currentStep === 'review' ? 'Continue to Payment' : 'Complete Order'}
                    </>
                  )}
                </IonButton>
              </div>
            </>
          )}
        </div>

        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header="Checkout"
          message={alertMessage}
          buttons={['OK']}
        />
      </IonContent>
    </IonPage>
  );
};

export default Checkout;
