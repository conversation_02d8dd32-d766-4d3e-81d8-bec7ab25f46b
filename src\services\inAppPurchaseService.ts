import { Capacitor } from '@capacitor/core';
// LemonSqueezy service removed - using Stripe instead

// Declare the cordova-plugin-purchase types
declare global {
  interface Window {
    store: {
      verbosity: number;
      QUIET: number;
      ERROR: number;
      WARNING: number;
      INFO: number;
      DEBUG: number;

      // Product types
      FREE_SUBSCRIPTION: string;
      PAID_SUBSCRIPTION: string;
      NON_RENEWING_SUBSCRIPTION: string;
      CONSUMABLE: string;
      NON_CONSUMABLE: string;

      // Product states
      REGISTERED: string;
      INVALID: string;
      VALID: string;
      REQUESTED: string;
      INITIATED: string;
      APPROVED: string;
      FINISHED: string;
      OWNED: string;

      // Methods
      register: (product: ProductDefinition) => void;
      ready: (callback: () => void) => void;
      when: (query: string) => {
        approved: (callback: (product: Product) => void) => any;
        verified: (callback: (product: Product) => void) => any;
        updated: (callback: (product: Product) => void) => any;
        owned: (callback: (product: Product) => void) => any;
        cancelled: (callback: (product: Product) => void) => any;
        error: (callback: (error: any) => void) => any;
      };
      refresh: () => void;
      order: (productId: string, additionalData?: any) => void;
      get: (productId: string) => Product | undefined;
      products: Product[];
    };
  }
}

interface ProductDefinition {
  id: string;
  type: string;
}

interface Product {
  id: string;
  title: string;
  description: string;
  price: string;
  currency: string;
  loaded: boolean;
  valid: boolean;
  state: string;
  transaction?: {
    id: string;
    type: string;
    state: string;
  };
}

export interface PurchaseResult {
  success: boolean;
  transactionId?: string;
  error?: string;
  product?: Product;
}

export interface ProductInfo {
  id: string;
  title: string;
  description: string;
  price: string;
  currency: string;
  loaded: boolean;
  valid: boolean;
}

export class InAppPurchaseService {
  private static instance: InAppPurchaseService;
  private isInitialized = false;
  private products: Map<string, Product> = new Map();

  private constructor() {}

  static getInstance(): InAppPurchaseService {
    if (!InAppPurchaseService.instance) {
      InAppPurchaseService.instance = new InAppPurchaseService();
    }
    return InAppPurchaseService.instance;
  }

  async initialize(productIds: string[] = []): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // Only initialize on native platforms
      if (!Capacitor.isNativePlatform()) {
        console.log('In-app purchases not available on web platform');
        return false;
      }

      if (!window.store) {
        console.error('Cordova Purchase plugin not available');
        return false;
      }

      // Set verbosity level
      window.store.verbosity = window.store.DEBUG;

      // Register products
      productIds.forEach(productId => {
        window.store.register({
          id: productId,
          type: window.store.NON_CONSUMABLE, // eSIMs are non-consumable
        });
      });

      // Set up event handlers
      this.setupEventHandlers();

      // Initialize the store
      window.store.ready(() => {
        console.log('Store is ready');
        this.isInitialized = true;
        this.updateProductsMap();
      });

      window.store.refresh();

      return true;
    } catch (error) {
      console.error('Failed to initialize cordova-plugin-purchase:', error);
      return false;
    }
  }

  private setupEventHandlers(): void {
    if (!window.store) return;

    // Handle successful purchases
    window.store.when('product').approved((product: Product) => {
      console.log('Product approved:', product);
      // Verify the purchase on your server
      this.verifyPurchase(product);
    });

    // Handle verified purchases
    window.store.when('product').verified((product: Product) => {
      console.log('Product verified:', product);
      // Transaction is automatically finished after verification in cordova-plugin-purchase
    });

    // Handle errors
    window.store.when('product').error((error: any) => {
      console.error('Purchase error:', error);
    });

    // Handle cancelled purchases
    window.store.when('product').cancelled((product: Product) => {
      console.log('Purchase cancelled:', product);
    });
  }

  private updateProductsMap(): void {
    if (!window.store) return;

    window.store.products.forEach(product => {
      this.products.set(product.id, product);
    });
  }

  private async verifyPurchase(product: Product): Promise<void> {
    try {
      // Here you would verify the purchase with your backend
      // For now, we'll just finish the transaction
      console.log('Verifying purchase:', product);

      // In a real app, send the receipt to your server for validation
      // const result = await this.apiService.validatePurchase(product.transaction);

      // For demo purposes, we'll assume verification is successful
      if (product.transaction) {
        // Mark as verified (this would normally be done after server verification)
        console.log('Purchase verified successfully');
      }
    } catch (error) {
      console.error('Purchase verification failed:', error);
    }
  }

  getProducts(): ProductInfo[] {
    return Array.from(this.products.values()).map(product => ({
      id: product.id,
      title: product.title,
      description: product.description,
      price: product.price,
      currency: product.currency,
      loaded: product.loaded,
      valid: product.valid,
    }));
  }

  async purchaseProduct(productId: string): Promise<PurchaseResult> {
    try {
      if (!this.isInitialized) {
        throw new Error('Store not initialized');
      }

      if (!window.store) {
        throw new Error('Store not available');
      }

      const product = window.store.get(productId);
      if (!product) {
        throw new Error(`Product ${productId} not found`);
      }

      if (!product.valid) {
        throw new Error(`Product ${productId} is not valid`);
      }

      return new Promise((resolve) => {
        // Set up one-time listeners for this purchase
        const cleanup = () => {
          // Remove listeners after purchase attempt
        };

        window.store.when(productId).approved((product: Product) => {
          cleanup();
          resolve({
            success: true,
            transactionId: product.transaction?.id,
            product,
          });
        });

        window.store.when(productId).error((error: any) => {
          cleanup();
          resolve({
            success: false,
            error: error.message || 'Purchase failed',
          });
        });

        window.store.when(productId).cancelled((_product: Product) => {
          cleanup();
          resolve({
            success: false,
            error: 'Purchase was cancelled by user',
          });
        });

        // Initiate the purchase
        window.store.order(productId);
      });
    } catch (error: any) {
      console.error('Purchase failed:', error);
      return {
        success: false,
        error: error.message || 'Purchase failed',
      };
    }
  }

  async restorePurchases(): Promise<PurchaseResult> {
    try {
      if (!this.isInitialized) {
        throw new Error('Store not initialized');
      }

      if (!window.store) {
        throw new Error('Store not available');
      }

      // Refresh the store to restore purchases
      window.store.refresh();

      return {
        success: true,
      };
    } catch (error: any) {
      console.error('Restore purchases failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to restore purchases',
      };
    }
  }

  getOwnedProducts(): Product[] {
    if (!window.store) return [];

    return window.store.products.filter(product =>
      product.state === window.store.OWNED
    );
  }

  refreshStore(): void {
    if (window.store && this.isInitialized) {
      window.store.refresh();
      this.updateProductsMap();
    }
  }

  isAvailable(): boolean {
    return Capacitor.isNativePlatform() && this.isInitialized && !!window.store;
  }

  isWebPlatform(): boolean {
    return !Capacitor.isNativePlatform();
  }

  // Method to handle web purchases using LemonSqueezy
  async purchaseOnWeb(
    items: Array<{
      planId: number;
      countryCode: string;
      countryName: string;
      dataInGb: number;
      price: number;
    }>,
    customerData?: {
      email?: string;
      name?: string;
      userId?: string;
    }
  ): Promise<{ success: boolean; checkoutUrl?: string; error?: string }> {
    try {
      if (!this.isWebPlatform()) {
        throw new Error('Web purchases only available on web platform');
      }

      // Web purchases now use credits system instead
      throw new Error('Web purchases disabled - please use the credits system instead');
    } catch (error: any) {
      console.error('Web purchase failed:', error);
      return {
        success: false,
        error: error.message || 'Web purchase failed',
      };
    }
  }

  // Helper method to create product identifiers for eSIM plans
  static createProductIdentifier(planId: number, countryCode: string): string {
    return `esim_plan_${countryCode.toLowerCase()}_${planId}`;
  }

  // Helper method to parse product identifier back to plan info
  static parseProductIdentifier(productId: string): { planId: number; countryCode: string } | null {
    const match = productId.match(/^esim_plan_([a-z]+)_(\d+)$/);
    if (match) {
      return {
        countryCode: match[1].toUpperCase(),
        planId: parseInt(match[2], 10),
      };
    }
    return null;
  }

  // Method to check if a specific plan is purchased
  isPlanPurchased(planId: number, countryCode: string): boolean {
    try {
      const productId = InAppPurchaseService.createProductIdentifier(planId, countryCode);
      const product = this.products.get(productId);
      return product ? product.state === window.store?.OWNED : false;
    } catch (error) {
      console.error('Failed to check plan purchase status:', error);
      return false;
    }
  }

  // Method to get all purchased plans
  getPurchasedPlans(): Array<{ planId: number; countryCode: string }> {
    try {
      const purchasedPlans: Array<{ planId: number; countryCode: string }> = [];
      const ownedProducts = this.getOwnedProducts();

      for (const product of ownedProducts) {
        const planInfo = InAppPurchaseService.parseProductIdentifier(product.id);
        if (planInfo) {
          purchasedPlans.push(planInfo);
        }
      }

      return purchasedPlans;
    } catch (error) {
      console.error('Failed to get purchased plans:', error);
      return [];
    }
  }
}

// Export singleton instance
export const inAppPurchaseService = InAppPurchaseService.getInstance();
