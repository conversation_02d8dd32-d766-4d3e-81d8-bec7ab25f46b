import { FirebaseAuthentication } from '@capacitor-firebase/authentication';
import { Capacitor } from '@capacitor/core';

export interface AuthUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

export interface AuthResult {
  user: AuthUser | null;
  success: boolean;
  error?: string;
  code?: string;
}

export class CapacitorAuthService {
  
  /**
   * Initialize the authentication service
   */
  static async initialize(): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      // Plugin is automatically initialized on native platforms
      console.log('Capacitor Firebase Auth initialized for native platform');
    } else {
      console.log('Running on web - using standard Firebase Auth');
    }
  }

  /**
   * Get the current authenticated user
   */
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      console.log('CapacitorAuthService.getCurrentUser - Calling FirebaseAuthentication.getCurrentUser()...');
      const result = await FirebaseAuthentication.getCurrentUser();
      console.log('CapacitorAuthService.getCurrentUser - Raw result:', result);

      if (!result) {
        console.log('CapacitorAuthService.getCurrentUser - No result from FirebaseAuthentication');
        return null;
      }

      if (!result.user) {
        console.log('CapacitorAuthService.getCurrentUser - No user in result');
        return null;
      }

      console.log('CapacitorAuthService.getCurrentUser - Raw user:', result.user);
      const mappedUser = this.mapFirebaseUser(result.user);
      console.log('CapacitorAuthService.getCurrentUser - Mapped user:', mappedUser);
      return mappedUser;
    } catch (error) {
      console.error('CapacitorAuthService.getCurrentUser - Error:', error);
      return null;
    }
  }

  /**
   * Sign in with Google
   */
  static async signInWithGoogle(): Promise<AuthResult> {
    try {
      const result = await FirebaseAuthentication.signInWithGoogle();
      
      return {
        user: result.user ? this.mapFirebaseUser(result.user) : null,
        success: true
      };
    } catch (error: any) {
      console.error('Google sign-in error:', error);
      return {
        user: null,
        success: false,
        error: error.message || 'Google sign-in failed',
        code: error.code
      };
    }
  }

  /**
   * Sign in with email and password
   */
  static async signInWithEmailAndPassword(email: string, password: string): Promise<AuthResult> {
    try {
      const result = await FirebaseAuthentication.signInWithEmailAndPassword({
        email,
        password
      });

      return {
        user: result.user ? this.mapFirebaseUser(result.user) : null,
        success: true
      };
    } catch (error: any) {
      console.error('Email sign-in error:', error);
      return {
        user: null,
        success: false,
        error: error.message || 'Email sign-in failed',
        code: error.code
      };
    }
  }

  /**
   * Create user with email and password
   */
  static async createUserWithEmailAndPassword(email: string, password: string): Promise<AuthResult> {
    try {
      const result = await FirebaseAuthentication.createUserWithEmailAndPassword({
        email,
        password
      });

      return {
        user: result.user ? this.mapFirebaseUser(result.user) : null,
        success: true
      };
    } catch (error: any) {
      console.error('User creation error:', error);
      return {
        user: null,
        success: false,
        error: error.message || 'User creation failed',
        code: error.code
      };
    }
  }

  /**
   * Send password reset email
   */
  static async sendPasswordResetEmail(email: string): Promise<{ success: boolean; error?: string }> {
    try {
      await FirebaseAuthentication.sendPasswordResetEmail({ email });
      return { success: true };
    } catch (error: any) {
      console.error('Password reset error:', error);
      return {
        success: false,
        error: error.message || 'Failed to send password reset email'
      };
    }
  }

  /**
   * Sign out the current user
   */
  static async signOut(): Promise<{ success: boolean; error?: string }> {
    try {
      await FirebaseAuthentication.signOut();
      return { success: true };
    } catch (error: any) {
      console.error('Sign out error:', error);
      return {
        success: false,
        error: error.message || 'Sign out failed'
      };
    }
  }

  /**
   * Get ID token for the current user
   */
  static async getIdToken(): Promise<string | null> {
    try {
      const currentUser = await this.getCurrentUser();
      if (!currentUser) {
        return null;
      }
      
      const result = await FirebaseAuthentication.getIdToken();
      return result.token;
    } catch (error) {
      console.error('Error getting ID token:', error);
      return null;
    }
  }

  /**
   * Add authentication state change listener
   */
  static async addAuthStateListener(callback: (user: AuthUser | null) => void): Promise<() => void> {
    const listener = await FirebaseAuthentication.addListener('authStateChange', (result) => {
      const user = result.user ? this.mapFirebaseUser(result.user) : null;
      callback(user);
    });

    return () => listener.remove();
  }

  /**
   * Map Firebase user to our AuthUser interface
   */
  private static mapFirebaseUser(firebaseUser: any): AuthUser {
    return {
      uid: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
      photoURL: firebaseUser.photoURL || firebaseUser.photoUrl || null,
      emailVerified: firebaseUser.emailVerified || false
    };
  }
}
