.onboarding-page {
  --background: radial-gradient(circle at 50% 10%, #1a1a2e 0%, #000000 100%);
  --ion-background-color: #000000;
}

.slides-viewport {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  will-change: transform;
}

.onboarding-slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 2rem;
  padding-top: 8vh;
  box-sizing: border-box;
  text-align: center;
}

.slide-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 3rem;
  height: 40vh;
  width: 100%;
}

.slide-image {
  max-width: 85%;
  max-height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 10px 30px rgba(0, 210, 255, 0.2));
}

.slide-image.main-logo {
  max-width: 70%;
}

.slide-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 400px;
}

.slide-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  line-height: 1.1;
  letter-spacing: -0.5px;
}

.slide-text {
  color: #e0e0e0;
  font-size: 1.1rem;
  line-height: 1.5;
  margin-bottom: 2rem;
}

.slide-actions {
  width: 100%;
  margin-top: auto;
  padding-bottom: 12rem; /* Increased space for dots and nav buttons */
  margin-bottom: 2rem; /* Additional margin for better separation */
}

/* Action Buttons */
.get-started-btn, .register-btn {
  --background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  --color: #ffffff;
  font-weight: 700;
  height: 56px;
  --border-radius: 14px;
  font-size: 1.1rem;
  margin: 0.5rem 0;
  width: 100%;
}

.login-btn {
  --color: #ffffff;
  --background: transparent;
  --border-color: rgba(255, 255, 255, 0.3);
  --border-width: 1px;
  --border-style: solid;
  font-weight: 600;
  height: 56px;
  --border-radius: 14px;
  font-size: 1.1rem;
  margin: 0.5rem 0;
  width: 100%;
}

/* Navigation & Dots */
.navigation-dots {
  position: absolute;
  bottom: 10rem; /* Moved higher to avoid button overlap */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 0.8rem; /* Slightly increased gap for better visibility */
  z-index: 10;
  padding: 1rem 0; /* Added padding for better touch targets */
  /* Temporary debug background */
  background: rgba(255, 0, 0, 0.2);
}

.dot {
  width: 10px; /* Slightly larger for better visibility */
  height: 6px; /* Slightly taller for better visibility */
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.3); /* More visible */
  transition: all 0.3s ease;
  cursor: pointer; /* Indicate clickable */
}

.dot:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.2);
}

.dot.active {
  background: #00d2ff;
  width: 28px; /* Proportionally larger */
  box-shadow: 0 0 8px rgba(0, 210, 255, 0.4); /* Subtle glow */
}

.dot.active:hover {
  background: #00d2ff;
  box-shadow: 0 0 12px rgba(0, 210, 255, 0.6);
}

.slide-navigation-buttons {
  position: absolute;
  bottom: 3rem; /* Moved slightly higher for better spacing */
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 2rem;
  z-index: 20;
}

.nav-btn-classy {
  --color: rgba(255, 255, 255, 0.9);
  --background: rgba(255, 255, 255, 0.08);
  --border-radius: 50%;
  --border-color: rgba(0, 210, 255, 0.2);
  --border-width: 1.5px;
  --border-style: solid;
  width: 56px; /* Slightly larger for better touch target */
  height: 56px;
  --padding-start: 0;
  --padding-end: 0;
  margin: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 210, 255, 0.1), 0 0 0 0 rgba(0, 210, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nav-btn-classy:hover {
  --background: rgba(255, 255, 255, 0.15);
  --border-color: rgba(0, 210, 255, 0.6);
  --color: #ffffff;
  box-shadow: 0 6px 25px rgba(0, 210, 255, 0.25), 0 0 0 4px rgba(0, 210, 255, 0.1);
  transform: scale(1.08) translateY(-2px);
}

.nav-btn-classy:active {
  transform: scale(1.02) translateY(0px);
  transition: all 0.1s ease;
}

.nav-btn-classy ion-icon {
  font-size: 1.5rem;
  transition: transform 0.2s ease;
}

.nav-btn-classy:hover ion-icon {
  transform: scale(1.1);
}

/* Responsive Design */
@media (max-height: 700px) {
  .slide-actions {
    padding-bottom: 10rem; /* Reduced spacing on smaller screens */
    margin-bottom: 1rem;
  }

  .navigation-dots {
    bottom: 8rem;
  }

  .slide-navigation-buttons {
    bottom: 2rem;
  }

  .slide-image-container {
    height: 35vh; /* Smaller image container */
    margin-bottom: 2rem;
  }

  .slide-title {
    font-size: 2.2rem; /* Slightly smaller title */
  }
}

@media (max-height: 600px) {
  .slide-actions {
    padding-bottom: 8rem;
  }

  .navigation-dots {
    bottom: 6rem;
  }

  .slide-navigation-buttons {
    bottom: 1.5rem;
  }

  .slide-image-container {
    height: 30vh;
    margin-bottom: 1.5rem;
  }

  .slide-title {
    font-size: 2rem;
    margin-bottom: 0.8rem;
  }

  .slide-text {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
}

/* Animations - Subtle */
.onboarding-slide > * {
  animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
