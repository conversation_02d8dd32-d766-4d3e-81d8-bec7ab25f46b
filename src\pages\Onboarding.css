.onboarding-page {
  --background: radial-gradient(circle at 50% 10%, #1a1a2e 0%, #000000 100%);
  --ion-background-color: #000000;
}

.slides-viewport {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  will-change: transform;
}

.onboarding-slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 2rem;
  padding-top: 8vh;
  box-sizing: border-box;
  text-align: center;
}

.slide-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 3rem;
  height: 40vh;
  width: 100%;
}

.slide-image {
  max-width: 85%;
  max-height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 10px 30px rgba(0, 210, 255, 0.2));
}

.slide-image.main-logo {
  max-width: 70%;
}

.slide-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 400px;
}

.slide-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  line-height: 1.1;
  letter-spacing: -0.5px;
}

.slide-text {
  color: #e0e0e0;
  font-size: 1.1rem;
  line-height: 1.5;
  margin-bottom: 2rem;
}

.slide-actions {
  width: 100%;
  margin-top: auto;
  padding-bottom: 4rem; /* Space for dots and nav buttons */
}

/* Action Buttons */
.get-started-btn, .register-btn {
  --background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  --color: #ffffff;
  font-weight: 700;
  height: 56px;
  --border-radius: 14px;
  font-size: 1.1rem;
  margin: 0.5rem 0;
  width: 100%;
}

.login-btn {
  --color: #ffffff;
  --background: transparent;
  --border-color: rgba(255, 255, 255, 0.3);
  --border-width: 1px;
  --border-style: solid;
  font-weight: 600;
  height: 56px;
  --border-radius: 14px;
  font-size: 1.1rem;
  margin: 0.5rem 0;
  width: 100%;
}

/* Navigation & Dots */
.navigation-dots {
  position: absolute;
  bottom: 8rem;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 0.6rem;
  z-index: 10;
}

.dot {
  width: 8px;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.dot.active {
  background: #00d2ff;
  width: 24px;
}

.slide-navigation-buttons {
  position: absolute;
  bottom: 2rem;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 2rem;
  z-index: 20;
}

.nav-btn-classy {
  --color: rgba(255, 255, 255, 0.8);
  --background: rgba(255, 255, 255, 0.03);
  --border-radius: 50%;
  --border-color: rgba(255, 255, 255, 0.1);
  --border-width: 1px;
  --border-style: solid;
  width: 50px;
  height: 50px;
  --padding-start: 0;
  --padding-end: 0;
  margin: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 15px rgba(0, 210, 255, 0.05);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.nav-btn-classy:hover {
  --background: rgba(255, 255, 255, 0.08);
  --border-color: rgba(0, 210, 255, 0.4);
  --color: #ffffff;
  box-shadow: 0 0 20px rgba(0, 210, 255, 0.2);
  transform: scale(1.05);
}

.nav-btn-classy ion-icon {
  font-size: 1.4rem;
}

/* Animations - Subtle */
.onboarding-slide > * {
  animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
