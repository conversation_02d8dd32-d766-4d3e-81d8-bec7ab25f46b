"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProducts = void 0;
const functions = require("firebase-functions");
const admin = require("firebase-admin");
exports.getProducts = functions.https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.set('Access-Control-Max-Age', '3600');
    if (req.method === 'OPTIONS') {
        res.status(200).send('');
        return;
    }
    try {
        const db = admin.firestore();
        const snapshot = await db
            .collection('credit_packages')
            .where('active', '==', true)
            .orderBy('price', 'asc')
            .get();
        const packages = snapshot.docs.map(doc => doc.data());
        res.json(packages);
    }
    catch (error) {
        console.error('Get products error:', error);
        res.status(500).json({ error: 'Failed to get products' });
    }
});
//# sourceMappingURL=getProducts.js.map