import { auth } from '../firebase';
import { getFunctions, httpsCallable } from 'firebase/functions';

export class ApiService {
  private baseUrl = 'https://us-central1-esim-numero.cloudfunctions.net';
  private functions = getFunctions();

  // Public API calls (no auth required)
  async getCountries() {
    const response = await fetch(`${this.baseUrl}/getESimPricing`);
    if (!response.ok) throw new Error('Failed to fetch countries');
    return response.json();
  }

  async getDataPlans(planId: string) {
    const response = await fetch(`${this.baseUrl}/getDataPlans?plan=${planId}`);
    if (!response.ok) throw new Error('Failed to fetch data plans');
    return response.json();
  }

  // Protected API calls (auth required) - using callable functions
  async purchaseEsim(planId: string, countryCode: string) {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    const purchaseFunction = httpsCallable(this.functions, 'purchaseEsim');
    const result = await purchaseFunction({ planId, countryCode });
    return result.data;
  }

  async getUserOrders() {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    const getOrdersFunction = httpsCallable(this.functions, 'getUserESimOrders');
    const result = await getOrdersFunction();
    return result.data;
  }

  // In-App Purchase validation methods
  async validateApplePurchase(
    transactionId: string,
    productId: string,
    planId: string,
    countryCode: string,
    purchaseDate?: Date
  ) {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    const validateFunction = httpsCallable(this.functions, 'validateAppleReceipt');
    const result = await validateFunction({
      transactionId,
      productId,
      planId,
      countryCode,
      purchaseDate: purchaseDate?.toISOString()
    });
    return result.data;
  }

  async validateGooglePlayPurchase(
    transactionId: string,
    productId: string,
    planId: string,
    countryCode: string,
    purchaseDate?: Date
  ) {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    const validateFunction = httpsCallable(this.functions, 'validateGooglePlayPurchase');
    const result = await validateFunction({
      transactionId,
      productId,
      planId,
      countryCode,
      purchaseDate: purchaseDate?.toISOString()
    });
    return result.data;
  }

  async handleRefund(transactionId: string) {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    const refundFunction = httpsCallable(this.functions, 'handleRefund');
    const result = await refundFunction({ transactionId });
    return result.data;
  }

  // LemonSqueezy methods
  async getLemonSqueezyOrders(limit?: number) {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    const getOrdersFunction = httpsCallable(this.functions, 'getLemonSqueezyOrders');
    const result = await getOrdersFunction({ limit });
    return result.data;
  }

  async verifyLemonSqueezyOrder(lemonSqueezyOrderId: string) {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    const verifyFunction = httpsCallable(this.functions, 'verifyLemonSqueezyOrder');
    const result = await verifyFunction({ lemonSqueezyOrderId });
    return result.data;
  }
}

export const apiService = new ApiService();

