"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const admin = require("firebase-admin");
const credits_1 = require("./types/credits");
// Initialize Firebase Admin
admin.initializeApp();
const db = admin.firestore();
// Fallback credit packages (in case Stripe is not set up yet)
const FALLBACK_PACKAGES = [
    {
        id: 'minnow',
        name: 'Minnow',
        animal: '🐟 Minnow',
        price: 5,
        credits: 5,
        vibe: 'Tiny but mighty',
        useCase: 'Light travelers, test users',
        icon: credits_1.CreditPackageIcon.MINNOW,
        color: credits_1.CreditPackageColor.MINNOW,
        stripePriceId: 'price_1QZqJrPwpPoMCkz5YGqJrPwp',
        stripeProductId: 'prod_RZqJrPwpPoMCkz5YGqJrPwp',
        active: true
    },
    {
        id: 'tuna',
        name: '<PERSON>na',
        animal: '🐟 Tuna',
        price: 10,
        credits: 10,
        vibe: 'Reliable and steady',
        useCase: 'Regional users',
        icon: credits_1.CreditPackageIcon.TUNA,
        color: credits_1.CreditPackageColor.TUNA,
        stripePriceId: 'price_1QZqKrPwpPoMCkz5YGqKrPwp',
        stripeProductId: 'prod_RZqKrPwpPoMCkz5YGqKrPwp',
        active: true
    },
    {
        id: 'dolphin',
        name: 'Dolphin',
        animal: '🐬 Dolphin',
        price: 20,
        credits: 20,
        vibe: 'Smart and fast',
        useCase: 'Business travelers',
        icon: credits_1.CreditPackageIcon.DOLPHIN,
        color: credits_1.CreditPackageColor.DOLPHIN,
        stripePriceId: 'price_1QZqLrPwpPoMCkz5YGqLrPwp',
        stripeProductId: 'prod_RZqLrPwpPoMCkz5YGqLrPwp',
        active: true
    },
    {
        id: 'octopus',
        name: 'Octopus',
        animal: '🐙 Octopus',
        price: 35,
        credits: 35,
        vibe: 'Flexible, multi-device',
        useCase: 'Remote workers',
        icon: credits_1.CreditPackageIcon.OCTOPUS,
        color: credits_1.CreditPackageColor.OCTOPUS,
        stripePriceId: 'price_1QZqMrPwpPoMCkz5YGqMrPwp',
        stripeProductId: 'prod_RZqMrPwpPoMCkz5YGqMrPwp',
        active: true
    },
    {
        id: 'shark',
        name: 'Shark',
        animal: '🦈 Shark',
        price: 50,
        credits: 50,
        vibe: 'Powerful, premium',
        useCase: 'Heavy data users',
        icon: credits_1.CreditPackageIcon.SHARK,
        color: credits_1.CreditPackageColor.SHARK,
        stripePriceId: 'price_1QZqNrPwpPoMCkz5YGqNrPwp',
        stripeProductId: 'prod_RZqNrPwpPoMCkz5YGqNrPwp',
        active: true
    },
    {
        id: 'whale',
        name: 'Whale',
        animal: '🐋 Whale',
        price: 75,
        credits: 75,
        vibe: 'Massive, enterprise-grade',
        useCase: 'Global nomads, teams',
        icon: credits_1.CreditPackageIcon.WHALE,
        color: credits_1.CreditPackageColor.WHALE,
        stripePriceId: 'price_1QZqOrPwpPoMCkz5YGqOrPwp',
        stripeProductId: 'prod_RZqOrPwpPoMCkz5YGqOrPwp',
        active: true
    }
];
async function initializeFallbackPackages() {
    console.log('Initializing fallback credit packages...');
    const batch = db.batch();
    const now = new Date();
    for (const packageData of FALLBACK_PACKAGES) {
        const packageRef = db.collection('credit_packages').doc(packageData.id);
        const fullPackageData = Object.assign(Object.assign({}, packageData), { createdAt: now, updatedAt: now });
        batch.set(packageRef, fullPackageData);
        console.log(`Added package: ${packageData.animal} - $${packageData.price} (${packageData.credits} credits)`);
    }
    await batch.commit();
    console.log('✅ Fallback credit packages initialized successfully!');
}
// Run initialization
initializeFallbackPackages()
    .then(() => {
    console.log('🎉 Initialization complete!');
    process.exit(0);
})
    .catch((error) => {
    console.error('❌ Initialization failed:', error);
    process.exit(1);
});
//# sourceMappingURL=initializeData.js.map