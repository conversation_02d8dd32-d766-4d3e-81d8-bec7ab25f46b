.cart-modal {
  --width: 100%;
  --max-width: 600px;
  --height: 90%;
  --border-radius: 16px;
}

.cart-content {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
  padding: 32px;
}

.empty-cart-icon {
  font-size: 80px;
  color: var(--ion-color-medium);
  margin-bottom: 24px;
}

.empty-cart h2 {
  color: var(--ion-color-dark);
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.empty-cart p {
  color: var(--ion-color-medium);
  margin: 0 0 24px 0;
  font-size: 16px;
}

.cart-items {
  margin-bottom: 16px;
}

.cart-item-card {
  margin-bottom: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cart-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-item-header ion-card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.remove-item-btn {
  --color: var(--ion-color-danger);
  --padding-start: 8px;
  --padding-end: 8px;
}

.item-details h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.item-details p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--ion-color-medium);
}

.item-features {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.item-price-col {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
}

.item-price h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  background: var(--ion-color-light);
  border-radius: 20px;
  padding: 4px;
}

.quantity-controls ion-button {
  --padding-start: 8px;
  --padding-end: 8px;
  --border-radius: 50%;
  width: 32px;
  height: 32px;
}

.quantity {
  min-width: 24px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
}

.cart-summary {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cart-summary ion-card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.summary-row:not(:last-child) {
  border-bottom: 1px solid var(--ion-color-light);
}

.summary-row.savings {
  color: var(--ion-color-success);
  font-weight: 600;
}

.summary-row.total {
  font-size: 18px;
  font-weight: 700;
  color: var(--ion-color-dark);
  padding-top: 12px;
  border-top: 2px solid var(--ion-color-primary);
  margin-top: 8px;
}

.cart-actions {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  position: sticky;
  bottom: 0;
  background: var(--ion-background-color);
  border-top: 1px solid var(--ion-color-light);
  margin: 0 -16px -16px -16px;
  padding: 16px;
}

.clear-cart-btn {
  flex: 0 0 auto;
  --color: var(--ion-color-medium);
}

.checkout-btn {
  flex: 1;
  --background: linear-gradient(45deg, var(--ion-color-primary), var(--ion-color-secondary));
  --border-radius: 12px;
  --box-shadow: 0 4px 16px rgba(var(--ion-color-primary-rgb), 0.3);
  font-weight: 600;
  height: 48px;
}

.checkout-btn:hover {
  --box-shadow: 0 6px 20px rgba(var(--ion-color-primary-rgb), 0.4);
  transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 768px) {
  .cart-modal {
    --width: 100%;
    --height: 100%;
    --border-radius: 0;
  }
  
  .cart-actions {
    flex-direction: column;
  }
  
  .clear-cart-btn,
  .checkout-btn {
    width: 100%;
  }
}

/* Animation for cart items */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.cart-item-card {
  animation: slideInRight 0.3s ease-out;
}

.cart-item-card:nth-child(2) {
  animation-delay: 0.1s;
}

.cart-item-card:nth-child(3) {
  animation-delay: 0.2s;
}

.cart-item-card:nth-child(4) {
  animation-delay: 0.3s;
}

/* Loading states */
.cart-item-card.removing {
  opacity: 0.5;
  transform: translateX(-100%);
  transition: all 0.3s ease-out;
}

/* Hover effects */
.cart-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* Quantity controls styling */
.quantity-controls ion-button[disabled] {
  --color: var(--ion-color-light-shade);
}

.quantity-controls ion-button:not([disabled]):hover {
  --background: var(--ion-color-primary);
  --color: white;
}

/* Summary animations */
.summary-row {
  transition: all 0.3s ease;
}

.summary-row.total {
  animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
