/**
 * Credits Conversion Service
 * Converts USD prices to credits for eSIM plans
 */

export class CreditsConversionService {
  // Base conversion rate: 1 USD = 1 Credit
  // This makes it simple for users to understand
  private static readonly USD_TO_CREDITS_RATE = 1;

  /**
   * Convert USD price to credits
   * @param usdPrice - Price in USD (can be string or number)
   * @returns Number of credits required
   */
  static convertUsdToCredits(usdPrice: string | number): number {
    const price = typeof usdPrice === 'string' ? parseFloat(usdPrice) : usdPrice;
    
    if (isNaN(price) || price < 0) {
      return 0;
    }

    // Round up to ensure we don't undercharge
    return Math.ceil(price * this.USD_TO_CREDITS_RATE);
  }

  /**
   * Convert credits to USD for display purposes
   * @param credits - Number of credits
   * @returns USD price as string
   */
  static convertCreditsToUsd(credits: number): string {
    if (isNaN(credits) || credits < 0) {
      return '$0.00';
    }

    const usdPrice = credits / this.USD_TO_CREDITS_RATE;
    return `$${usdPrice.toFixed(2)}`;
  }

  /**
   * Format credits for display
   * @param credits - Number of credits
   * @returns Formatted string like "5 credits"
   */
  static formatCredits(credits: number): string {
    if (credits === 1) {
      return '1 credit';
    }
    return `${credits} credits`;
  }

  /**
   * Get credit cost with original USD price for comparison
   * @param usdPrice - Original USD price
   * @returns Object with credits and original price
   */
  static getCreditCostInfo(usdPrice: string | number) {
    const credits = this.convertUsdToCredits(usdPrice);
    const originalPrice = typeof usdPrice === 'string' ? usdPrice : usdPrice.toFixed(2);
    
    return {
      credits,
      creditsText: this.formatCredits(credits),
      originalPriceUsd: `$${originalPrice}`,
      conversionRate: this.USD_TO_CREDITS_RATE
    };
  }

  /**
   * Check if user has enough credits for a purchase
   * @param userCredits - User's current credit balance
   * @param requiredCredits - Credits needed for purchase
   * @returns Boolean indicating if user can afford the purchase
   */
  static canAfford(userCredits: number, requiredCredits: number): boolean {
    return userCredits >= requiredCredits;
  }

  /**
   * Calculate how many more credits user needs
   * @param userCredits - User's current credit balance
   * @param requiredCredits - Credits needed for purchase
   * @returns Number of additional credits needed (0 if user can afford)
   */
  static creditsNeeded(userCredits: number, requiredCredits: number): number {
    const needed = requiredCredits - userCredits;
    return Math.max(0, needed);
  }

  /**
   * Get recommended credit package for a purchase
   * @param requiredCredits - Credits needed
   * @param userCredits - User's current credits
   * @returns Recommended package info
   */
  static getRecommendedPackage(requiredCredits: number, userCredits: number = 0) {
    const creditsNeeded = this.creditsNeeded(userCredits, requiredCredits);
    
    // Credit packages (matching the ones in Credits.tsx)
    const packages = [
      { id: 'minnow', name: '🐟 Minnow', credits: 5, price: 5 },
      { id: 'tuna', name: '🐟 Tuna', credits: 10, price: 10 },
      { id: 'dolphin', name: '🐬 Dolphin', credits: 20, price: 20 },
      { id: 'octopus', name: '🐙 Octopus', credits: 35, price: 35 },
      { id: 'shark', name: '🦈 Shark', credits: 50, price: 50 },
      { id: 'whale', name: '🐋 Whale', credits: 75, price: 75 }
    ];

    // Find the smallest package that covers the needed credits
    const recommendedPackage = packages.find(pkg => pkg.credits >= creditsNeeded);
    
    return {
      creditsNeeded,
      recommendedPackage: recommendedPackage || packages[packages.length - 1], // Default to largest if needed
      canAfford: creditsNeeded === 0
    };
  }
}

export default CreditsConversionService;
