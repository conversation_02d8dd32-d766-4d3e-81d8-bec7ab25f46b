import React from 'react';
import {
    IonButton,
    IonGrid,
    IonRow,
    IonCol,
    IonText
} from "@ionic/react";
import './../pages/Onboarding.css';

interface OnboardingSlideProps {
    image: string;
    title: string;
    text: string;
    mainSlide?: boolean;
    finalSlide?: boolean;
    onNext: () => void;
    onLogin?: () => void;
    onRegister?: () => void;
}

const OnboardingSlide: React.FC<OnboardingSlideProps> = ({
    image,
    title,
    text,
    mainSlide = false,
    finalSlide = false,
    onNext,
    onLogin,
    onRegister
}) => {
    return (
        <div className="onboarding-slide">
            <div className="slide-image-container">
                <img
                    src={image}
                    alt={title}
                    className={`slide-image ${mainSlide ? 'main-logo' : ''}`}
                />
            </div>

            <div className="slide-content-wrapper">
                <IonText>
                    <h1 className="slide-title">{title}</h1>
                </IonText>
                <p className="slide-text">{text}</p>

                <div className="slide-actions">
                    {mainSlide && (
                        <IonButton
                            expand="block"
                            className="get-started-btn"
                            onClick={onNext}
                        >
                            Get Started
                        </IonButton>
                    )}

                    {finalSlide && (
                        <>
                            <IonButton
                                expand="block"
                                className="register-btn"
                                onClick={onRegister}
                            >
                                Register
                            </IonButton>
                            <IonButton
                                expand="block"
                                fill="outline"
                                className="login-btn"
                                onClick={onLogin}
                            >
                                Login
                            </IonButton>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
}

export default OnboardingSlide;
