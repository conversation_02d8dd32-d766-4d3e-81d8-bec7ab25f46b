// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN
include ':capacitor-android'
project(':capacitor-android').projectDir = new File('../node_modules/.pnpm/@capacitor+android@7.4.3_@capacitor+core@7.4.3/node_modules/@capacitor/android/capacitor')

include ':capacitor-community-stripe'
project(':capacitor-community-stripe').projectDir = new File('../node_modules/.pnpm/@capacitor-community+stripe_119fa5fcfbf9443d7fc4b5f65248373f/node_modules/@capacitor-community/stripe/android')

include ':capacitor-firebase-app'
project(':capacitor-firebase-app').projectDir = new File('../node_modules/.pnpm/@capacitor-firebase+app@7.3_cb7bf50c5a6f967edb4dc7fcd492c3ad/node_modules/@capacitor-firebase/app/android')

include ':capacitor-firebase-authentication'
project(':capacitor-firebase-authentication').projectDir = new File('../node_modules/.pnpm/@capacitor-firebase+authent_1dda7cba45dcdf5d1f9852b429d377af/node_modules/@capacitor-firebase/authentication/android')

include ':capacitor-app'
project(':capacitor-app').projectDir = new File('../node_modules/.pnpm/@capacitor+app@7.1.0_@capacitor+core@7.4.3/node_modules/@capacitor/app/android')

include ':capacitor-haptics'
project(':capacitor-haptics').projectDir = new File('../node_modules/.pnpm/@capacitor+haptics@7.0.2_@capacitor+core@7.4.3/node_modules/@capacitor/haptics/android')

include ':capacitor-keyboard'
project(':capacitor-keyboard').projectDir = new File('../node_modules/.pnpm/@capacitor+keyboard@7.0.3_@capacitor+core@7.4.3/node_modules/@capacitor/keyboard/android')

include ':capacitor-push-notifications'
project(':capacitor-push-notifications').projectDir = new File('../node_modules/.pnpm/@capacitor+push-notifications@7.0.3_@capacitor+core@7.4.3/node_modules/@capacitor/push-notifications/android')

include ':capacitor-status-bar'
project(':capacitor-status-bar').projectDir = new File('../node_modules/.pnpm/@capacitor+status-bar@7.0.3_@capacitor+core@7.4.3/node_modules/@capacitor/status-bar/android')

include ':codetrix-studio-capacitor-google-auth'
project(':codetrix-studio-capacitor-google-auth').projectDir = new File('../node_modules/.pnpm/@codetrix-studio+capacitor-_bdd510f08d0c78df51f015dc22e4972b/node_modules/@codetrix-studio/capacitor-google-auth/android')
