{"name": "krakens<PERSON>", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "tsc && vite build", "preview": "vite preview", "test.e2e": "cypress run", "test.unit": "vitest", "lint": "eslint", "version": "node scripts/sync-version.cjs && git add ."}, "dependencies": {"@capacitor-community/stripe": "^7.2.1", "@capacitor-firebase/app": "^7.3.1", "@capacitor-firebase/authentication": "^7.3.1", "@capacitor/android": "^7.4.3", "@capacitor/app": "7.1.0", "@capacitor/core": "7.4.3", "@capacitor/haptics": "7.0.2", "@capacitor/keyboard": "7.0.3", "@capacitor/push-notifications": "^7.0.3", "@capacitor/status-bar": "7.0.3", "@codetrix-studio/capacitor-google-auth": "3.4.0-rc.4", "@ionic/react": "^8.5.0", "@ionic/react-router": "^8.5.0", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "cordova-plugin-purchase": "^13.12.1", "firebase": "^11.2.0", "i18next": "^25.5.2", "i18next-http-backend": "^3.0.2", "ionicons": "^7.4.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^16.0.0", "react-qr-code": "^2.0.18", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "stripe-pwa-elements": "^2.1.0"}, "devDependencies": {"@capacitor/cli": "7.4.3", "@testing-library/dom": ">=7.21.4", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^30.0.0", "@types/node": "^24.9.2", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@vitejs/plugin-legacy": "^5.0.0", "@vitejs/plugin-react": "^4.0.1", "cypress": "^13.5.0", "eslint": "^9.20.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jsdom": "^22.1.0", "terser": "^5.4.0", "typescript": "^5.1.6", "typescript-eslint": "^8.24.0", "vite": "~5.2.0", "vitest": "^0.34.6"}, "overrides": {"rollup": "4.44.0"}, "description": "An Ionic project"}