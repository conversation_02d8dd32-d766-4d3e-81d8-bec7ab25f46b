import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as crypto from 'crypto';
import { logger } from 'firebase-functions/v2';

const cors = require('cors');
const corsHandler = cors({ origin: true });

interface LemonSqueezyWebhookEvent {
  meta: {
    event_name: string;
    custom_data?: {
      user_id?: string;
      package_id?: string;
    };
  };
  data: {
    id: string;
    type: string;
    attributes: {
      store_id: number;
      customer_id: number;
      identifier: string;
      order_number: number;
      user_name: string;
      user_email: string;
      currency: string;
      currency_rate: string;
      subtotal: number;
      discount_total: number;
      tax: number;
      total: number;
      subtotal_usd: number;
      discount_total_usd: number;
      tax_usd: number;
      total_usd: number;
      tax_name: string;
      tax_rate: string;
      status: string;
      status_formatted: string;
      refunded: boolean;
      refunded_at: string | null;
      subtotal_formatted: string;
      discount_total_formatted: string;
      tax_formatted: string;
      total_formatted: string;
      first_order_item: {
        id: number;
        order_id: number;
        product_id: number;
        variant_id: number;
        product_name: string;
        variant_name: string;
        price: number;
        created_at: string;
        updated_at: string;
      };
      urls: {
        receipt: string;
      };
      created_at: string;
      updated_at: string;
      custom?: {
        user_id?: string;
        package_id?: string;
      };
    };
  };
}

class LemonSqueezyService {
  private db: admin.firestore.Firestore;

  constructor() {
    this.db = admin.firestore();
  }

  // Verify webhook signature (following LemonSqueezy official docs)
  private verifyWebhookSignature(body: string, signature: string): boolean {
    // Get webhook secret from environment variable
    const webhookSecret = process.env.LEMONSQUEEZY_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.warn('LemonSqueezy webhook secret not configured');
      return false;
    }

    try {
      const hmac = crypto.createHmac('sha256', webhookSecret);
      const digest = Buffer.from(hmac.update(body).digest('hex'), 'utf8');
      const receivedSignature = Buffer.from(signature || '', 'utf8');

      return crypto.timingSafeEqual(digest, receivedSignature);
    } catch (error) {
      logger.error('Error verifying webhook signature:', error);
      return false;
    }
  }

  // Map LemonSqueezy product names to credit packages
  private mapProductToPackage(productName: string): { packageId: string; credits: number } | null {
    const productMap: Record<string, { packageId: string; credits: number }> = {
      'Minnow': { packageId: 'minnow', credits: 5 },
      'Tuna': { packageId: 'tuna', credits: 10 },
      'Dolphin': { packageId: 'dolphin', credits: 20 },
      'Octopus': { packageId: 'octopus', credits: 35 },
      'Shark': { packageId: 'shark', credits: 50 },
      'Whale': { packageId: 'whale', credits: 75 }
    };

    // Try exact match first
    if (productMap[productName]) {
      return productMap[productName];
    }

    // Try partial match (in case product name includes "Buy" prefix)
    for (const [key, value] of Object.entries(productMap)) {
      if (productName.includes(key)) {
        return value;
      }
    }

    return null;
  }

  // Extract user ID from customer email or custom data
  private async getUserIdFromOrder(event: LemonSqueezyWebhookEvent): Promise<string | null> {
    const { user_email } = event.data.attributes;

    // Check for custom user ID in meta.custom_data or order attributes
    const customUserId = event.meta.custom_data?.user_id ||
                        event.data.attributes.custom?.user_id;

    // If we have custom user ID, use it
    if (customUserId) {
      logger.info(`Found user ID in custom data: ${customUserId}`);
      return customUserId;
    }

    // Otherwise, try to find user by email
    if (user_email) {
      try {
        const userRecord = await admin.auth().getUserByEmail(user_email);
        logger.info(`Found user by email ${user_email}: ${userRecord.uid}`);
        return userRecord.uid;
      } catch (error) {
        logger.warn(`Could not find user with email ${user_email}:`, error);
        return null;
      }
    }

    logger.warn('No user identification method available in webhook');
    return null;
  }

  // Add credits to user account
  private async addCreditsToUser(
    userId: string,
    packageId: string,
    credits: number,
    transactionId: string,
    orderData: any
  ): Promise<void> {
    const batch = this.db.batch();

    // Mark order as processed to prevent duplicates
    const processedOrderRef = this.db.collection('processed_lemonsqueezy_orders').doc(transactionId);
    batch.set(processedOrderRef, {
      orderId: transactionId,
      userId,
      packageId,
      credits,
      processedAt: admin.firestore.FieldValue.serverTimestamp(),
      orderData: {
        orderNumber: orderData.order_number,
        totalPaid: orderData.total_usd,
        currency: orderData.currency,
        userEmail: orderData.user_email
      }
    });

    // Update user credits
    const userCreditsRef = this.db.collection('users').doc(userId).collection('credits').doc('balance');
    
    // Get current credits
    const currentCreditsDoc = await userCreditsRef.get();
    const currentCredits = currentCreditsDoc.exists ? currentCreditsDoc.data() : {
      totalCredits: 0,
      lifetimeCredits: 0,
      lifetimeSpent: 0,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp()
    };

    // Update credits
    const newCredits = {
      totalCredits: (currentCredits?.totalCredits || 0) + credits,
      lifetimeCredits: (currentCredits?.lifetimeCredits || 0) + credits,
      lifetimeSpent: currentCredits?.lifetimeSpent || 0,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp()
    };

    batch.set(userCreditsRef, newCredits, { merge: true });

    // Create transaction record
    const transactionRef = this.db
      .collection('users')
      .doc(userId)
      .collection('credit_transactions')
      .doc();

    const transaction = {
      id: transactionRef.id,
      userId,
      type: 'purchase',
      amount: credits,
      balanceAfter: newCredits.totalCredits,
      metadata: {
        packageId,
        packageName: packageId.charAt(0).toUpperCase() + packageId.slice(1),
        provider: 'lemonsqueezy',
        transactionId,
        orderNumber: orderData.order_number,
        totalPaid: orderData.total_usd,
        currency: orderData.currency
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    };

    batch.set(transactionRef, transaction);

    // Commit the batch
    await batch.commit();

    logger.info(`Added ${credits} credits to user ${userId} for LemonSqueezy order ${transactionId}`);
  }

  // Handle LemonSqueezy webhook
  async handleWebhook(body: string, signature?: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Verify webhook signature if provided
      if (signature && !this.verifyWebhookSignature(body, signature)) {
        logger.error('Invalid webhook signature');
        return { success: false, error: 'Invalid signature' };
      }

      // Parse the webhook payload
      const event: LemonSqueezyWebhookEvent = JSON.parse(body);

      logger.info(`Processing LemonSqueezy webhook: ${event.meta.event_name}`, event.data.id);

      // Only process order_created events
      if (event.meta.event_name !== 'order_created') {
        logger.info(`Ignoring webhook event: ${event.meta.event_name}`);
        return { success: true };
      }

      // Check if order is paid
      if (event.data.attributes.status !== 'paid') {
        logger.info(`Order ${event.data.id} is not paid yet, status: ${event.data.attributes.status}`);
        return { success: true };
      }

      // Get user ID
      const userId = await this.getUserIdFromOrder(event);
      if (!userId) {
        logger.error(`Could not determine user ID for order ${event.data.id}`);
        return { success: false, error: 'Could not determine user ID' };
      }

      // Map product to credit package
      const productName = event.data.attributes.first_order_item.product_name;
      const packageInfo = this.mapProductToPackage(productName);
      
      if (!packageInfo) {
        logger.error(`Unknown product: ${productName}`);
        return { success: false, error: `Unknown product: ${productName}` };
      }

      // Check if we've already processed this order by storing processed orders
      const processedOrderRef = this.db.collection('processed_lemonsqueezy_orders').doc(event.data.id);
      const existingOrder = await processedOrderRef.get();

      if (existingOrder.exists) {
        logger.info(`Order ${event.data.id} already processed`);
        return { success: true };
      }

      // Add credits to user account
      await this.addCreditsToUser(
        userId,
        packageInfo.packageId,
        packageInfo.credits,
        event.data.id,
        event.data.attributes
      );

      return { success: true };

    } catch (error: any) {
      logger.error('Failed to handle LemonSqueezy webhook:', error);
      return {
        success: false,
        error: error.message || 'Failed to process webhook'
      };
    }
  }
}

// Singleton instance
let lemonSqueezyService: LemonSqueezyService;

function getLemonSqueezyService(): LemonSqueezyService {
  if (!lemonSqueezyService) {
    lemonSqueezyService = new LemonSqueezyService();
  }
  return lemonSqueezyService;
}

// Export the webhook handler
export const lemonSqueezyWebhook = functions.https.onRequest(async (req, res) => {
  corsHandler(req, res, async () => {
    try {
      // Get signature from X-Signature header (as per LemonSqueezy docs)
      const signature = req.headers['x-signature'] as string;

      // Use raw body for signature verification
      const body = req.rawBody ? req.rawBody.toString() : JSON.stringify(req.body);

      const result = await getLemonSqueezyService().handleWebhook(body, signature);

      if (result.success) {
        res.status(200).json({ success: true });
      } else {
        res.status(400).json({ success: false, error: result.error });
      }
    } catch (error: any) {
      logger.error('LemonSqueezy webhook error:', error);
      res.status(500).json({ success: false, error: 'Internal server error' });
    }
  });
});
