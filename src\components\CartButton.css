.cart-button {
  --color: var(--ion-color-primary);
  --padding-start: 8px;
  --padding-end: 8px;
  position: relative;
}

.cart-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-icon-container ion-icon {
  font-size: 24px;
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  min-width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: 700;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  animation: bounceIn 0.3s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.cart-button:hover {
  --color: var(--ion-color-primary-shade);
  transform: scale(1.05);
  transition: all 0.2s ease;
}

.cart-button:active {
  transform: scale(0.95);
}
