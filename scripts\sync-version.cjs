const fs = require('fs');
const path = require('path');

const packageJsonPath = path.join(__dirname, '../package.json');
const androidGradlePath = path.join(__dirname, '../android/app/build.gradle');
// Add iOS path if it exists later
// const iosPlistPath = path.join(__dirname, '../ios/App/App/Info.plist');

function syncVersion() {
    console.log('Syncing version from package.json to native projects...');

    // 1. Read package.json
    if (!fs.existsSync(packageJsonPath)) {
        console.error('package.json not found!');
        process.exit(1);
    }
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const version = packageJson.version;
    console.log(`Current version: ${version}`);

    // Clean version for code (remove non-digits for simplicity or use a counter)
    const [major, minor, patch] = version.split('.').map(Number);
    // Simple version code strategy: major * 10000 + minor * 100 + patch
    // e.g. 0.1.0 -> 100
    // e.g. 1.2.3 -> 10203
    const versionCode = major * 10000 + minor * 100 + patch;

    // 2. Update Android
    if (fs.existsSync(androidGradlePath)) {
        let gradleContent = fs.readFileSync(androidGradlePath, 'utf8');
        
        // Update versionName
        gradleContent = gradleContent.replace(/versionName "[^"]*"/, `versionName "${version}"`);
        
        // Update versionCode
        // Note: This regex assumes standard format. Adjust if needed.
        gradleContent = gradleContent.replace(/versionCode \d+/, `versionCode ${versionCode}`);
        
        fs.writeFileSync(androidGradlePath, gradleContent);
        console.log(`Updated Android build.gradle to version ${version} (code ${versionCode})`);
    } else {
        console.warn('Android build.gradle not found, skipping Android sync.');
    }

    // 3. Update iOS (Placeholder)
    // console.log('Checking for iOS...');
}

syncVersion();
