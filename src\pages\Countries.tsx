import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonPage,
  IonList,
  IonItem,
  IonLabel,
  IonAvatar,
  IonBadge,
  IonSearchbar,
  IonRefresher,
  IonRefresherContent,
  IonSpinner,
  IonText,
  IonSegment,
  IonSegmentButton,
  IonIcon
} from '@ionic/react';
import { funnel, grid } from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { CreditsConversionService } from '../services/creditsConversionService';
import AppBar from '../components/AppBar';
import './Countries.css';

interface ESimPlan {
  ID: number;
  countryCode: string;
  name: string;
  priceUsd: string;
  dataInGb: number;
  speed: string;
}

const Countries: React.FC = () => {
  const { t } = useTranslation();
  const [countries, setCountries] = useState<ESimPlan[]>([]);
  const [filteredCountries, setFilteredCountries] = useState<ESimPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'data'>('name');
  const [sortDirection, setSortDirection] = useState<{[key: string]: 'asc' | 'desc'}>({
    price: 'asc', // Default: low to high
    data: 'desc'  // Default: high to low
  });
  const history = useHistory();
  
  // Check if we're in development mode (disable for mobile)
  const isDevelopment = import.meta.env.DEV && window.location.protocol !== 'capacitor:';

  const fetchCountries = async () => {
    try {
      setLoading(true);
      const response = await fetch('https://us-central1-esim-numero.cloudfunctions.net/getESimPricing');
      const data = await response.json();
      
      console.log('Full API Response:', data);
      console.log('First country object:', data.data?.[0]);
      console.log('All field names in first country:', Object.keys(data.data?.[0] || {}));
      
      setCountries(data.data || []);
      setFilteredCountries(data.data || []);
    } catch (error) {
      console.error('Error fetching countries:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCountries();
  }, []);

  useEffect(() => {
    if (isDevelopment) {
      console.log('Sample country data:', countries[0]); // Debug first country
      console.log('All countries sample:', countries.slice(0, 3)); // Debug first 3 countries
    }
    
    let filtered = countries.filter(country => {
      // More lenient filtering - just check if country exists
      if (!country) {
        return false;
      }
      
      // If no search text, show all countries
      if (!searchText) {
        return true;
      }
      
      const searchLower = searchText.toLowerCase();
      const countryName = country.name || '';
      const countryCode = country.countryCode || '';
      
      return countryName.toLowerCase().includes(searchLower) ||
             countryCode.toLowerCase().includes(searchLower);
    });
    
    // Apply sorting
    filtered = filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return (a.name || '').localeCompare(b.name || '');
        case 'price':
          const priceA = parseFloat(a.priceUsd || '0');
          const priceB = parseFloat(b.priceUsd || '0');
          return sortDirection.price === 'asc' ? priceA - priceB : priceB - priceA;
        case 'data':
          const dataA = a.dataInGb || 0;
          const dataB = b.dataInGb || 0;
          return sortDirection.data === 'asc' ? dataA - dataB : dataB - dataA;
        default:
          return 0;
      }
    });
    
    if (isDevelopment) {
      console.log('Filtered countries:', filtered.length);
    }
    setFilteredCountries(filtered);
  }, [searchText, countries, sortBy, sortDirection, isDevelopment]);

  const getFlagUrl = (countryCode: string) => {
    return `https://flagcdn.com/w40/${countryCode.toLowerCase()}.png`;
  };

  const handleCountryClick = (planId: number, countryName: string) => {
    history.push(`/plans/${planId}?country=${encodeURIComponent(countryName)}`);
  };

  const refresh = async (e: CustomEvent) => {
    await fetchCountries();
    e.detail.complete();
  };

  return (
    <IonPage>
      <AppBar title={t('countries.title')} />
      
      <IonContent fullscreen>
        <IonRefresher slot="fixed" onIonRefresh={refresh}>
          <IonRefresherContent></IonRefresherContent>
        </IonRefresher>

        <IonSearchbar
          value={searchText}
          onIonInput={(e) => setSearchText(e.detail.value!)}
          placeholder={t('countries.searchPlaceholder')}
          showClearButton="focus"
        />

        {/* Debug info - only in development */}
        {isDevelopment && (
          <div style={{padding: '10px', fontSize: '12px', color: 'gray', background: '#f0f0f0', margin: '10px', borderRadius: '4px'}}>
            🐛 Debug: Loading: {loading.toString()}, Countries: {countries.length}, Filtered: {filteredCountries.length}, Sort: {sortBy}
          </div>
        )}

        {/* Filter Controls */}
        <div className="filter-container">
          <IonSegment value={sortBy}>
            <IonSegmentButton value="name" onClick={() => setSortBy('name')}>
              <IonIcon icon={grid} />
              <IonLabel>{t('countries.filterName')}</IonLabel>
            </IonSegmentButton>
            <IonSegmentButton value="price" onClick={() => {
              if (sortBy === 'price') {
                setSortDirection(prev => ({
                  ...prev,
                  price: prev.price === 'asc' ? 'desc' : 'asc'
                }));
              } else {
                setSortBy('price');
              }
            }}>
              <IonIcon icon={funnel} />
              <IonLabel>
                {t('countries.filterPrice')} 
                <span style={{opacity: sortBy === 'price' && sortDirection.price === 'asc' ? 1 : 0.3}}>↑</span>
                <span style={{opacity: sortBy === 'price' && sortDirection.price === 'desc' ? 1 : 0.3}}>↓</span>
              </IonLabel>
            </IonSegmentButton>
            <IonSegmentButton value="data" onClick={() => {
              if (sortBy === 'data') {
                setSortDirection(prev => ({
                  ...prev,
                  data: prev.data === 'asc' ? 'desc' : 'asc'
                }));
              } else {
                setSortBy('data');
              }
            }}>
              <IonIcon icon={funnel} />
              <IonLabel>
                {t('countries.filterData')} 
                <span style={{opacity: sortBy === 'data' && sortDirection.data === 'asc' ? 1 : 0.3}}>↑</span>
                <span style={{opacity: sortBy === 'data' && sortDirection.data === 'desc' ? 1 : 0.3}}>↓</span>
              </IonLabel>
            </IonSegmentButton>
          </IonSegment>
        </div>

        {loading ? (
          <div className="loading-container">
            <IonSpinner name="crescent" />
            <IonText>{t('countries.loadingCountries')}</IonText>
          </div>
        ) : filteredCountries.length === 0 ? (
          <div className="loading-container">
            <IonText>{t('countries.noCountries')}</IonText>
          </div>
        ) : (
          <IonList>
            {filteredCountries.map((country, index) => (
              <IonItem
                key={`${country.ID}-${country.countryCode}-${index}`}
                button
                onClick={() => handleCountryClick(country.ID, country.name)}
              >
                <IonAvatar slot="start">
                  <img
                    src={getFlagUrl(country.countryCode)}
                    alt={`${country.name} flag`}
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/assets/icon/default-flag.png';
                    }}
                  />
                </IonAvatar>
                
                <IonLabel>
                  <h2>{country.name || t('countries.unknownCountry')}</h2>
                  <p>{country.dataInGb || 0}GB • {country.speed || t('countries.unknownSpeed')}</p>
                </IonLabel>
                
                <IonBadge color="secondary" slot="end">
                  {CreditsConversionService.formatCredits(
                    CreditsConversionService.convertUsdToCredits(country.priceUsd)
                  )}
                </IonBadge>
              </IonItem>
            ))}
          </IonList>
        )}
      </IonContent>
    </IonPage>
  );
};

export default Countries;













