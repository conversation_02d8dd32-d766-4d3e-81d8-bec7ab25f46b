import React from 'react';
import {
  IonContent,
  IonItem,
  IonLabel,
  IonList,
  IonListHeader,
  IonMenu,
  IonMenuToggle,
  IonIcon,
  IonAvatar,
  IonText
} from '@ionic/react';
import {
  person,
  settings,
  document,
  shield,
  logOut,
  globe,
  wallet,
  card,
  // Add an icon for login/registration if needed, e.g., logIn
} from 'ionicons/icons';
import { useAuth } from '../contexts/AuthContext';
import { useHistory, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
// import ThemeToggle from './ThemeToggle';

const Sidebar: React.FC = () => {
  const { currentUser, logout } = useAuth();
  const history = useHistory();
  const { t } = useTranslation();
  
  // *** Placeholder for actual balance hook ***
  const creditBalance = '€25.50'; 

  const handleLogout = async () => {
    try {
      await logout();
      history.push('/login');
    } catch (error) {
      console.error('Failed to log out', error);
    }
  };

  /* Hide Sidebar on Landing Page */
  const location = useLocation();
  if (location.pathname === '/') {
    return null;
  }

  return (
    <IonMenu contentId="main-content" className="custom-sidebar"> {/* Added custom-sidebar class */}
      <IonContent scrollY={true} className="ion-padding-bottom">
        
        {/* === HEADER / USER PROFILE SECTION === */}
        {/* ... (This section remains largely the same) ... */}
        {currentUser ? (
          <div style={{ 
            padding: '20px', 
            background: 'var(--ion-color-step-50)',
            borderBottom: '1px solid var(--ion-color-step-150)',
            marginBottom: '10px' 
          }}>
            <h1 style={{ fontSize: '1.4rem', fontWeight: '800', marginBottom: '15px' }}>
              KrakenSim
            </h1>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <IonAvatar style={{ width: '48px', height: '48px' }}>
                <img src={currentUser.photoURL || '/assets/icon/default-avatar.svg'} alt="User" />
              </IonAvatar>
              <div>
                <IonText>
                  <h3 style={{ margin: '0', fontSize: '16px', fontWeight: '600' }}>
                    {currentUser.displayName || 'User'}
                  </h3>
                  <p style={{ margin: '0', fontSize: '14px', color: 'var(--ion-color-medium)' }}>
                    {currentUser.email}
                  </p>
                </IonText>
              </div>
            </div>
          </div>
        ) : (
          <div style={{ padding: '20px', paddingBottom: '10px' }}>
            <h1 style={{ fontSize: '1.4rem', fontWeight: '800' }}>
              KrakenSim
            </h1>
          </div>
        )}

        {/* === CORE NAVIGATION === */}
        <IonList lines="full"> 
          <IonMenuToggle autoHide={true}>
            <IonItem button routerLink="/countries">
              <IonIcon slot="start" icon={globe} color="primary" /> {/* Changed to primary */}
              <IonLabel>{t('nav.countries')}</IonLabel>
            </IonItem>
          </IonMenuToggle>

          {currentUser && (
            <IonMenuToggle autoHide={true}>
              <IonItem button routerLink="/my-esims">
                <IonIcon slot="start" icon={card} color="primary" /> {/* Changed to primary */}
                <IonLabel>{t('nav.my_esims')}</IonLabel>
              </IonItem>
            </IonMenuToggle>
          )}

          {currentUser && (
            <IonMenuToggle autoHide={true}>
              <IonItem button routerLink="/credits">
                <IonIcon slot="start" icon={wallet} color="primary" />
                <IonLabel>{t('nav.credits')}</IonLabel>
              </IonItem>
            </IonMenuToggle>
          )}
        </IonList>

        {/* === ACCOUNT SECTION === */}
        <IonList lines="full">
          <IonListHeader>
            <IonLabel>Account</IonLabel>
          </IonListHeader>
          
          {currentUser ? (
            <>
              <IonMenuToggle autoHide={true}>
                <IonItem button routerLink="/profile">
                  <IonIcon slot="start" icon={person} color="primary" /> {/* Changed to primary */}
                  <IonLabel>{t('nav.profile')}</IonLabel>
                </IonItem>
              </IonMenuToggle>

              <IonMenuToggle autoHide={true}>
                <IonItem button routerLink="/settings">
                  <IonIcon slot="start" icon={settings} color="primary" /> {/* Changed to primary */}
                  <IonLabel>{t('nav.settings')}</IonLabel>
                </IonItem>
              </IonMenuToggle>

              <IonMenuToggle autoHide={true}>
                <IonItem button onClick={handleLogout} detail={false}>
                  <IonIcon slot="start" icon={logOut} color="danger" />
                  <IonLabel color="danger">{t('nav.logout')}</IonLabel>
                </IonItem>
              </IonMenuToggle>
            </>
          ) : (
            <IonMenuToggle autoHide={true}>
              <IonItem button routerLink="/login">
                <IonIcon slot="start" icon={person} color="primary" />
                <IonLabel>{t('nav.login')}</IonLabel>
              </IonItem>
            </IonMenuToggle>
          )}
        </IonList>


      </IonContent>
    </IonMenu>
  );
  
};

export default Sidebar;