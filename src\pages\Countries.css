.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
}

.loading-container ion-text {
  color: var(--ion-color-medium);
}

ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
}

ion-avatar {
  width: 40px;
  height: 40px;
}

ion-avatar img {
  border-radius: 4px;
  object-fit: cover;
}

ion-badge {
  font-weight: 600;
  font-size: 0.9rem;
}

ion-label h2 {
  font-weight: 600;
  margin-bottom: 4px;
}

ion-label p {
  color: var(--ion-color-medium);
  font-size: 0.85rem;
}

.filter-container {
  padding: 16px;
  background: var(--app-surface) !important;
  border-bottom: 1px solid var(--app-border);
  margin: 0;
}

.filter-container ion-segment {
  --background: var(--app-background) !important;
  border-radius: 16px;
  padding: 4px;
  box-shadow: 0 2px 8px var(--app-shadow);
  border: 1px solid var(--app-border);
}

.filter-container ion-segment-button {
  --color: var(--app-text-secondary) !important;
  --color-checked: var(--ion-color-primary-contrast) !important;
  --background-checked: var(--ion-color-primary) !important;
  --border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 2px;
}

.filter-container ion-segment-button:hover {
  --color: var(--app-text-primary) !important;
}

.filter-container ion-segment-button ion-icon {
  font-size: 1rem;
  margin-bottom: 4px;
}

.filter-container ion-segment-button ion-label {
  margin-top: 0;
  font-size: 0.8rem;
}