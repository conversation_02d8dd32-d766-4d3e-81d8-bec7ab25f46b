import React from 'react';
import { Ion<PERSON>tem, IonLabel, IonNote } from '@ionic/react';
import { Message } from '../data/messages';

interface MessageListItemProps {
  message: Message;
}

const MessageListItem: React.FC<MessageListItemProps> = ({ message }) => {
  return (
    <IonItem routerLink={`/message/${message.id}`}>
      <IonLabel>
        <h2>{message.fromName}</h2>
        <h3>{message.subject}</h3>
      </IonLabel>
      <IonNote slot="end" color="medium">
        {message.date}
      </IonNote>
    </IonItem>
  );
};

export default MessageListItem;
