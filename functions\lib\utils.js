"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PricingUtils = void 0;
class PricingUtils {
    static getESimProfitMargin() {
        return parseFloat(process.env.ESIM_PROFIT_MARGIN || '1.5');
    }
    static getDataPlanProfitMargin() {
        return parseFloat(process.env.DATA_PLAN_PROFIT_MARGIN || '1.5');
    }
    static calculateESimPrice(costUsd) {
        const cost = parseFloat(costUsd);
        if (isNaN(cost)) {
            console.error('Invalid cost value:', costUsd);
            return 0;
        }
        return Math.round((cost * this.getESimProfitMargin()) * 100) / 100;
    }
    static calculateDataPlanPrice(costUsd) {
        const cost = parseFloat(costUsd);
        if (isNaN(cost)) {
            console.error('Invalid cost value:', costUsd);
            return 0;
        }
        return Math.round((cost * this.getDataPlanProfitMargin()) * 100) / 100;
    }
    static formatPrice(price) {
        return `$${price.toFixed(2)}`;
    }
}
exports.PricingUtils = PricingUtils;
//# sourceMappingURL=utils.js.map