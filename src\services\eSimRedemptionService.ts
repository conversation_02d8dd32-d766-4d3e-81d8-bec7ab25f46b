import { auth } from '../firebase';
import { ESimOrder, ESimTopup } from '../types/credits';

interface ESimRedemptionRequest {
  planId: number;
  countryCode: string;
  countryName: string;
  dataInGb: number;
  durationDays: number;
  creditCost: number;
  originalPriceUsd: number;
}

interface ESimTopupRequest {
  esimOrderId: string;
  dataInGb: number;
  creditCost: number;
}

class ESimRedemptionApiService {
  private getFunctionsBaseUrl(): string {
    // Use local emulator for development
    // if (window.location.hostname === 'localhost') {
    //   return 'http://127.0.0.1:5002/esim-numero/us-central1';
    // }
    // Use production Firebase Functions
    return 'https://us-central1-esim-numero.cloudfunctions.net';
  }

  // Redeem credits for eSIM
  async redeemCreditsForESim(request: ESimRedemptionRequest): Promise<{
    success: boolean;
    orderId?: string;
    error?: string;
  }> {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    try {
      const response = await fetch(`${this.getFunctionsBaseUrl()}/redeemCreditsForESim`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await user.getIdToken()}`
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error: any) {
      console.error('Error redeeming credits for eSIM:', error);
      return { success: false, error: error.message || 'Failed to redeem credits' };
    }
  }

  // Top up existing eSIM
  async topupESim(request: ESimTopupRequest): Promise<{
    success: boolean;
    topupId?: string;
    error?: string;
  }> {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    try {
      const response = await fetch(`${this.getFunctionsBaseUrl()}/topupESimCredits`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await user.getIdToken()}`
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error: any) {
      console.error('Error topping up eSIM:', error);
      return { success: false, error: error.message || 'Failed to top up eSIM' };
    }
  }

  // Get user's eSIM orders
  async getUserESimOrders(): Promise<ESimOrder[]> {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    try {
      const response = await fetch(`${this.getFunctionsBaseUrl()}/getUserESimOrders`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${await user.getIdToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.orders || [];
    } catch (error: any) {
      console.error('Error fetching eSIM orders:', error);
      return [];
    }
  }

  // Get user's eSIM topups
  async getUserESimTopups(): Promise<ESimTopup[]> {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    try {
      const response = await fetch(`${this.getFunctionsBaseUrl()}/getUserESimTopups`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${await user.getIdToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.topups || [];
    } catch (error: any) {
      console.error('Error fetching eSIM topups:', error);
      return [];
    }
  }

  // Get eSIM order details
  async getESimOrderDetails(orderId: string): Promise<ESimOrder | null> {
    const user = auth.currentUser;
    if (!user) throw new Error('Authentication required');

    try {
      const response = await fetch(`${this.getFunctionsBaseUrl()}/getESimOrderDetails`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await user.getIdToken()}`
        },
        body: JSON.stringify({ orderId })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.order || null;
    } catch (error: any) {
      console.error('Error fetching eSIM order details:', error);
      return null;
    }
  }

  // Calculate credit cost for a plan
  calculateCreditCost(priceUsd: number): number {
    // Simple conversion: 1 USD = 10 credits
    // You can make this more sophisticated based on your business model
    return Math.ceil(priceUsd * 10);
  }

  // Check if user has sufficient credits
  async checkSufficientCredits(requiredCredits: number): Promise<{
    sufficient: boolean;
    currentCredits: number;
    shortfall: number;
  }> {
    try {
      // This would typically call the getUserCredits API
      // For now, we'll use the existing creditsApiService
      const { creditsApiService } = await import('./creditsApiService');
      const userCredits = await creditsApiService.getUserCredits();
      
      const currentCredits = userCredits?.totalCredits || 0;
      const sufficient = currentCredits >= requiredCredits;
      const shortfall = sufficient ? 0 : requiredCredits - currentCredits;

      return {
        sufficient,
        currentCredits,
        shortfall
      };
    } catch (error) {
      console.error('Error checking credit balance:', error);
      return {
        sufficient: false,
        currentCredits: 0,
        shortfall: requiredCredits
      };
    }
  }

  // Get recommended credit package for shortfall
  async getRecommendedCreditPackage(shortfall: number): Promise<string> {
    try {
      // Import the credits service to get actual packages
      const { creditsApiService } = await import('./creditsApiService');
      const packages = await creditsApiService.getCreditPackages();

      // Sort packages by credits (ascending) to find the smallest that covers shortfall
      const sortedPackages = packages
        .filter(pkg => pkg.active && pkg.credits >= shortfall)
        .sort((a, b) => a.credits - b.credits);

      // Return the smallest package that covers the shortfall
      if (sortedPackages.length > 0) {
        return sortedPackages[0].id;
      }

      // If no package covers the shortfall, return the largest package
      const largestPackage = packages
        .filter(pkg => pkg.active)
        .sort((a, b) => b.credits - a.credits)[0];

      return largestPackage?.id || 'whale';
    } catch (error) {
      console.error('Error getting recommended package:', error);
      // Fallback to static logic
      if (shortfall <= 100) return 'minnow';
      if (shortfall <= 250) return 'tuna';
      if (shortfall <= 500) return 'dolphin';
      if (shortfall <= 1000) return 'octopus';
      if (shortfall <= 1500) return 'shark';
      return 'whale';
    }
  }

  // Format credit cost for display
  formatCreditCost(credits: number): string {
    return `${credits} credits`;
  }

  // Format data amount for display
  formatDataAmount(dataInGb: number): string {
    if (dataInGb < 1) {
      return `${Math.round(dataInGb * 1000)}MB`;
    }
    return `${dataInGb}GB`;
  }

  // Format duration for display
  formatDuration(durationDays: number): string {
    if (durationDays === 1) return '1 day';
    if (durationDays < 30) return `${durationDays} days`;
    if (durationDays === 30) return '1 month';
    if (durationDays < 365) return `${Math.round(durationDays / 30)} months`;
    return `${Math.round(durationDays / 365)} year${durationDays >= 730 ? 's' : ''}`;
  }

  // Get status color for UI
  getStatusColor(status: string): string {
    switch (status) {
      case 'pending': return 'warning';
      case 'provisioned': return 'primary';
      case 'activated': return 'success';
      case 'expired': return 'medium';
      case 'cancelled': return 'danger';
      default: return 'medium';
    }
  }

  // Get status icon for UI
  getStatusIcon(status: string): string {
    switch (status) {
      case 'pending': return 'time-outline';
      case 'provisioned': return 'checkmark-circle-outline';
      case 'activated': return 'cellular-outline';
      case 'expired': return 'alert-circle-outline';
      case 'cancelled': return 'close-circle-outline';
      default: return 'help-circle-outline';
    }
  }
}

export const eSimRedemptionService = new ESimRedemptionApiService();
