"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.withCors = void 0;
const functions = require("firebase-functions");
const withCors = (handler) => {
    return functions.https.onRequest(async (req, res) => {
        // Set CORS headers for all requests
        res.set('Access-Control-Allow-Origin', '*');
        res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.set('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept, Authorization, X-Requested-With');
        res.set('Access-Control-Max-Age', '3600');
        // Handle preflight requests
        if (req.method === 'OPTIONS') {
            res.status(204).send('');
            return;
        }
        try {
            await handler(req, res);
        }
        catch (error) {
            console.error('Function error:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    });
};
exports.withCors = withCors;
//# sourceMappingURL=cors.js.map