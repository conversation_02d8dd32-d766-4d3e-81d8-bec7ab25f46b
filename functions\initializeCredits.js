#!/usr/bin/env node

/**
 * Credits System Initialization Script
 * 
 * This script initializes the credits-based system by:
 * 1. Creating credit packages in Firestore
 * 2. Setting up eSIM credit costs
 * 
 * Usage: node initializeCredits.js
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin with default credentials
try {
  admin.initializeApp({
    projectId: 'esim-numero'
  });
  console.log('✅ Firebase Admin initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin:', error);
  process.exit(1);
}

const db = admin.firestore();

// Credit packages data
const CREDIT_PACKAGES = [
  {
    id: 'minnow',
    name: 'Minnow',
    animal: '🐟 Minnow',
    price: 5,
    credits: 5,
    vibe: 'Tiny but mighty',
    useCase: 'Light travelers, test users',
    lemonSqueezyVariantId: '',
    lemonSqueezyProductId: '',
    active: true
  },
  {
    id: 'tuna',
    name: '<PERSON>na',
    animal: '🐟 Tuna',
    price: 10,
    credits: 10,
    vibe: 'Reliable and steady',
    useCase: 'Regional users',
    lemonSqueezyVariantId: '',
    lemonSqueezyProductId: '',
    active: true
  },
  {
    id: 'dolphin',
    name: 'Dolphin',
    animal: '🐬 Dolphin',
    price: 20,
    credits: 20,
    vibe: 'Smart and fast',
    useCase: 'Business travelers',
    lemonSqueezyVariantId: '',
    lemonSqueezyProductId: '',
    active: true
  },
  {
    id: 'octopus',
    name: 'Octopus',
    animal: '🐙 Octopus',
    price: 35,
    credits: 35,
    vibe: 'Flexible, multi-device',
    useCase: 'Remote workers',
    lemonSqueezyVariantId: '',
    lemonSqueezyProductId: '',
    active: true
  },
  {
    id: 'shark',
    name: 'Shark',
    animal: '🦈 Shark',
    price: 50,
    credits: 50,
    vibe: 'Powerful, premium',
    useCase: 'Heavy data users',
    lemonSqueezyVariantId: '',
    lemonSqueezyProductId: '',
    active: true
  },
  {
    id: 'whale',
    name: 'Whale',
    animal: '🐋 Whale',
    price: 75,
    credits: 75,
    vibe: 'Massive, enterprise-grade',
    useCase: 'Global nomads, teams',
    lemonSqueezyVariantId: '',
    lemonSqueezyProductId: '',
    active: true
  }
];

// Sample eSIM credit costs
const SAMPLE_ESIM_COSTS = [
  {
    id: 'usa_1gb',
    planId: 1,
    countryCode: 'USA',
    countryName: 'United States',
    dataInGb: 1,
    creditCost: 8,
    originalPriceUsd: 7.99,
    active: true
  },
  {
    id: 'uk_2gb',
    planId: 2,
    countryCode: 'UK',
    countryName: 'United Kingdom',
    dataInGb: 2,
    creditCost: 12,
    originalPriceUsd: 11.99,
    active: true
  },
  {
    id: 'japan_3gb',
    planId: 3,
    countryCode: 'JP',
    countryName: 'Japan',
    dataInGb: 3,
    creditCost: 15,
    originalPriceUsd: 14.99,
    active: true
  }
];

async function initializeCreditPackages() {
  console.log('🚀 Initializing credit packages...');
  
  const batch = db.batch();
  const now = new Date();
  
  for (const packageData of CREDIT_PACKAGES) {
    const packageRef = db.collection('credit_packages').doc(packageData.id);
    
    const fullPackageData = {
      ...packageData,
      createdAt: now,
      updatedAt: now
    };
    
    batch.set(packageRef, fullPackageData);
    console.log(`  ✅ Added package: ${packageData.animal} - $${packageData.price} (${packageData.credits} credits)`);
  }
  
  await batch.commit();
  console.log('✅ Credit packages initialized successfully!');
}

async function initializeESimCreditCosts() {
  console.log('🚀 Initializing eSIM credit costs...');
  
  const batch = db.batch();
  const now = new Date();
  
  for (const costData of SAMPLE_ESIM_COSTS) {
    const costRef = db.collection('esim_credit_costs').doc(costData.id);
    
    batch.set(costRef, {
      ...costData,
      createdAt: now,
      updatedAt: now
    });
    
    console.log(`  ✅ Added eSIM cost: ${costData.countryName} ${costData.dataInGb}GB - ${costData.creditCost} credits`);
  }
  
  await batch.commit();
  console.log('✅ eSIM credit costs initialized successfully!');
}

async function checkExistingData() {
  console.log('🔍 Checking for existing data...');
  
  const packagesSnapshot = await db.collection('credit_packages').limit(1).get();
  const costsSnapshot = await db.collection('esim_credit_costs').limit(1).get();
  
  return {
    hasPackages: !packagesSnapshot.empty,
    hasCosts: !costsSnapshot.empty
  };
}

async function main() {
  try {
    console.log('🎯 Credits System Initialization Starting...\n');
    
    const existing = await checkExistingData();
    
    if (existing.hasPackages) {
      console.log('⚠️  Credit packages already exist. Skipping package initialization.');
    } else {
      await initializeCreditPackages();
    }
    
    if (existing.hasCosts) {
      console.log('⚠️  eSIM credit costs already exist. Skipping cost initialization.');
    } else {
      await initializeESimCreditCosts();
    }
    
    console.log('\n🎉 Credits system initialization complete!');
    console.log('\n📋 Next Steps:');
    console.log('1. Create LemonSqueezy products for each credit package');
    console.log('2. Update credit packages with LemonSqueezy variant IDs');
    console.log('3. Test the purchase flow');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Initialization failed:', error);
    process.exit(1);
  }
}

// Run the initialization
main();
