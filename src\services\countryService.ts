// Country code to name mapping service
export class CountryService {
  private static countryMap: Record<string, string> = {
    // North America
    'US': 'United States',
    'CA': 'Canada',
    'MX': 'Mexico',
    'GT': 'Guatemala',
    'BZ': 'Belize',
    'SV': 'El Salvador',
    'HN': 'Honduras',
    'NI': 'Nicaragua',
    'CR': 'Costa Rica',
    'PA': 'Panama',
    
    // Caribbean
    'CU': 'Cuba',
    'JM': 'Jamaica',
    'HT': 'Haiti',
    'DO': 'Dominican Republic',
    'PR': 'Puerto Rico',
    'TT': 'Trinidad and Tobago',
    'BB': 'Barbados',
    'GD': 'Grenada',
    'LC': 'Saint Lucia',
    'VC': 'Saint Vincent and the Grenadines',
    'AG': 'Antigua and Barbuda',
    'KN': 'Saint Kitts and Nevis',
    'DM': 'Dominica',
    'BS': 'Bahamas',
    
    // South America
    'BR': 'Brazil',
    'AR': 'Argentina',
    'CL': 'Chile',
    'CO': 'Colombia',
    'PE': 'Peru',
    'VE': 'Venezuela',
    'EC': 'Ecuador',
    'UY': 'Uruguay',
    'PY': 'Paraguay',
    'BO': 'Bolivia',
    'GY': 'Guyana',
    'SR': 'Suriname',
    'GF': 'French Guiana',
    
    // Europe
    'GB': 'United Kingdom',
    'FR': 'France',
    'DE': 'Germany',
    'IT': 'Italy',
    'ES': 'Spain',
    'PT': 'Portugal',
    'NL': 'Netherlands',
    'BE': 'Belgium',
    'CH': 'Switzerland',
    'AT': 'Austria',
    'SE': 'Sweden',
    'NO': 'Norway',
    'DK': 'Denmark',
    'FI': 'Finland',
    'IS': 'Iceland',
    'IE': 'Ireland',
    'PL': 'Poland',
    'CZ': 'Czech Republic',
    'SK': 'Slovakia',
    'HU': 'Hungary',
    'RO': 'Romania',
    'BG': 'Bulgaria',
    'GR': 'Greece',
    'HR': 'Croatia',
    'SI': 'Slovenia',
    'RS': 'Serbia',
    'BA': 'Bosnia and Herzegovina',
    'ME': 'Montenegro',
    'MK': 'North Macedonia',
    'AL': 'Albania',
    'XK': 'Kosovo',
    'MD': 'Moldova',
    'UA': 'Ukraine',
    'BY': 'Belarus',
    'LT': 'Lithuania',
    'LV': 'Latvia',
    'EE': 'Estonia',
    'RU': 'Russia',
    'TR': 'Turkey',
    'CY': 'Cyprus',
    'MT': 'Malta',
    'LU': 'Luxembourg',
    'LI': 'Liechtenstein',
    'MC': 'Monaco',
    'AD': 'Andorra',
    'SM': 'San Marino',
    'VA': 'Vatican City',
    
    // Asia
    'CN': 'China',
    'JP': 'Japan',
    'KR': 'South Korea',
    'KP': 'North Korea',
    'IN': 'India',
    'PK': 'Pakistan',
    'BD': 'Bangladesh',
    'LK': 'Sri Lanka',
    'MV': 'Maldives',
    'NP': 'Nepal',
    'BT': 'Bhutan',
    'TH': 'Thailand',
    'VN': 'Vietnam',
    'LA': 'Laos',
    'KH': 'Cambodia',
    'MM': 'Myanmar',
    'MY': 'Malaysia',
    'SG': 'Singapore',
    'ID': 'Indonesia',
    'PH': 'Philippines',
    'BN': 'Brunei',
    'TL': 'East Timor',
    'MN': 'Mongolia',
    'KZ': 'Kazakhstan',
    'KG': 'Kyrgyzstan',
    'TJ': 'Tajikistan',
    'UZ': 'Uzbekistan',
    'TM': 'Turkmenistan',
    'AF': 'Afghanistan',
    'IR': 'Iran',
    'IQ': 'Iraq',
    'SY': 'Syria',
    'LB': 'Lebanon',
    'JO': 'Jordan',
    'IL': 'Israel',
    'PS': 'Palestine',
    'SA': 'Saudi Arabia',
    'AE': 'United Arab Emirates',
    'QA': 'Qatar',
    'BH': 'Bahrain',
    'KW': 'Kuwait',
    'OM': 'Oman',
    'YE': 'Yemen',
    'GE': 'Georgia',
    'AM': 'Armenia',
    'AZ': 'Azerbaijan',
    
    // Africa
    'EG': 'Egypt',
    'LY': 'Libya',
    'TN': 'Tunisia',
    'DZ': 'Algeria',
    'MA': 'Morocco',
    'SD': 'Sudan',
    'SS': 'South Sudan',
    'ET': 'Ethiopia',
    'ER': 'Eritrea',
    'DJ': 'Djibouti',
    'SO': 'Somalia',
    'KE': 'Kenya',
    'UG': 'Uganda',
    'TZ': 'Tanzania',
    'RW': 'Rwanda',
    'BI': 'Burundi',
    'CD': 'Democratic Republic of the Congo',
    'CG': 'Republic of the Congo',
    'CF': 'Central African Republic',
    'TD': 'Chad',
    'CM': 'Cameroon',
    'GQ': 'Equatorial Guinea',
    'GA': 'Gabon',
    'ST': 'São Tomé and Príncipe',
    'NG': 'Nigeria',
    'NE': 'Niger',
    'BF': 'Burkina Faso',
    'ML': 'Mali',
    'SN': 'Senegal',
    'MR': 'Mauritania',
    'GW': 'Guinea-Bissau',
    'GN': 'Guinea',
    'SL': 'Sierra Leone',
    'LR': 'Liberia',
    'CI': 'Côte d\'Ivoire',
    'GH': 'Ghana',
    'TG': 'Togo',
    'BJ': 'Benin',
    'ZA': 'South Africa',
    'NA': 'Namibia',
    'BW': 'Botswana',
    'ZW': 'Zimbabwe',
    'ZM': 'Zambia',
    'MW': 'Malawi',
    'MZ': 'Mozambique',
    'SZ': 'Eswatini',
    'LS': 'Lesotho',
    'MG': 'Madagascar',
    'MU': 'Mauritius',
    'SC': 'Seychelles',
    'KM': 'Comoros',
    'AO': 'Angola',
    
    // Oceania
    'AU': 'Australia',
    'NZ': 'New Zealand',
    'PG': 'Papua New Guinea',
    'FJ': 'Fiji',
    'SB': 'Solomon Islands',
    'VU': 'Vanuatu',
    'NC': 'New Caledonia',
    'PF': 'French Polynesia',
    'WS': 'Samoa',
    'TO': 'Tonga',
    'KI': 'Kiribati',
    'TV': 'Tuvalu',
    'NR': 'Nauru',
    'PW': 'Palau',
    'FM': 'Micronesia',
    'MH': 'Marshall Islands',
    'CK': 'Cook Islands',
    'NU': 'Niue',
    'TK': 'Tokelau',
    'AS': 'American Samoa',
    'GU': 'Guam',
    'MP': 'Northern Mariana Islands'
  };

  /**
   * Get country name from country code
   * @param countryCode ISO 2-letter country code
   * @returns Country name or the country code if not found
   */
  static getCountryName(countryCode: string): string {
    if (!countryCode) return 'Unknown Country';
    
    const upperCode = countryCode.toUpperCase();
    return this.countryMap[upperCode] || upperCode;
  }

  /**
   * Get all available countries
   * @returns Array of {code, name} objects
   */
  static getAllCountries(): Array<{code: string, name: string}> {
    return Object.entries(this.countryMap).map(([code, name]) => ({
      code,
      name
    }));
  }

  /**
   * Check if a country code is valid
   * @param countryCode ISO 2-letter country code
   * @returns true if valid, false otherwise
   */
  static isValidCountryCode(countryCode: string): boolean {
    if (!countryCode) return false;
    return countryCode.toUpperCase() in this.countryMap;
  }
}
