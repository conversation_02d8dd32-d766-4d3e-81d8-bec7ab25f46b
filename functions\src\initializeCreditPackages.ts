// import * as admin from 'firebase-admin';
// import { CreditPackage } from './types/credits';

// // Initialize Firebase Admin if not already done
// if (!admin.apps.length) {
//   admin.initializeApp();
// }

// const db = admin.firestore();

// // Credit packages data based on the image provided
// const CREDIT_PACKAGES: Omit<CreditPackage, 'createdAt' | 'updatedAt'>[] = [
//   {
//     id: 'minnow',
//     name: 'Minnow',
//     animal: '🐟 Minnow',
//     price: 5,
//     credits: 5,
//     vibe: 'Tiny but mighty',
//     useCase: 'Light travelers, test users',
//     stripePriceId: 'price_minnow_5_credits',
//     stripeProductId: 'prod_minnow_credits',
//     lemonSqueezyVariantId: '', // To be filled when LemonSqueezy products are created
//     lemonSqueezyProductId: '',
//     active: true
//   },
//   {
//     id: 'tuna',
//     name: '<PERSON>na',
//     animal: '🐟 Tuna',
//     price: 10,
//     credits: 10,
//     vibe: 'Reliable and steady',
//     useCase: 'Regional users',
//     stripePriceId: 'price_tuna_10_credits',
//     stripeProductId: 'prod_tuna_credits',
//     lemonSqueezyVariantId: '',
//     lemonSqueezyProductId: '',
//     active: true
//   },
//   {
//     id: 'dolphin',
//     name: 'Dolphin',
//     animal: '🐬 Dolphin',
//     price: 20,
//     credits: 20,
//     vibe: 'Smart and fast',
//     useCase: 'Business travelers',
//     stripePriceId: 'price_dolphin_20_credits',
//     stripeProductId: 'prod_dolphin_credits',
//     lemonSqueezyVariantId: '',
//     lemonSqueezyProductId: '',
//     active: true
//   },
//   {
//     id: 'octopus',
//     name: 'Octopus',
//     animal: '🐙 Octopus',
//     price: 35,
//     credits: 35,
//     vibe: 'Flexible, multi-device',
//     useCase: 'Remote workers',
//     stripePriceId: 'price_octopus_35_credits',
//     stripeProductId: 'prod_octopus_credits',
//     lemonSqueezyVariantId: '',
//     lemonSqueezyProductId: '',
//     active: true
//   },
//   {
//     id: 'shark',
//     name: 'Shark',
//     animal: '🦈 Shark',
//     price: 50,
//     credits: 50,
//     vibe: 'Powerful, premium',
//     useCase: 'Heavy data users',
//     stripePriceId: 'price_shark_50_credits',
//     stripeProductId: 'prod_shark_credits',
//     lemonSqueezyVariantId: '',
//     lemonSqueezyProductId: '',
//     active: true
//   },
//   {
//     id: 'whale',
//     name: 'Whale',
//     animal: '🐋 Whale',
//     price: 75,
//     credits: 75,
//     vibe: 'Massive, enterprise-grade',
//     useCase: 'Global nomads, teams',
//     stripePriceId: 'price_whale_75_credits',
//     stripeProductId: 'prod_whale_credits',
//     lemonSqueezyVariantId: '',
//     lemonSqueezyProductId: '',
//     active: true
//   }
// ];

// export async function initializeCreditPackages(): Promise<void> {
//   console.log('Initializing credit packages...');
  
//   const batch = db.batch();
//   const now = new Date();
  
//   for (const packageData of CREDIT_PACKAGES) {
//     const packageRef = db.collection('credit_packages').doc(packageData.id);
    
//     const fullPackageData: CreditPackage = {
//       ...packageData,
//       createdAt: now,
//       updatedAt: now
//     };
    
//     batch.set(packageRef, fullPackageData);
//     console.log(`Added package: ${packageData.animal} - $${packageData.price} (${packageData.credits} credits)`);
//   }
  
//   await batch.commit();
//   console.log('✅ Credit packages initialized successfully!');
// }

// export async function updateLemonSqueezyIds(packageId: string, variantId: string, productId: string): Promise<void> {
//   const packageRef = db.collection('credit_packages').doc(packageId);
  
//   await packageRef.update({
//     lemonSqueezyVariantId: variantId,
//     lemonSqueezyProductId: productId,
//     updatedAt: new Date()
//   });
  
//   console.log(`✅ Updated LemonSqueezy IDs for ${packageId}: variant=${variantId}, product=${productId}`);
// }

// // Helper function to calculate credit costs for existing eSIM plans
// export async function calculateESimCreditCosts(): Promise<void> {
//   console.log('Calculating eSIM credit costs...');
  
//   // This would typically fetch existing eSIM plans and calculate credit costs
//   // For now, we'll create a placeholder structure
  
//   const sampleESimCosts = [
//     {
//       id: 'usa_1gb',
//       planId: 1,
//       countryCode: 'USA',
//       countryName: 'United States',
//       dataInGb: 1,
//       creditCost: 8,  // $8 USD equivalent
//       originalPriceUsd: 7.99,
//       active: true
//     },
//     {
//       id: 'uk_2gb',
//       planId: 2,
//       countryCode: 'UK',
//       countryName: 'United Kingdom',
//       dataInGb: 2,
//       creditCost: 12, // $12 USD equivalent
//       originalPriceUsd: 11.99,
//       active: true
//     },
//     {
//       id: 'japan_3gb',
//       planId: 3,
//       countryCode: 'JP',
//       countryName: 'Japan',
//       dataInGb: 3,
//       creditCost: 15, // $15 USD equivalent
//       originalPriceUsd: 14.99,
//       active: true
//     }
//   ];
  
//   const batch = db.batch();
//   const now = new Date();
  
//   for (const costData of sampleESimCosts) {
//     const costRef = db.collection('esim_credit_costs').doc(costData.id);
    
//     batch.set(costRef, {
//       ...costData,
//       createdAt: now,
//       updatedAt: now
//     });
//   }
  
//   await batch.commit();
//   console.log('✅ eSIM credit costs calculated and stored!');
// }

// // Function to initialize a user's credit balance
// export async function initializeUserCredits(userId: string): Promise<void> {
//   const userCreditsRef = db.collection('users').doc(userId).collection('credits').doc('balance');
  
//   const userCredits = {
//     userId,
//     totalCredits: 0,
//     lifetimeCredits: 0,
//     lifetimeSpent: 0,
//     lastUpdated: new Date(),
//     createdAt: new Date()
//   };
  
//   await userCreditsRef.set(userCredits);
//   console.log(`✅ Initialized credits for user: ${userId}`);
// }

// // Run initialization if this file is executed directly
// if (require.main === module) {
//   initializeCreditPackages()
//     .then(() => calculateESimCreditCosts())
//     .then(() => {
//       console.log('🎉 All initialization complete!');
//       process.exit(0);
//     })
//     .catch((error) => {
//       console.error('❌ Initialization failed:', error);
//       process.exit(1);
//     });
// }
