import { functions, auth } from '../firebase';
import { httpsCallable } from 'firebase/functions';
import { ESim, ESimProfile } from '../types/eSim';
import { Capacitor } from '@capacitor/core';
import { CapacitorAuthService } from './capacitorAuthService';
import { getFunctionsBaseUrl } from './apiConfig';

// Keep callable functions for those that still use them
const getESimProfile = httpsCallable(functions, 'getESimProfile');
const topUpESim = httpsCallable(functions, 'topUpESim');
const deleteESim = httpsCallable(functions, 'deleteESim');
const getESimHistory = httpsCallable(functions, 'getESimHistory');

// Helper method to get ID token from appropriate auth service
async function getIdToken(): Promise<string> {
  if (Capacitor.isNativePlatform()) {
    const token = await CapacitorAuthService.getIdToken();
    if (!token) {
      throw new Error('Failed to get ID token');
    }
    return token;
  } else {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('Authentication required');
    }
    return await user.getIdToken();
  }
}

export const eSimApiService = {
  purchaseESim: async (plan: number, credits: number): Promise<{ success: boolean; transactionId: string }> => {
    console.log('eSimApiService.purchaseESim called with:', { plan, credits });

    try {
      console.log('Getting ID token...');
      const idToken = await getIdToken();
      console.log('ID token obtained, making request to:', `${getFunctionsBaseUrl()}/purchaseESim`);

      const response = await fetch(`${getFunctionsBaseUrl()}/purchaseESim`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        },
        body: JSON.stringify({ plan, credits })
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error text:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('Purchase response:', result);
      return result;
    } catch (error) {
      console.error('Error purchasing eSIM:', error);
      throw error;
    }
  },
  getESimProfile: async (transactionId: string): Promise<ESimProfile> => {
    try {
      const result = await getESimProfile({ transactionId });
      return result.data as ESimProfile;
    } catch (error) {
      console.error('Error calling getESimProfile:', error);
      throw error;
    }
  },
  topUpESim: (transactionId: string, plan: number, credits: number): Promise<{ success: boolean; message: string }> => {
    return topUpESim({ transactionId, plan, credits }).then(result => result.data as { success: boolean; message: string });
  },
  deleteESim: (transactionId: string): Promise<{ success: boolean; message: string }> => {
    return deleteESim({ transactionId }).then(result => result.data as { success: boolean; message:string });
  },
  getESimHistory: (start = '', length = '20', search = ''): Promise<{ data: ESim[], rows: number, page: number, limit: number }> => {
    return getESimHistory({ start, length, search }).then(result => result.data as { data: ESim[], rows: number, page: number, limit: number });
  },
  getMyESims: async (): Promise<ESim[]> => {
    try {
      const idToken = await getIdToken();
      const response = await fetch(`${getFunctionsBaseUrl()}/getMyESims`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        },
        body: JSON.stringify({})
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error: any) {
      console.error('Error fetching eSIMs:', error);
      return [];
    }
  }
};