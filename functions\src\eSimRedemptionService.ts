import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions';
import * as cors from 'cors';
import { logger } from 'firebase-functions';
import { ESimOrder, ESimTopup, CreditTransaction, UserCredits } from './types/credits';
import { eSimApiService } from './eSimService';

// CORS handler for HTTP functions
const corsHandler = cors({ origin: true });

export interface ESimRedemptionRequest {
  planId: number;
  countryCode: string;
  countryName: string;
  dataInGb: number;
  durationDays: number;
  creditCost: number;
  originalPriceUsd: number;
}

export interface ESimTopupRequest {
  esimOrderId: string;
  dataInGb: number;
  creditCost: number;
}

export class ESimRedemptionService {
  private db: admin.firestore.Firestore;

  constructor() {
    this.db = admin.firestore();
  }

  // Redeem credits for eSIM
  async redeemCreditsForESim(
    userId: string,
    request: ESimRedemptionRequest
  ): Promise<{ success: boolean; orderId?: string; error?: string }> {
    try {
      return await this.db.runTransaction(async (transaction) => {
        // 1. Check user's credit balance
        const userCreditsRef = this.db.collection('users').doc(userId).collection('credits').doc('balance');
        const userCreditsDoc = await transaction.get(userCreditsRef);

        if (!userCreditsDoc.exists) {
          throw new Error('User credits not found');
        }

        const userCredits = userCreditsDoc.data() as UserCredits;
        if (userCredits.totalCredits < request.creditCost) {
          throw new Error(`Insufficient credits. Required: ${request.creditCost}, Available: ${userCredits.totalCredits}`);
        }

        // 2. Create eSIM order
        const orderRef = this.db.collection('users').doc(userId).collection('esim_orders').doc();
        const orderId = orderRef.id;

        const esimOrder: ESimOrder = {
          orderId,
          userId,
          planId: request.planId,
          countryCode: request.countryCode,
          countryName: request.countryName,
          dataInGb: request.dataInGb,
          durationDays: request.durationDays,
          creditCost: request.creditCost,
          originalPriceUsd: request.originalPriceUsd,
          status: 'pending',
          purchasedAt: new Date(),
          dataUsed: 0,
          lastUsageUpdate: new Date(),
          metadata: {}
        };

        // 3. Deduct credits
        const updatedCredits: UserCredits = {
          ...userCredits,
          totalCredits: userCredits.totalCredits - request.creditCost,
          lifetimeSpent: userCredits.lifetimeSpent + request.creditCost,
          lastUpdated: new Date()
        };

        // 4. Create credit transaction
        const transactionRef = this.db.collection('users').doc(userId).collection('credit_transactions').doc();
        const creditTransaction: CreditTransaction = {
          id: transactionRef.id,
          userId,
          type: 'redemption',
          amount: -request.creditCost,
          esimOrderId: orderId,
          esimPlanId: request.planId.toString(),
          esimCountryCode: request.countryCode,
          platform: 'web',
          status: 'completed',
          metadata: {
            countryName: request.countryName,
            dataInGb: request.dataInGb,
            durationDays: request.durationDays
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // 5. Commit all changes atomically
        transaction.set(orderRef, esimOrder);
        transaction.set(userCreditsRef, updatedCredits);
        transaction.set(transactionRef, creditTransaction);

        return { success: true, orderId };
      });
    } catch (error: any) {
      logger.error('Failed to redeem credits for eSIM:', error);
      return {
        success: false,
        error: error.message || 'Failed to redeem credits'
      };
    }
  }

  // Process eSIM with provider (called after successful redemption)
  async processESimWithProvider(
    userId: string,
    orderId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const orderRef = this.db.collection('users').doc(userId).collection('esim_orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new Error('eSIM order not found');
      }

      const order = orderDoc.data() as ESimOrder;

      // Call provider API to purchase eSIM
      const providerResult = await eSimApiService.purchaseESim(order.planId);

      if (providerResult.success !== 1) {
        // Update order status to failed
        await orderRef.update({
          status: 'cancelled',
          metadata: {
            ...order.metadata,
            providerError: providerResult.message,
            failedAt: new Date()
          }
        });

        // Refund credits
        await this.refundCreditsForFailedOrder(userId, order);

        throw new Error(providerResult.message || 'Provider API failed');
      }

      // Get eSIM profile details for activation
      let profileData = null;
      try {
        profileData = await eSimApiService.getESimProfile(providerResult.transactionId);
      } catch (error) {
        logger.warn('Failed to get eSIM profile immediately after purchase:', error);
      }

      // Update order with provider details
      await orderRef.update({
        status: 'provisioned',
        providerTransactionId: providerResult.transactionId,
        activationCode: profileData?.activationCode,
        // Note: QR code would need to be generated from activation code if needed
        expiresAt: new Date(Date.now() + order.durationDays * 24 * 60 * 60 * 1000),
        metadata: {
          ...order.metadata,
          provisionedAt: new Date(),
          providerResponse: providerResult,
          profileData: profileData
        }
      });

      return { success: true };
    } catch (error: any) {
      logger.error('Failed to process eSIM with provider:', error);
      return {
        success: false,
        error: error.message || 'Failed to process eSIM'
      };
    }
  }

  // Refund credits for failed order
  private async refundCreditsForFailedOrder(userId: string, order: ESimOrder): Promise<void> {
    await this.db.runTransaction(async (transaction) => {
      const userCreditsRef = this.db.collection('users').doc(userId).collection('credits').doc('balance');
      const userCreditsDoc = await transaction.get(userCreditsRef);

      if (!userCreditsDoc.exists) {
        throw new Error('User credits not found for refund');
      }

      const userCredits = userCreditsDoc.data() as UserCredits;

      // Refund credits
      const updatedCredits: UserCredits = {
        ...userCredits,
        totalCredits: userCredits.totalCredits + order.creditCost,
        lifetimeSpent: userCredits.lifetimeSpent - order.creditCost,
        lastUpdated: new Date()
      };

      // Create refund transaction
      const transactionRef = this.db.collection('users').doc(userId).collection('credit_transactions').doc();
      const creditTransaction: CreditTransaction = {
        id: transactionRef.id,
        userId,
        type: 'refund',
        amount: order.creditCost,
        esimOrderId: order.orderId,
        platform: 'web',
        status: 'completed',
        metadata: {
          reason: 'Provider API failure',
          originalOrderId: order.orderId
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      transaction.set(userCreditsRef, updatedCredits);
      transaction.set(transactionRef, creditTransaction);
    });
  }

  // Top up existing eSIM
  async topupESim(
    userId: string,
    request: ESimTopupRequest
  ): Promise<{ success: boolean; topupId?: string; error?: string }> {
    try {
      return await this.db.runTransaction(async (transaction) => {
        // 1. Verify eSIM order exists and is active
        const orderRef = this.db.collection('users').doc(userId).collection('esim_orders').doc(request.esimOrderId);
        const orderDoc = await transaction.get(orderRef);

        if (!orderDoc.exists) {
          throw new Error('eSIM order not found');
        }

        const order = orderDoc.data() as ESimOrder;
        if (order.status !== 'provisioned' && order.status !== 'activated') {
          throw new Error('eSIM is not active for top-up');
        }

        // 2. Check user's credit balance
        const userCreditsRef = this.db.collection('users').doc(userId).collection('credits').doc('balance');
        const userCreditsDoc = await transaction.get(userCreditsRef);

        if (!userCreditsDoc.exists) {
          throw new Error('User credits not found');
        }

        const userCredits = userCreditsDoc.data() as UserCredits;
        if (userCredits.totalCredits < request.creditCost) {
          throw new Error(`Insufficient credits for top-up. Required: ${request.creditCost}, Available: ${userCredits.totalCredits}`);
        }

        // 3. Create top-up record
        const topupRef = this.db.collection('users').doc(userId).collection('esim_topups').doc();
        const topupId = topupRef.id;

        const esimTopup: ESimTopup = {
          topupId,
          userId,
          esimOrderId: request.esimOrderId,
          dataInGb: request.dataInGb,
          creditCost: request.creditCost,
          status: 'pending',
          createdAt: new Date()
        };

        // 4. Deduct credits
        const updatedCredits: UserCredits = {
          ...userCredits,
          totalCredits: userCredits.totalCredits - request.creditCost,
          lifetimeSpent: userCredits.lifetimeSpent + request.creditCost,
          lastUpdated: new Date()
        };

        // 5. Create credit transaction
        const transactionRef = this.db.collection('users').doc(userId).collection('credit_transactions').doc();
        const creditTransaction: CreditTransaction = {
          id: transactionRef.id,
          userId,
          type: 'topup',
          amount: -request.creditCost,
          esimOrderId: request.esimOrderId,
          topupOrderId: topupId,
          platform: 'web',
          status: 'completed',
          metadata: {
            dataInGb: request.dataInGb
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // 6. Commit all changes
        transaction.set(topupRef, esimTopup);
        transaction.set(userCreditsRef, updatedCredits);
        transaction.set(transactionRef, creditTransaction);

        return { success: true, topupId };
      });
    } catch (error: any) {
      logger.error('Failed to top up eSIM:', error);
      return {
        success: false,
        error: error.message || 'Failed to top up eSIM'
      };
    }
  }
}

// Singleton instance
let eSimRedemptionService: ESimRedemptionService;

export function getESimRedemptionService(): ESimRedemptionService {
  if (!eSimRedemptionService) {
    eSimRedemptionService = new ESimRedemptionService();
  }
  return eSimRedemptionService;
}

// Callable endpoints
export const redeemCreditsForESim = functions.https.onCall(async (data: any, context: any) => {
  try {
    // Authentication: prefer context.auth.uid, fallback to provided userId
    const userId = (context && context.auth && context.auth.uid) || data?.userId;
    if (!userId) {
      throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }

    const result = await getESimRedemptionService().redeemCreditsForESim(userId, data as ESimRedemptionRequest);

    if (result.success && result.orderId) {
      // Process with provider in background
      getESimRedemptionService().processESimWithProvider(userId, result.orderId)
        .catch(error => logger.error('Background eSIM processing failed:', error));
    }

    return result;
  } catch (error: any) {
    logger.error('Error in redeemCreditsForESim (callable):', error);
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});

export const topupESimCredits = functions.https.onCall(async (data: any, context: any) => {
  try {
    const userId = (context && context.auth && context.auth.uid) || data?.userId;
    if (!userId) {
      throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }
    const result = await getESimRedemptionService().topupESim(userId, data as ESimTopupRequest);
    return result;
  } catch (error: any) {
    logger.error('Error in topupESim (callable):', error);
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});

export const getUserESimOrdersHttp = functions.https.onRequest((req, res) => {
  corsHandler(req, res, async () => {
    try {
      // Verify authentication
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const idToken = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      const userId = decodedToken.uid;

      const db = admin.firestore();
      const ordersSnapshot = await db
        .collection('users')
        .doc(userId)
        .collection('esim_orders')
        .orderBy('purchasedAt', 'desc')
        .get();

      const orders = ordersSnapshot.docs.map(doc => ({
        ...doc.data(),
        purchasedAt: doc.data().purchasedAt.toDate(),
        activatedAt: doc.data().activatedAt?.toDate(),
        expiresAt: doc.data().expiresAt?.toDate(),
        lastUsageUpdate: doc.data().lastUsageUpdate?.toDate()
      }));

      res.status(200).json({ success: true, orders });
    } catch (error: any) {
      logger.error('Error in getUserESimOrdersHttp:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });
});

// Alias for frontend compatibility
export const getUserESimOrders = getUserESimOrdersHttp;

export const getUserESimTopupsHttp = functions.https.onRequest((req, res) => {
  corsHandler(req, res, async () => {
    try {
      // Verify authentication
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const idToken = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      const userId = decodedToken.uid;

      const db = admin.firestore();
      const topupsSnapshot = await db
        .collection('users')
        .doc(userId)
        .collection('esim_topups')
        .orderBy('createdAt', 'desc')
        .get();

      const topups = topupsSnapshot.docs.map(doc => ({
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate(),
        completedAt: doc.data().completedAt?.toDate()
      }));

      res.status(200).json({ success: true, topups });
    } catch (error: any) {
      logger.error('Error in getUserESimTopupsHttp:', error);
      res.status(500).json({ error: error.message || 'Internal server error' });
    }
  });
});

// Alias for frontend compatibility
export const getUserESimTopups = getUserESimTopupsHttp;

export const getESimOrderDetails = functions.https.onCall(async (data: any, context: any) => {
  try {
    const userId = (context && context.auth && context.auth.uid) || data?.userId;
    if (!userId) {
      throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }

    const { orderId } = data as { orderId?: string };
    if (!orderId) {
      throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
    }

    const db = admin.firestore();
    const orderDoc = await db
      .collection('users')
      .doc(userId)
      .collection('esim_orders')
      .doc(orderId)
      .get();

    if (!orderDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Order not found');
    }

    const orderData = orderDoc.data();
    const order = {
      ...orderData,
      purchasedAt: orderData?.purchasedAt.toDate(),
      activatedAt: orderData?.activatedAt?.toDate(),
      expiresAt: orderData?.expiresAt?.toDate(),
      lastUsageUpdate: orderData?.lastUsageUpdate?.toDate()
    };

    return { success: true, order };
  } catch (error: any) {
    logger.error('Error in getESimOrderDetails (callable):', error);
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});
