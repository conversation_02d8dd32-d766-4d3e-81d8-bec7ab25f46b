import React from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonBackButton,
  IonButtons,
  IonText
} from '@ionic/react';
import { useTranslation } from 'react-i18next';

const Terms: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonBackButton />
          </IonButtons>
          <IonTitle>{t('terms.title')}</IonTitle>
        </IonToolbar>
      </IonHeader>
      
      <IonContent className="ion-padding">
        <IonText>
          <h1>{t('terms.title')}</h1>
          <p><strong>{t('terms.lastUpdated')}</strong> {new Date().toLocaleDateString()}</p>
          
          <h2>1. {t('terms.acceptanceOfTerms')}</h2>
          <p>{t('terms.acceptanceOfTermsText')}</p>
          
          <h2>2. {t('terms.serviceDescription')}</h2>
          <p>{t('terms.serviceDescriptionText')}</p>
          
          <h2>3. {t('terms.userAccounts')}</h2>
          <p>{t('terms.userAccountsText')}</p>
          
          <h2>4. {t('terms.privacy')}</h2>
          <p>{t('terms.privacyText')}</p>
          
          <h2>5. {t('terms.prohibitedUses')}</h2>
          <p>{t('terms.prohibitedUsesText')}</p>
          
          <h2>6. {t('terms.serviceAvailability')}</h2>
          <p>{t('terms.serviceAvailabilityText')}</p>
          
          <h2>7. {t('terms.limitationOfLiability')}</h2>
          <p>{t('terms.limitationOfLiabilityText')}</p>
          
          <h2>8. {t('terms.changesToTerms')}</h2>
          <p>{t('terms.changesToTermsText')}</p>
          
          <h2>9. {t('terms.contactInformation')}</h2>
          <p>{t('terms.contactInformationText')}</p>
        </IonText>
      </IonContent>
    </IonPage>
  );
};

export default Terms;