import Stripe from 'stripe';
import * as admin from 'firebase-admin';
import { logger } from 'firebase-functions/v2';
import * as functions from 'firebase-functions';
import * as dotenv from 'dotenv';
import { CreditPackage, UserCredits, CreditTransaction } from './types/credits';
import * as cors from 'cors';

const corsHandler = cors({ origin: true });

export interface StripePaymentIntentData {
  priceId: string;
  userId: string;
  userEmail: string;
  userName: string;
  packageId: string;
  amount: number; // in cents
  currency: string;
  platform: 'web' | 'ios' | 'android';
}

export interface StripeWebhookData {
  id: string;
  object: string;
  type: string;
  data: {
    object: Stripe.PaymentIntent | Stripe.Checkout.Session;
  };
}

export class StripeBackendService {
  private stripe: Stripe | null = null;
  private db = admin.firestore();

  constructor() {
    // Don't initialize Stripe in constructor - do it lazily
  }

  private getStripe(): Stripe {
    if (!this.stripe) {
      dotenv.config();
      
      const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
      if (!stripeSecretKey) {
        throw new Error('STRIPE_SECRET_KEY environment variable is required');
      }

      this.stripe = new Stripe(stripeSecretKey, {
        apiVersion: '2025-08-27.basil',
      });

      logger.info('Stripe backend service initialized');
    }
    return this.stripe;
  }

  // Create payment intent for mobile payments
  async createPaymentIntent(data: StripePaymentIntentData): Promise<{
    success: boolean;
    clientSecret?: string;
    paymentIntentId?: string;
    ephemeralKeySecret?: string;
    customerId?: string;
    error?: string;
  }> {
    try {
      // Find or create a Stripe customer
      let customer: Stripe.Customer;
      const customers = await this.getStripe().customers.list({ email: data.userEmail, limit: 1 });

      if (customers.data.length > 0) {
        customer = customers.data[0];
      } else {
        customer = await this.getStripe().customers.create({
          email: data.userEmail,
          name: data.userName,
          metadata: { userId: data.userId },
        });
      }

      // Create an ephemeral key for the customer
      const ephemeralKey = await this.getStripe().ephemeralKeys.create(
        { customer: customer.id },
        { apiVersion: '2025-08-27.basil' }
      );

      const paymentIntent = await this.getStripe().paymentIntents.create({
        amount: data.amount,
        currency: data.currency,
        customer: customer.id,
        metadata: {
          userId: data.userId,
          packageId: data.packageId,
          platform: data.platform,
          priceId: data.priceId,
        },
        receipt_email: data.userEmail,
        description: `Credits purchase - ${data.packageId}`,
      });

      return {
        success: true,
        clientSecret: paymentIntent.client_secret!,
        paymentIntentId: paymentIntent.id,
        ephemeralKeySecret: ephemeralKey.secret!,
        customerId: customer.id,
      };
    } catch (error: any) {
      logger.error('Failed to create payment intent:', error);
      return {
        success: false,
        error: error.message || 'Failed to create payment intent',
      };
    }
  }

  // Create checkout session for web payments
  async createCheckoutSession(data: StripePaymentIntentData & {
    successUrl: string;
    cancelUrl: string;
  }): Promise<{
    success: boolean;
    checkoutUrl?: string;
    sessionId?: string;
    error?: string;
  }> {
    try {
      const session = await this.getStripe().checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price: data.priceId,
            quantity: 1,
          },
        ],
        mode: 'payment',
        success_url: data.successUrl,
        cancel_url: data.cancelUrl,
        customer_email: data.userEmail,
        metadata: {
          userId: data.userId,
          packageId: data.packageId,
          platform: data.platform,
        },
      });

      return {
        success: true,
        checkoutUrl: session.url!,
        sessionId: session.id,
      };
    } catch (error: any) {
      logger.error('Failed to create checkout session:', error);
      return {
        success: false,
        error: error.message || 'Failed to create checkout session',
      };
    }
  }

  // Handle Stripe webhook events
  async handleWebhook(
    body: string,
    signature: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
      if (!webhookSecret) {
        throw new Error('STRIPE_WEBHOOK_SECRET not configured');
      }

      const event = this.getStripe().webhooks.constructEvent(body, signature, webhookSecret);

      logger.info(`Processing Stripe webhook: ${event.type}`, event.id);

      switch (event.type) {
        case 'payment_intent.succeeded':
          return await this.handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
        case 'checkout.session.completed':
          return await this.handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
        case 'payment_intent.payment_failed':
          return await this.handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        default:
          logger.info(`Unhandled webhook event: ${event.type}`);
          return { success: true };
      }
    } catch (error: any) {
      logger.error('Failed to handle Stripe webhook:', error);
      return {
        success: false,
        error: error.message || 'Failed to process webhook',
      };
    }
  }

  // Handle successful payment intent
  private async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const { userId, packageId, platform } = paymentIntent.metadata;

      if (!userId || !packageId) {
        throw new Error('Missing required metadata in payment intent');
      }

      // Get credit package details
      const packageDoc = await this.db.collection('credit_packages').doc(packageId).get();
      if (!packageDoc.exists) {
        throw new Error(`Credit package not found: ${packageId}`);
      }

      const creditPackage = packageDoc.data() as CreditPackage;

      // Add credits to user account
      await this.addCreditsToUser(userId, creditPackage, paymentIntent.id, platform || 'unknown');

      logger.info(`Credits added successfully for payment: ${paymentIntent.id}`);
      return { success: true };
    } catch (error: any) {
      logger.error('Failed to handle payment intent succeeded:', error);
      return {
        success: false,
        error: error.message || 'Failed to process successful payment',
      };
    }
  }

  // Handle completed checkout session
  private async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const { userId, packageId, platform } = session.metadata || {};

      if (!userId || !packageId) {
        throw new Error('Missing required metadata in checkout session');
      }

      // Get credit package details
      const packageDoc = await this.db.collection('credit_packages').doc(packageId).get();
      if (!packageDoc.exists) {
        throw new Error(`Credit package not found: ${packageId}`);
      }

      const creditPackage = packageDoc.data() as CreditPackage;

      // Add credits to user account
      await this.addCreditsToUser(userId, creditPackage, session.id, platform || 'web');

      logger.info(`Credits added successfully for checkout session: ${session.id}`);
      return { success: true };
    } catch (error: any) {
      logger.error('Failed to handle checkout session completed:', error);
      return {
        success: false,
        error: error.message || 'Failed to process completed checkout',
      };
    }
  }

  // Handle failed payment
  private async handlePaymentFailed(paymentIntent: Stripe.PaymentIntent): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const { userId, packageId } = paymentIntent.metadata;

      logger.warn(`Payment failed for user ${userId}, package ${packageId}:`, paymentIntent.id);

      // You could send a notification to the user here
      // or log the failure for analytics

      return { success: true };
    } catch (error: any) {
      logger.error('Failed to handle payment failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to process payment failure',
      };
    }
  }

  // Add credits to user account (atomic transaction)
  private async addCreditsToUser(
    userId: string,
    creditPackage: CreditPackage,
    stripeTransactionId: string,
    platform: string
  ): Promise<void> {
    await this.db.runTransaction(async (transaction) => {
      const userCreditsRef = this.db.collection('users').doc(userId).collection('credits').doc('balance');
      const userCreditsDoc = await transaction.get(userCreditsRef);

      let currentCredits: UserCredits;
      if (userCreditsDoc.exists) {
        currentCredits = userCreditsDoc.data() as UserCredits;
      } else {
        currentCredits = {
          userId,
          totalCredits: 0,
          lifetimeCredits: 0,
          lifetimeSpent: 0,
          pendingCredits: 0,
          lastUpdated: new Date(),
          createdAt: new Date(),
        };
      }

      // Update credits
      const updatedCredits: UserCredits = {
        ...currentCredits,
        totalCredits: currentCredits.totalCredits + creditPackage.credits,
        lifetimeCredits: currentCredits.lifetimeCredits + creditPackage.credits,
        lastUpdated: new Date(),
      };

      // Create transaction record
      const transactionRef = this.db
        .collection('users')
        .doc(userId)
        .collection('credit_transactions')
        .doc();

      const creditTransaction: CreditTransaction = {
        id: transactionRef.id,
        userId,
        type: 'purchase',
        amount: creditPackage.credits,
        packageId: creditPackage.id,
        stripePaymentIntentId: stripeTransactionId,
        stripePriceId: creditPackage.stripePriceId,
        platform: platform as 'web' | 'ios' | 'android',
        status: 'completed',
        metadata: {
          packageName: creditPackage.name,
          priceUsd: creditPackage.price,
          stripeTransactionId, // Keep for backward compatibility
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Commit both updates
      transaction.set(userCreditsRef, updatedCredits);
      transaction.set(transactionRef, creditTransaction);
    });
  }

  // Verify payment intent status
  async verifyPaymentIntent(paymentIntentId: string): Promise<{
    success: boolean;
    status?: string;
    error?: string;
  }> {
    try {
      const paymentIntent = await this.getStripe().paymentIntents.retrieve(paymentIntentId);

      return {
        success: true,
        status: paymentIntent.status,
      };
    } catch (error: any) {
      logger.error('Failed to verify payment intent:', error);
      return {
        success: false,
        error: error.message || 'Failed to verify payment intent',
      };
    }
  }
}

// Firebase Functions exports
let stripeService: StripeBackendService;

function getStripeService(): StripeBackendService {
  if (!stripeService) {
    stripeService = new StripeBackendService();
  }
  return stripeService;
}

// Convert to HTTP endpoints with CORS - match frontend function names
export const createStripePaymentIntent = functions.https.onRequest((req, res) => {
  corsHandler(req, res, async () => {
    try {
      // Verify authentication
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ success: false, error: 'Unauthorized: Missing or invalid authorization header' });
        return;
      }

      const idToken = authHeader.split('Bearer ')[1];
      let decodedToken;
      try {
        decodedToken = await admin.auth().verifyIdToken(idToken);
      } catch (authError) {
        console.error('Authentication error:', authError);
        res.status(401).json({ success: false, error: 'Unauthorized: Invalid token' });
        return;
      }

      // Verify that the user email matches the request
      const requestData = req.body as StripePaymentIntentData;
      if (decodedToken.email !== requestData.userEmail) {
        console.error('Email mismatch:', { tokenEmail: decodedToken.email, requestEmail: requestData.userEmail });
        res.status(403).json({ success: false, error: 'Forbidden: Email mismatch' });
        return;
      }

      // Verify that the user ID matches the request
      if (decodedToken.uid !== requestData.userId) {
        console.error('User ID mismatch:', { tokenUid: decodedToken.uid, requestUid: requestData.userId });
        res.status(403).json({ success: false, error: 'Forbidden: User ID mismatch' });
        return;
      }

      const result = await getStripeService().createPaymentIntent(requestData);
      res.status(200).json(result);
    } catch (error: any) {
      console.error('Error in createStripePaymentIntent:', error);
      res.status(500).json({ success: false, error: error.message || 'Internal server error' });
    }
  });
});

export const createStripeCheckoutSession = functions.https.onRequest((req, res) => {
  corsHandler(req, res, async () => {
    try {
      const result = await getStripeService().createCheckoutSession(req.body as StripePaymentIntentData & { successUrl: string; cancelUrl: string; });
      res.status(200).json(result);
    } catch (error: any) {
      console.error('Error in createStripeCheckoutSession:', error);
      res.status(500).json({ success: false, error: error.message || 'Internal server error' });
    }
  });
});

export const stripeWebhook = functions.https.onRequest(async (req, res) => {
  const signature = req.headers['stripe-signature'] as string;
  const body = req.rawBody.toString();
  
  const result = await getStripeService().handleWebhook(body, signature);
  
  if (result.success) {
    res.status(200).send('OK');
  } else {
    res.status(400).send(result.error);
  }
});

export const verifyStripePaymentIntent = functions.https.onRequest((req, res) => {
  corsHandler(req, res, async () => {
    try {
      const { paymentIntentId } = req.body as { paymentIntentId: string };
      const result = await getStripeService().verifyPaymentIntent(paymentIntentId);
      res.status(200).json(result);
    } catch (error: any) {
      console.error('Error in verifyStripePaymentIntent:', error);
      res.status(500).json({ success: false, error: error.message || 'Internal server error' });
    }
  });
});
