import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonButton,
  IonIcon,
  IonText,
  IonSpinner,
  IonAlert,
  IonBadge,
  IonGrid,
  IonRow,
  IonCol,
  IonItem,
  IonLabel,
  IonList,
  IonRefresher,
  IonRefresherContent,
  IonLoading
} from '@ionic/react';
import {
  card,
  flash,
  checkmark,
  star,
  wallet,
  refresh,
  headset,
  lockClosed,
  rocket
} from 'ionicons/icons';
import { useAuth } from '../contexts/AuthContext';
import { useTranslation, Trans } from 'react-i18next';
import { useHistory, useLocation } from 'react-router-dom';
import { CreditPackage, UserCredits, CreditTransaction } from '../types/credits';
import { creditsApiService } from '../services/creditsApiService';
import { eSimRedemptionService } from '../services/eSimRedemptionService';
import AppBar from '../components/AppBar';
import './Credits.css';
import { Capacitor } from '@capacitor/core';
import { CapacitorAuthService } from '../services/capacitorAuthService';
import { auth } from '../firebase';

// LemonSqueezy TypeScript declarations
declare global {
  interface Window {
    createLemonSqueezy: () => void;
    LemonSqueezy: {
      Url: {
        Open: (url: string) => void;
      };
    };
  }
}

const Credits: React.FC = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const history = useHistory();
  const location = useLocation();

  // Debug logging
  useEffect(() => {
    console.log('Credits page - currentUser:', currentUser);
    console.log('Credits page - currentUser type:', typeof currentUser);
    console.log('Credits page - currentUser uid:', currentUser?.uid);
    console.log('Credits page - currentUser email:', currentUser?.email);
    console.log('Credits page - is null?', currentUser === null);
    console.log('Credits page - is undefined?', currentUser === undefined);
  }, [currentUser]);

  // Debug logging
  useEffect(() => {
    console.log('Credits page - currentUser:', currentUser);
    console.log('Credits page - currentUser type:', typeof currentUser);
    console.log('Credits page - currentUser uid:', currentUser?.uid);
    console.log('Credits page - currentUser email:', currentUser?.email);
    console.log('Credits page - is null?', currentUser === null);
    console.log('Credits page - is undefined?', currentUser === undefined);
  }, [currentUser]);



  const [creditPackages, setCreditPackages] = useState<CreditPackage[]>([]);
  const [userCredits, setUserCredits] = useState<UserCredits | null>(null);
  const [recentTransactions, setRecentTransactions] = useState<CreditTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState<string | null>(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [processingPayment, setProcessingPayment] = useState(false);

  // LemonSqueezy checkout URLs mapping
  const lemonSqueezyUrls = {
    minnow: 'https://esimnumero.lemonsqueezy.com/buy/20140fd2-969f-44b2-b283-9ce8c427b08e?embed=1',
    tuna: 'https://esimnumero.lemonsqueezy.com/buy/56fadc4f-0565-4154-a556-e9d4c510bac5?embed=1',
    dolphin: 'https://esimnumero.lemonsqueezy.com/buy/2c0f15d4-84cf-4274-8f7b-24e532debe90?embed=1',
    octopus: 'https://esimnumero.lemonsqueezy.com/buy/389e5be9-c737-460d-8256-0b1f2fd77dd7?embed=1',
    shark: 'https://esimnumero.lemonsqueezy.com/buy/db8d4796-08f7-4fb4-82d7-d06d394c90c3?embed=1',
    whale: 'https://esimnumero.lemonsqueezy.com/buy/8628d89a-a999-4a6e-819a-ffe47171a63c?embed=1'
  };

  // Load LemonSqueezy script and initialize
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://app.lemonsqueezy.com/js/lemon.js';
    script.defer = true;

    script.onload = () => {
      // Initialize LemonSqueezy after script loads
      if (window.createLemonSqueezy) {
        window.createLemonSqueezy();

        // Add event listeners for checkout events
        window.addEventListener('lemon-squeezy-checkout-opened', () => {
          console.log('LemonSqueezy checkout opened');
          // Hide the "Opening secure checkout..." alert when overlay opens
          setShowAlert(false);
        });

        window.addEventListener('lemon-squeezy-checkout-closed', () => {
          console.log('LemonSqueezy checkout closed');
          setPurchasing(null);
        });

        window.addEventListener('lemon-squeezy-checkout-completed', () => {
          console.log('LemonSqueezy checkout completed');
          setAlertMessage('Payment successful! Credits will be added shortly.');
          setShowAlert(true);
          setPurchasing(null);

          // Refresh credits after a short delay
          setTimeout(() => {
            loadData();
          }, 3000);
        });
      }
    };

    document.head.appendChild(script);

    return () => {
      // Cleanup script on unmount
      const existingScript = document.querySelector('script[src="https://app.lemonsqueezy.com/js/lemon.js"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  // Parse URL parameters for context
  const urlParams = new URLSearchParams(location.search);
  const neededCredits = urlParams.get('needed') ? parseInt(urlParams.get('needed')!) : null;
  const shortfall = urlParams.get('shortfall') ? parseInt(urlParams.get('shortfall')!) : null;
  const planId = urlParams.get('plan');
  const countryCode = urlParams.get('country');
  // Removed recommended package to avoid loading issues
  const topupOrderId = urlParams.get('topup');
  const isWelcome = urlParams.get('welcome') === 'true';
  const firstESimId = urlParams.get('first_esim');

  // Load data from API with better error handling
  const loadData = React.useCallback(async () => {
    setLoading(true);
    try {
      // Load credit packages first (most important)
      let packages: CreditPackage[] = [];
      try {
        packages = await creditsApiService.getCreditPackages();

        // Remove duplicates and filter out packages with "Credits" in the name
        const filteredPackages = packages.filter((pkg) => {
          // Remove packages that have "Credits" in the name (keep the base version)
          return !pkg.name.toLowerCase().includes('credits') && !pkg.id.toLowerCase().includes('credits');
        });

        // Remove any remaining duplicates based on package ID
        const uniquePackages = filteredPackages.filter((pkg, index, self) =>
          index === self.findIndex(p => p.id === pkg.id)
        );

        setCreditPackages(uniquePackages);
      } catch (error) {
        console.error('Error loading credit packages:', error);
        setCreditPackages([]);
      }

      // Load transactions first (this works, so copy its auth logic)
      try {
        console.log('Credits page - Loading transactions for user...');
        const transactions = await creditsApiService.getUserCreditTransactions(10);
        setRecentTransactions(transactions);
        console.log('Credits page - Successfully loaded transactions:', transactions.length);

        // If transactions loaded successfully, we have auth - now load credits
        console.log('Credits page - Auth confirmed via transactions, loading credits...');
        const credits = await creditsApiService.getUserCredits();
        console.log('Credits page - getUserCredits returned:', credits);
        setUserCredits(credits);
        console.log('Credits page - Successfully loaded user credits:', credits);
      } catch (error) {
        console.error('Credits page - Error loading data:', error);
        setRecentTransactions([]);
        setUserCredits(null);
      }
    } catch (error) {
      console.error('Error loading credit data:', error);
    } finally {
      setLoading(false);
    }
  }, [currentUser]);

  // Load data on component mount and when user changes
  useEffect(() => {
    // Wait for auth state to be ready before loading data
    const timer = setTimeout(() => {
      loadData();
    }, 100); // Small delay to ensure auth state is established

    return () => clearTimeout(timer);
  }, [loadData]);

  // Handle refresh
  const handleRefresh = React.useCallback(async (event: CustomEvent) => {
    await loadData();
    event.detail.complete();
  }, [loadData]);

  // Handle purchase with LemonSqueezy
  const handlePurchase = React.useCallback(async (creditPackage: CreditPackage) => {
    // Check currentUser from context first
    if (!currentUser) {
      console.log('Credits - No currentUser from context, checking directly...');

      // Fallback: Check authentication directly
      try {
        const directUser = Capacitor.isNativePlatform()
          ? await CapacitorAuthService.getCurrentUser()
          : auth.currentUser;

        console.log('Credits - Direct user check:', directUser);

        if (!directUser) {
          setAlertMessage(t('credits.signInToPurchase'));
          setShowAlert(true);
          return;
        }
      } catch (error) {
        console.error('Credits - Error checking authentication:', error);
        setAlertMessage(t('credits.signInToPurchase'));
        setShowAlert(true);
        return;
      }
    }

    // Get the LemonSqueezy checkout URL for this package
    const baseUrl = lemonSqueezyUrls[creditPackage.id as keyof typeof lemonSqueezyUrls];

    if (!baseUrl) {
      setAlertMessage('Payment option not available for this package');
      setShowAlert(true);
      return;
    }

    setPurchasing(creditPackage.id);

    try {
      // Get user data for prefilling
      const userEmail = currentUser?.email || '';
      const userName = currentUser?.displayName || '';
      const userId = currentUser?.uid || '';

      // Build checkout URL with prefilled data
      const url = new URL(baseUrl);
      if (userEmail) {
        url.searchParams.set('checkout[email]', userEmail);
      }
      if (userName) {
        url.searchParams.set('checkout[name]', userName);
      }
      // Add custom data for webhook processing
      if (userId) {
        url.searchParams.set('checkout[custom][user_id]', userId);
        url.searchParams.set('checkout[custom][package_id]', creditPackage.id);
      }

      const checkoutUrl = url.toString();

      // Use LemonSqueezy's URL opener for better overlay handling
      if (window.LemonSqueezy && window.LemonSqueezy.Url) {
        window.LemonSqueezy.Url.Open(checkoutUrl);
      } else {
        // Fallback: Create a temporary link element
        const link = document.createElement('a');
        link.href = checkoutUrl;
        link.className = 'lemonsqueezy-button';
        link.style.display = 'none';
        link.textContent = `Buy ${creditPackage.name}`;

        // Add to DOM temporarily
        document.body.appendChild(link);

        // Trigger the LemonSqueezy checkout
        link.click();

        // Clean up
        document.body.removeChild(link);
      }

      // Show processing message
      setAlertMessage('Opening secure checkout...');
      setShowAlert(true);

      // Reset purchasing state after a short delay (in case checkout doesn't open)
      setTimeout(() => {
        setPurchasing(null);
        // Only hide alert if checkout didn't open (fallback)
        if (showAlert) {
          setShowAlert(false);
        }
      }, 5000);

    } catch (error) {
      console.error('Purchase error:', error);
      setAlertMessage(t('credits.purchaseError'));
      setShowAlert(true);
      setPurchasing(null);
    }
  }, [currentUser, lemonSqueezyUrls, t]);

  // Get package icon
  const getPackageIcon = React.useCallback((packageId: string) => {
    const icons: Record<string, string> = {
      minnow: '🐟',
      tuna: '🐟',
      dolphin: '🐬',
      octopus: '🐙',
      shark: '🦈',
      whale: '🐋'
    };
    return icons[packageId] || '🐟';
  }, []);

  // Check if package is popular
  const isPopularPackage = React.useCallback((packageId: string) => packageId === 'dolphin', []);

  if (loading) {
    return (
      <IonPage>
        <AppBar title={t('credits.title')} />
        <IonContent className="ion-padding">
          <div className="loading-container">
            <IonSpinner name="crescent" />
            <IonText>
              <p>{t('credits.loadingPackages')}</p>
            </IonText>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <AppBar title={t('credits.title')} />
      <IonContent className="credits-page-content">
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent></IonRefresherContent>
        </IonRefresher>

        <div className="content-wrapper">

          {/* Welcome Banner for First-Time Users */}
          {isWelcome && firstESimId && (
            <IonCard className="welcome-banner" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white', margin: '16px' }}>
              <IonCardContent>
                <div className="context-message">
                  <IonIcon icon={star} style={{ fontSize: '2rem', marginRight: '12px' }} />
                  <div>
                    <h3 style={{ margin: '0 0 8px 0' }}>🎉 Welcome to KrakenSim!</h3>
                    <p style={{ margin: '0 0 12px 0', opacity: 0.9 }}>
                      Congratulations on purchasing your first eSIM! Now you can buy credits to get better rates on future eSIM purchases.
                    </p>
                    <p style={{ margin: '0', fontSize: '0.9rem', opacity: 0.8 }}>
                      💡 Credits give you discounted rates and faster checkout for your next eSIMs.
                    </p>
                  </div>
                </div>
              </IonCardContent>
            </IonCard>
          )}

          {/* Context Banner */}
          {(neededCredits || shortfall || topupOrderId) && (
            <IonCard className="context-banner" color="primary">
              <IonCardContent>
                {topupOrderId ? (
                  <div className="context-message">
                    <IonIcon icon={flash} />
                    <div>
                      <h3>Top-up Required</h3>
                      <p>Purchase credits to top up your eSIM with additional data.</p>
                      {neededCredits && <p><strong>Credits needed: {neededCredits}</strong></p>}
                    </div>
                  </div>
                ) : shortfall ? (
                  <div className="context-message">
                    <IonIcon icon={flash} />
                    <div>
                      <h3>Insufficient Credits</h3>
                      <p>You need {shortfall} more credits to purchase this eSIM plan.</p>
                      <p>Choose a credit package below to continue with your purchase.</p>
                    </div>
                  </div>
                ) : neededCredits ? (
                  <div className="context-message">
                    <IonIcon icon={star} />
                    <div>
                      <h3>Credits Needed</h3>
                      <p>You need {neededCredits} credits for your selected eSIM plan.</p>
                    </div>
                  </div>
                ) : null}
              </IonCardContent>
            </IonCard>
          )}

          {/* User Credits Summary */}
          {(() => {
            console.log('Credits page - Rendering, userCredits:', userCredits);
            console.log('Credits page - userCredits type:', typeof userCredits);
            console.log('Credits page - userCredits truthy:', !!userCredits);
            return null;
          })()}
          {/* ALWAYS SHOW CREDITS BANNER */}
          {true && (
            <IonCard className={`credits-summary ${processingPayment ? 'updating' : ''}`}>
              <IonCardHeader>
                <IonCardTitle>
                  <IonIcon icon={wallet} /> {t('credits.yourCredits')}
                </IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                <div className="credits-balance">
                  <div className="balance-item">
                    <IonText color="primary">
                      <h1>
                        {processingPayment ? (
                          <IonSpinner name="dots" />
                        ) : (
                          userCredits?.totalCredits || 0
                        )}
                      </h1>
                    </IonText>
                    <IonText color="medium">
                      <p>
                        {processingPayment
                          ? 'Updating credits...'
                          : t('credits.availableCredits')
                        }
                      </p>
                    </IonText>
                  </div>
                  <div className="balance-stats">
                    <div className="stat-item">
                      <IonText color="medium">
                        <small>{t('credits.lifetimePurchased')}</small>
                      </IonText>
                      <IonText>
                        <p>{userCredits?.lifetimeCredits || 0}</p>
                      </IonText>
                    </div>
                    <div className="stat-item">
                      <IonText color="medium">
                        <small>{t('credits.totalSpent')}</small>
                      </IonText>
                      <IonText>
                        <p>{userCredits?.lifetimeSpent || 0}</p>
                      </IonText>
                    </div>
                  </div>
                </div>
              </IonCardContent>
            </IonCard>
          )}

          {/* Credit Packages */}
          <div className="credits-packages">
            <IonText className="section-title">
              <h2>{t('credits.buyCredits')}</h2>
              <p>{t('credits.choosePackage')}</p>
            </IonText>

            {/* Trust Features (Moved to Top) */}
            <div className="trust-features">
              <div className="trust-item">
                <div className="trust-icon-wrapper">
                  <IonIcon icon={headset} />
                </div>
                <span>{t('credits.trust.support')}</span>
              </div>
              <div className="trust-item">
                <div className="trust-icon-wrapper">
                  <IonIcon icon={rocket} />
                </div>
                <span>{t('credits.trust.instant')}</span>
              </div>
              <div className="trust-item">
                <div className="trust-icon-wrapper">
                  <IonIcon icon={lockClosed} />
                </div>
                <span>{t('credits.trust.secure')}</span>
              </div>
            </div>

            {!currentUser ? (
              <IonCard className="credit-card" style={{ textAlign: 'center', padding: '32px', border: '1px solid var(--ion-color-medium)' }}>
                <IonCardContent>
                  <IonIcon icon={card} style={{ fontSize: '48px', color: 'var(--ion-color-medium)', marginBottom: '16px' }} />
                  <h2 style={{ fontSize: '1.4rem', fontWeight: 'bold', color: 'white', marginBottom: '8px' }}>
                    {t('credits.signInToView')}
                  </h2>
                  <p style={{ color: '#888', marginBottom: '24px' }}>
                    {t('credits.signInDescription', 'Please sign in to view and purchase credit packages.')}
                  </p>
                  <IonButton
                    routerLink="/login"
                    expand="block"
                    mode="ios"
                    style={{ maxWidth: '300px', margin: '0 auto' }}
                  >
                    {t('nav.login')}
                  </IonButton>
                </IonCardContent>
              </IonCard>
            ) : (
              <IonGrid className="credits-packages-grid" fixed>
                <IonRow>
                  {creditPackages.map((pkg) => (
                    <IonCol size="12" sizeMd="6" sizeLg="6" key={pkg.id}>
                      <div className={`credit-card ${isPopularPackage(pkg.id) ? 'popular' : ''}`}>
                        {isPopularPackage(pkg.id) && (
                          <div className="popular-badge">{t('credits.mostPopular')}</div>
                        )}

                        {/* Header: Amount & Price */}
                        <div className="credit-card-header" style={{ paddingBottom: '8px' }}>
                          <div className="credits-display">
                            <span className="credit-amount">{pkg.credits}</span>
                            <span className="credit-label">credits</span>
                          </div>
                          <div className="price-pill">
                            ${pkg.price}
                          </div>
                        </div>

                        {/* Content: Details */}
                        <div className="credit-card-content" style={{ paddingTop: '8px' }}>
                          <div className="features-list" style={{ marginBottom: '16px', gridTemplateColumns: '1fr', textAlign: 'center' }}>
                            <div className="feature-row" style={{ justifyContent: 'center', background: 'transparent' }}>
                              <span style={{ fontSize: '1.1rem', fontWeight: '600', color: '#fff' }}>
                                {t(`credits.packages.${pkg.id}.name`, pkg.name)}
                              </span>
                            </div>
                          </div>

                          <IonButton
                            className={`action-btn ${purchasing === pkg.id ? 'processing' : ''}`}
                            expand="block"
                            onClick={() => handlePurchase(pkg)}
                            disabled={purchasing === pkg.id || processingPayment}
                          >
                            {purchasing === pkg.id ? (
                              <>
                                <IonSpinner name="dots" />
                              </>
                            ) : processingPayment ? (
                              'Processing...'
                            ) : (
                              t('plans.buyNow')
                            )}
                          </IonButton>
                        </div>
                      </div>
                    </IonCol>
                  ))}
                </IonRow>

                <div className="powered-by-container">
                  <span className="powered-by-text">Powered by Lemon Squeezy</span>
                  <span className="powered-by-text" style={{ margin: '0 8px' }}>•</span>
                  <span className="powered-by-text">Secured by Stripe</span>
                </div>

                <div className="legal-footer" style={{ textAlign: 'center', margin: '24px 16px', fontSize: '0.8rem', color: '#666' }}>
                  <Trans
                    i18nKey="credits.legal_disclaimer_v2"
                    defaults="By purchasing the package, you agree to {{appName}} <1>Terms</1> and <2>Privacy Policy</2>."
                    values={{ appName: import.meta.env.VITE_APP_NAME || 'KrakenSim' }}
                    components={{
                      1: <a href="/terms" className="legal-link" style={{ textDecoration: 'underline' }}>Terms</a>,
                      2: <a href="/privacy" className="legal-link" style={{ textDecoration: 'underline' }}>Privacy Policy</a>
                    }}
                  />
                </div>
              </IonGrid>
            )}
          </div>

          {/* Recent Transactions */}
          {recentTransactions.length > 0 && (
            <IonCard className="recent-transactions">
              <IonCardHeader>
                <IonCardTitle>{t('credits.recentActivity')}</IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                <IonList>
                  {recentTransactions.map((transaction) => (
                    <IonItem key={transaction.id}>
                      <IonIcon
                        icon={transaction.type === 'purchase' ? card : flash}
                        slot="start"
                        color={transaction.type === 'purchase' ? 'success' : 'primary'}
                      />
                      <IonLabel>
                        <h3>
                          {transaction.type === 'purchase' ? t('credits.creditsPurchased') : t('credits.creditsSpent')}
                        </h3>
                        <p>
                          {transaction.metadata?.packageName || transaction.metadata?.esimPlanName || t('credits.transaction')}
                        </p>
                        <p>{transaction.createdAt instanceof Date
                          ? transaction.createdAt.toLocaleDateString()
                          : (transaction.createdAt as any)?.toDate?.()?.toLocaleDateString() || 'Recent'
                        }</p>
                      </IonLabel>
                      <IonText
                        color={transaction.amount > 0 ? 'success' : 'primary'}
                        slot="end"
                      >
                        <strong>
                          {transaction.amount > 0 ? '+' : ''}{t('credits.creditsCount', { count: transaction.amount })}
                        </strong>
                      </IonText>
                    </IonItem>
                  ))}
                </IonList>
              </IonCardContent>
            </IonCard>
          )}

          <IonAlert
            isOpen={showAlert}
            onDidDismiss={() => setShowAlert(false)}
            header={t('credits.title')}
            message={alertMessage}
            buttons={[t('common.ok')]}
          />

          {/* Payment Processing Loading */}
          <IonLoading
            isOpen={processingPayment}
            message="Processing your payment and updating credits..."
            duration={0}
            spinner="crescent"
          />
        </div>
      </IonContent>
    </IonPage>
  );
};

export default Credits;