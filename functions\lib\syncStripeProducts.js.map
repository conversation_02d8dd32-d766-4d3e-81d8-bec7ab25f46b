{"version": 3, "file": "syncStripeProducts.js", "sourceRoot": "", "sources": ["../src/syncStripeProducts.ts"], "names": [], "mappings": ";;;AAAA,wCAAwC;AACxC,gDAAgD;AAChD,iCAAiC;AAEjC,6BAA6B;AAE7B,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AAE3C,gDAAgD;AAChD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;IACtB,KAAK,CAAC,aAAa,EAAE,CAAC;CACvB;AAED,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;AAE7B,MAAM,wBAAwB;IAA9B;QACU,WAAM,GAAQ,IAAI,CAAC;IAiH7B,CAAC;IA/GS,SAAS;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,CAAC,MAAM,EAAE,CAAC;YAEhB,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;YACtD,IAAI,CAAC,eAAe,EAAE;gBACpB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;aACvE;YAED,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,eAAe,EAAE;gBACxC,UAAU,EAAE,kBAAkB;aAC/B,CAAC,CAAC;SACJ;QACD,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,sBAAsB;QAC1B,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAChC,6BAA6B;YAC7B,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC1C,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YACzB,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,MAAM,UAAU,GAAa,EAAE,CAAC;YAEhC,KAAK,MAAM,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;gBACnC,iCAAiC;gBACjC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;oBACtC,OAAO,EAAE,OAAO,CAAC,EAAE;oBACnB,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;gBAEH,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;oBAAE,SAAS;gBAEvC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC;gBAEzC,kDAAkD;gBAClD,MAAM,WAAW,GAAkB;oBACjC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;oBACnD,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC;oBACzC,KAAK,EAAE,QAAQ;oBACf,OAAO,EAAE,QAAQ;oBACjB,IAAI,EAAE,OAAO,CAAC,WAAW,IAAI,gBAAgB;oBAC7C,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;oBACtC,aAAa,EAAE,KAAK,CAAC,EAAE;oBACvB,eAAe,EAAE,OAAO,CAAC,EAAE;oBAC3B,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBAEF,MAAM,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;gBACpE,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBAC/B,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;gBAChC,WAAW,EAAE,CAAC;aACf;YAED,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;YAErB,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC;YAC5F,MAAM,aAAa,GAAoB,EAAE,CAAC;YAC1C,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACrB,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAmB,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,uBAAuB,WAAW,uBAAuB;gBAClE,KAAK,EAAE,WAAW;gBAClB,QAAQ,EAAE,aAAa;aACxB,CAAC;SAEH;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAChG,CAAC;SACH;IACH,CAAC;IAEO,cAAc,CAAC,WAAmB;QACxC,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,WAAW,CAAC;QAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,SAAS,CAAC;QAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,YAAY,CAAC;QAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,YAAY,CAAC;QAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,UAAU,CAAC;QAC9C,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,UAAU,CAAC;QAC9C,OAAO,MAAM,WAAW,EAAE,CAAC;IAC7B,CAAC;IAEO,UAAU,CAAC,WAAmB;QACpC,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,6BAA6B,CAAC;QAClE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,gBAAgB,CAAC;QACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,oBAAoB,CAAC;QAC1D,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,gBAAgB,CAAC;QACtD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,kBAAkB,CAAC;QACtD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,sBAAsB,CAAC;QAC1D,OAAO,wBAAwB,CAAC;IAClC,CAAC;CACF;AAED,IAAI,WAAqC,CAAC;AAE1C,SAAS,cAAc;IACrB,IAAI,CAAC,WAAW,EAAE;QAChB,WAAW,GAAG,IAAI,wBAAwB,EAAE,CAAC;KAC9C;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAEY,QAAA,kBAAkB,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IACvE,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,IAAI,EAAE;QAC/B,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,cAAc,EAAE,CAAC,sBAAsB,EAAE,CAAC;YAE/D,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC9B;iBAAM;gBACL,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC9B;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uBAAuB;aACjC,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}