import { getFunctionsBaseUrl } from './apiConfig';
import { auth, firestore as db, functions } from '../firebase';
import { collection, query, where, orderBy, limit, getDocs, doc, getDoc, setDoc, addDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { signInWithCustomToken } from 'firebase/auth';
import { httpsCallable } from 'firebase/functions';
import {
  CreditPackage,
  UserCredits,
  CreditTransaction,
  ESimCreditCost,
  ESimRedemptionRequest,
  ESimRedemptionResult
} from '../types/credits';
import { Capacitor } from '@capacitor/core';
import { CapacitorAuthService } from './capacitorAuthService';
import { FirebaseAuthentication } from '@capacitor-firebase/authentication';

export class CreditsApiService {

  // Helper method to ensure proper authentication for Firestore operations
  private async ensureAuthentication(): Promise<boolean> {
    if (Capacitor.isNativePlatform()) {
      try {
        // Check if we have a valid Capacitor user
        const capacitorUser = await CapacitorAuthService.getCurrentUser();
        if (!capacitorUser) {
          console.error('creditsApiService.ensureAuthentication - No Capacitor user found');
          return false;
        }

        // Check if we have a valid ID token
        const token = await CapacitorAuthService.getIdToken();
        if (!token) {
          console.error('creditsApiService.ensureAuthentication - No valid ID token');
          return false;
        }

        console.log('creditsApiService.ensureAuthentication - Authentication verified for user:', capacitorUser.uid);
        return true;
      } catch (error) {
        console.error('creditsApiService.ensureAuthentication - Error checking auth:', error);
        return false;
      }
    } else {
      // Web platform - check Firebase Auth
      const user = auth.currentUser;
      if (!user) {
        console.error('creditsApiService.ensureAuthentication - No Firebase user found');
        return false;
      }

      try {
        // Verify the token is still valid
        await user.getIdToken(true); // Force refresh
        console.log('creditsApiService.ensureAuthentication - Firebase auth verified for user:', user.uid);
        return true;
      } catch (error) {
        console.error('creditsApiService.ensureAuthentication - Firebase token invalid:', error);
        return false;
      }
    }
  }

  // Helper method to get current user from appropriate auth service
  private async getCurrentUser() {
    console.log('creditsApiService.getCurrentUser - Platform:', Capacitor.isNativePlatform() ? 'Native' : 'Web');

    // First ensure authentication is valid
    const isAuthenticated = await this.ensureAuthentication();
    if (!isAuthenticated) {
      console.error('creditsApiService.getCurrentUser - Authentication check failed');
      return null;
    }

    if (Capacitor.isNativePlatform()) {
      // Try multiple approaches to get the user
      console.log('creditsApiService.getCurrentUser - Trying CapacitorAuthService...');
      let user = await CapacitorAuthService.getCurrentUser();
      console.log('creditsApiService.getCurrentUser - Capacitor user:', user);

      if (!user) {
        console.log('creditsApiService.getCurrentUser - Capacitor user is null, trying direct Firebase call...');
        try {
          const result = await FirebaseAuthentication.getCurrentUser();
          console.log('creditsApiService.getCurrentUser - Direct Firebase result:', result);
          if (result && result.user) {
            user = {
              uid: result.user.uid,
              email: result.user.email,
              displayName: result.user.displayName,
              photoURL: result.user.photoUrl,
              emailVerified: result.user.emailVerified || false
            };
            console.log('creditsApiService.getCurrentUser - Manually mapped user:', user);
          }
        } catch (error) {
          console.error('creditsApiService.getCurrentUser - Direct Firebase call failed:', error);
        }
      }

      return user;
    } else {
      const user = auth.currentUser;
      console.log('creditsApiService.getCurrentUser - Firebase user:', user);
      return user;
    }
  }

  // Helper method to get ID token from appropriate auth service
  private async getIdToken(): Promise<string> {
    if (Capacitor.isNativePlatform()) {
      const token = await CapacitorAuthService.getIdToken();
      if (!token) {
        throw new Error('Failed to get ID token');
      }
      return token;
    } else {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('Authentication required');
      }
      return await user.getIdToken();
    }
  }

  // Get all available credit packages (no auth required)
  async getCreditPackages(): Promise<CreditPackage[]> {
    try {
      // Fetch from Firestore
      const q = query(
        collection(db, 'credit_packages'),
        where('active', '==', true)
      );
      const snapshot = await getDocs(q);
      const packages = snapshot.docs.map(doc => doc.data() as CreditPackage);
      // Sort by price in JavaScript instead of Firestore
      return packages.sort((a, b) => a.price - b.price);
    } catch (error) {
      console.error('Error fetching credit packages:', error);
      return [];
    }
  }

  // async syncStripeProducts(): Promise<{ success: boolean; message: string; count?: number }> {
  //   try {
  //     const response = await fetch(`${getFunctionsBaseUrl()}/syncStripeProducts`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json'
  //       }
  //     });
      
  //     if (!response.ok) {
  //       throw new Error(`HTTP error! status: ${response.status}`);
  //     }
      
  //     return await response.json();
  //   } catch (error) {
  //     console.error('Error syncing Stripe products:', error);
  //     return { success: false, message: 'Failed to sync products' };
  //   }
  // }

  // Get user's current credit balance
  async getUserCredits(): Promise<UserCredits | null> {
    const user = await this.getCurrentUser();
    if (!user) throw new Error('Authentication required');

    // On mobile, use backend API with proper CORS handling (same as transactions)
    if (Capacitor.isNativePlatform()) {
      try {
        console.log('creditsApiService.getUserCredits - Using backend API for mobile...');

        const idToken = await this.getIdToken();
        if (!idToken) {
          throw new Error('No ID token available');
        }

        const baseUrl = getFunctionsBaseUrl();
        console.log('creditsApiService.getUserCredits - Backend URL:', `${baseUrl}/getUserCreditsHttp`);

        const response = await fetch(`${baseUrl}/getUserCreditsHttp`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${idToken}`
          },
          body: JSON.stringify({})
        });

        console.log('creditsApiService.getUserCredits - Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('creditsApiService.getUserCredits - Response error:', errorText);
          throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
        }

        const data = await response.json();
        console.log('creditsApiService.getUserCredits - Backend API response:', data);
        console.log('creditsApiService.getUserCredits - data.totalCredits:', data.totalCredits);
        console.log('creditsApiService.getUserCredits - data keys:', Object.keys(data));

        // Backend returns credits object directly, not wrapped in .credits
        return data.userId ? data : null;
      } catch (error) {
        console.error('creditsApiService.getUserCredits - Backend API error:', error);
        return null;
      }
    }

    // On web, use direct Firestore access
    try {
      const docRef = doc(db, 'users', user.uid, 'credits', 'balance');
      const docSnap = await getDoc(docRef);
      return docSnap.exists() ? docSnap.data() as UserCredits : null;
    } catch (error) {
      console.error('Error fetching user credits:', error);
      return null;
    }
  }

  // Initialize user credits (for new users)
  async initializeUserCredits(): Promise<UserCredits> {
    const user = await this.getCurrentUser();
    if (!user) throw new Error('Authentication required');

    // On mobile, use backend API to avoid Firestore permission issues
    if (Capacitor.isNativePlatform()) {
      try {
        console.log('creditsApiService.initializeUserCredits - Using backend API for mobile...');

        const idToken = await this.getIdToken();
        if (!idToken) {
          throw new Error('No ID token available');
        }

        const baseUrl = getFunctionsBaseUrl();
        console.log('creditsApiService.initializeUserCredits - Backend URL:', `${baseUrl}/initializeUserCredits`);

        const response = await fetch(`${baseUrl}/initializeUserCredits`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${idToken}`
          },
          body: JSON.stringify({})
        });

        console.log('creditsApiService.initializeUserCredits - Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('creditsApiService.initializeUserCredits - Response error:', errorText);
          throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
        }

        const data = await response.json();
        console.log('creditsApiService.initializeUserCredits - Backend API response:', data);

        return data.userCredits;
      } catch (error) {
        console.error('creditsApiService.initializeUserCredits - Backend API error:', error);
        throw error;
      }
    }

    // Direct Firestore access (web)
    const userCredits: UserCredits = {
      userId: user.uid,
      totalCredits: 0,
      lifetimeCredits: 0,
      lifetimeSpent: 0,
      pendingCredits: 0,
      lastUpdated: new Date(),
      createdAt: new Date()
    };

    const docRef = doc(db, 'users', user.uid, 'credits', 'balance');
    await setDoc(docRef, userCredits);
    return userCredits;
  }

  // Get user's credit transaction history
  async getUserCreditTransactions(limitCount: number = 50): Promise<CreditTransaction[]> {
    console.log('creditsApiService.getUserCreditTransactions - Starting...');

    const user = await this.getCurrentUser();
    console.log('creditsApiService.getUserCreditTransactions - User:', user?.uid);

    if (!user || !user.uid) {
      console.error('creditsApiService.getUserCreditTransactions - No authenticated user');
      throw new Error('Authentication required');
    }

    // On mobile, use backend API with proper CORS handling
    if (Capacitor.isNativePlatform()) {
      try {
        console.log('creditsApiService.getUserCreditTransactions - Using backend API for mobile...');

        const idToken = await this.getIdToken();
        if (!idToken) {
          throw new Error('No ID token available');
        }

        const baseUrl = getFunctionsBaseUrl();
        console.log('creditsApiService.getUserCreditTransactions - Backend URL:', `${baseUrl}/getUserCreditTransactions`);

        const requestBody = { limitCount };
        console.log('creditsApiService.getUserCreditTransactions - Request body:', requestBody);

        const response = await fetch(`${baseUrl}/getUserCreditTransactions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${idToken}`
          },
          body: JSON.stringify(requestBody)
        });

        console.log('creditsApiService.getUserCreditTransactions - Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('creditsApiService.getUserCreditTransactions - Response error:', errorText);
          throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
        }

        const data = await response.json();
        console.log('creditsApiService.getUserCreditTransactions - Backend API response:', data);

        return data.transactions || [];
      } catch (error) {
        console.error('creditsApiService.getUserCreditTransactions - Backend API error:', error);
        // Don't fall back to direct Firestore on mobile - it will fail with permissions
        console.log('creditsApiService.getUserCreditTransactions - Returning empty array for mobile');
        return [];
      }
    }

    // Direct Firestore access (web or fallback)
    try {
      console.log('creditsApiService.getUserCreditTransactions - Querying transactions for user:', user.uid);

      const q = query(
        collection(db, 'users', user.uid, 'credit_transactions'),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      console.log('creditsApiService.getUserCreditTransactions - Executing query...');
      const snapshot = await getDocs(q);

      console.log('creditsApiService.getUserCreditTransactions - Query successful, docs:', snapshot.docs.length);
      const transactions = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as CreditTransaction));

      console.log('creditsApiService.getUserCreditTransactions - Returning transactions:', transactions.length);
      return transactions;
    } catch (error) {
      console.error('creditsApiService.getUserCreditTransactions - Error fetching credit transactions:', error);

      // Log more details about the error
      if (error instanceof Error) {
        console.error('creditsApiService.getUserCreditTransactions - Error message:', error.message);
        console.error('creditsApiService.getUserCreditTransactions - Error stack:', error.stack);
      }

      // Re-throw the error so the calling code can handle it appropriately
      throw error;
    }
  }

  // Get eSIM credit costs for redemption
  async getESimCreditCosts(): Promise<ESimCreditCost[]> {
    try {
      const q = query(collection(db, 'esim_credit_costs'), where('active', '==', true));
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => doc.data() as ESimCreditCost);
    } catch (error) {
      console.error('Error fetching eSIM credit costs:', error);
      return [];
    }
  }

  // Get specific eSIM credit cost
  async getESimCreditCost(planId: number, countryCode: string): Promise<ESimCreditCost | null> {
    try {
      const q = query(
        collection(db, 'esim_credit_costs'),
        where('planId', '==', planId),
        where('countryCode', '==', countryCode),
        where('active', '==', true)
      );
      const snapshot = await getDocs(q);
      return snapshot.empty ? null : snapshot.docs[0].data() as ESimCreditCost;
    } catch (error) {
      console.error('Error fetching eSIM credit cost:', error);
      return null;
    }
  }

  // Redeem credits for eSIM
  async redeemCreditsForESim(request: ESimRedemptionRequest): Promise<ESimRedemptionResult> {
    const user = await this.getCurrentUser();
    if (!user) throw new Error('Authentication required');

    try {
      const idToken = await this.getIdToken();
      const response = await fetch(`${getFunctionsBaseUrl()}/redeemCreditsForESim`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        },
        body: JSON.stringify(request)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error redeeming credits:', error);
      return { success: false, error: 'Failed to redeem credits' };
    }
  }

  // Process credit purchase (called after successful payment)
  async processCreditPurchase(
    packageId: string,
    transactionId: string,
    customerEmail: string,
    customerName: string
  ): Promise<{ success: boolean; error?: string }> {
    console.log('processCreditPurchase - Starting...');
    const user = await this.getCurrentUser();
    console.log('processCreditPurchase - User from getCurrentUser:', user);
    console.log('processCreditPurchase - Platform:', Capacitor.isNativePlatform() ? 'Native' : 'Web');

    if (!user) {
      console.error('processCreditPurchase - No user found!');
      throw new Error('Authentication required');
    }

    try {
      const idToken = await this.getIdToken();
      const response = await fetch(`${getFunctionsBaseUrl()}/processCreditPurchase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        },
        body: JSON.stringify({
          packageId,
          transactionId,
          customerEmail,
          customerName,
          platform: 'web'
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error processing credit purchase:', error);
      return { success: false, error: 'Failed to process purchase' };
    }
  }

  // Get user's credit summary for dashboard
  async getUserCreditsSummary(): Promise<{
    currentBalance: number;
    lifetimeCredits: number;
    lifetimeSpent: number;
    recentTransactions: CreditTransaction[];
  }> {
    const [userCredits, recentTransactions] = await Promise.all([
      this.getUserCredits(),
      this.getUserCreditTransactions(10)
    ]);

    return {
      currentBalance: userCredits?.totalCredits || 0,
      lifetimeCredits: userCredits?.lifetimeCredits || 0,
      lifetimeSpent: userCredits?.lifetimeSpent || 0,
      recentTransactions
    };
  }

  // Check if user has enough credits for a specific eSIM
  // async canAffordESim(planId: number, countryCode: string): Promise<{
  //   canAfford: boolean;
  //   currentCredits: number;
  //   requiredCredits: number;
  //   shortfall?: number;
  // }> {
  //   const [userCredits, esimCost] = await Promise.all([
  //     this.getUserCredits(),
  //     this.getESimCreditCost(planId, countryCode)
  //   ]);

  //   const currentCredits = userCredits?.totalCredits || 0;
  //   const requiredCredits = esimCost?.creditCost || 0;
  //   const canAfford = currentCredits >= requiredCredits;

  //   return {
  //     canAfford,
  //     currentCredits,
  //     requiredCredits,
  //     shortfall: canAfford ? undefined : requiredCredits - currentCredits
  //   };
  // }

  // Get recommended credit package based on shortfall
  async getRecommendedPackage(shortfall: number): Promise<CreditPackage | null> {
    const packages = await this.getCreditPackages();
    
    // Find the smallest package that covers the shortfall
    const suitablePackages = packages
      .filter(pkg => pkg.credits >= shortfall)
      .sort((a, b) => a.credits - b.credits);

    return suitablePackages.length > 0 ? suitablePackages[0] : null;
  }

  // Validate if user can make a purchase (has valid auth, etc.)
  async validatePurchaseEligibility(): Promise<{ eligible: boolean; reason?: string }> {
    const user = await this.getCurrentUser();

    if (!user) {
      return { eligible: false, reason: 'Authentication required' };
    }

    if (!user.emailVerified) {
      return { eligible: false, reason: 'Email verification required' };
    }

    return { eligible: true };
  }
}

// Export singleton instance
export const creditsApiService = new CreditsApiService();
