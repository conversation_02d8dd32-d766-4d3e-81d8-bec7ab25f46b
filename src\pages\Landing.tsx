import React from 'react';
import {
  IonContent,
  IonPage,
  IonButton,
  IonGrid,
  IonRow,
  IonCol,
  IonIcon,
  IonText,
  IonCard,
  IonCardContent,
  isPlatform
} from '@ionic/react';
import { rocket, globe, shieldCheckmark, wifi, arrowForward, headset, logoA<PERSON>le, logoGooglePlaystore } from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import './Landing.css';

import { useAuth } from '../contexts/AuthContext'; // Import auth context
import { Redirect } from 'react-router-dom';

const Landing: React.FC = () => {
  const history = useHistory();
  const { currentUser } = useAuth(); // Get auth state

  if (currentUser) {
    return <Redirect to="/countries" />;
  }

  if (isPlatform('mobile')) {
    return <Redirect to="/onboarding" />;
  }

  return (
    <IonPage>
      <IonContent fullscreen className="landing-page">

        {/* CUSTOM NAVBAR */}
        <div className="landing-navbar">
          <div className="navbar-logo">KrakenSim</div>
          <div className="navbar-links">
            <span onClick={() => document.getElementById('browse-section')?.scrollIntoView({ behavior: 'smooth' })}>Browse eSIMs</span>
            <span onClick={() => document.getElementById('features-section')?.scrollIntoView({ behavior: 'smooth' })}>Why Us</span>
            <span onClick={() => document.getElementById('testimonials-section')?.scrollIntoView({ behavior: 'smooth' })}>Reviews</span>
            <span onClick={() => document.getElementById('support-section')?.scrollIntoView({ behavior: 'smooth' })}>Support</span>
          </div>
          <div className="navbar-actions">
            <IonButton fill="clear" className="login-btn" onClick={() => history.push('/login')}>Log In</IonButton>
            <IonButton shape="round" className="get-app-btn" onClick={() => document.getElementById('support-section')?.scrollIntoView({ behavior: 'smooth' })}>Get App</IonButton>
          </div>
        </div>

        {/* HERO SECTION */}
        <div className="hero-section">
          <div className="hero-content">
            <h1 className="hero-title">
              <span className="gradient-text">Global Connectivity</span>
              <br />
              <span className="white-text">Zero Hassle.</span>
            </h1>
            <p className="hero-subtitle animate-fade-in">
              Instant eSIMs for 150+ countries. No roaming fees. No plastic.
              Just pure connection.
            </p>
            <div className="hero-actions">
              <IonButton
                shape="round"
                size="large"
                className="hero-cta"
                onClick={() => history.push('/countries')}
              >
                Find Your Destination
                <IonIcon slot="end" icon={arrowForward} />
              </IonButton>
            </div>
          </div>
          <div className="hero-visual">
            <div className="glow-sphere"></div>
            <IonIcon icon={globe} className="floating-globe" />
          </div>
        </div>

        {/* BROWSE eSIMs PREVIEW */}
        <div id="browse-section" className="browse-section">
          <IonText color="light">
            <h2 className="section-title">Popular Destinations</h2>
            <p className="section-subtitle">Choose from 150+ countries</p>
          </IonText>

          <div className="destinations-grid">
            {/* USA */}
            <div className="destination-card" onClick={() => history.push('/countries')}>
              <div className="flag-icon">🇺🇸</div>
              <h3>USA</h3>
              <p>From $3.50</p>
            </div>
            {/* Japan */}
            <div className="destination-card" onClick={() => history.push('/countries')}>
              <div className="flag-icon">🇯🇵</div>
              <h3>Japan</h3>
              <p>From $4.00</p>
            </div>
            {/* Europe */}
            <div className="destination-card" onClick={() => history.push('/countries')}>
              <div className="flag-icon">🇪🇺</div>
              <h3>Europe</h3>
              <p>From $5.00</p>
            </div>
            {/* UK */}
            <div className="destination-card" onClick={() => history.push('/countries')}>
              <div className="flag-icon">🇬🇧</div>
              <h3>United Kingdom</h3>
              <p>From $4.50</p>
            </div>
          </div>

          <IonButton fill="outline" color="light" shape="round" onClick={() => history.push('/countries')} style={{ marginTop: '2rem' }}>
            View All 150+ Countries
          </IonButton>
        </div>

        {/* FEATURES GRID */}
        <div id="features-section" className="features-section">
          <IonGrid>
            <IonRow>
              <IonCol size="12" sizeMd="4">
                <div className="feature-card">
                  <div className="icon-box blue">
                    <IonIcon icon={rocket} />
                  </div>
                  <h3>Instant Activation</h3>
                  <p>Get online in minutes. Scan a QR code and you're connected instantly.</p>
                </div>
              </IonCol>
              <IonCol size="12" sizeMd="4">
                <div className="feature-card">
                  <div className="icon-box purple">
                    <IonIcon icon={wifi} />
                  </div>
                  <h3>Global Coverage</h3>
                  <p>Reliable 4G/5G data in over 150 countries. Roam without the bill shock.</p>
                </div>
              </IonCol>
              <IonCol size="12" sizeMd="4">
                <div className="feature-card">
                  <div className="icon-box green">
                    <IonIcon icon={shieldCheckmark} />
                  </div>
                  <h3>Secure & Private</h3>
                  <p>No more shady public WiFi. Keep your connection private and secure anywhere.</p>
                </div>
              </IonCol>
            </IonRow>
          </IonGrid>
        </div>

        {/* TESTIMONIALS */}
        <div id="testimonials-section" className="testimonials-section">
          <h2 className="section-title">Loved by Travelers</h2>
          <div className="testimonials-grid">
            <div className="testimonial-card">
              <div className="stars">⭐⭐⭐⭐⭐</div>
              <p>"Saved me over $200 on my trip to Japan. Setup was super easy."</p>
              <div className="user">- Sarah J.</div>
            </div>
            <div className="testimonial-card">
              <div className="stars">⭐⭐⭐⭐⭐</div>
              <p>"Finally an app that just works. Instant data as soon as I landed."</p>
              <div className="user">- Michael T.</div>
            </div>
            <div className="testimonial-card">
              <div className="stars">⭐⭐⭐⭐⭐</div>
              <p>"Excellent support team. Helped me set up in minutes at 2 AM."</p>
              <div className="user">- Elena R.</div>
            </div>
          </div>
        </div>

        {/* TRUST SIGNALS */}
        <div className="trust-strip">
          <div className="trust-item">
            <IonIcon icon={headset} />
            <span>24/7 Support</span>
          </div>
          <div className="trust-item">
            <IonIcon icon={globe} />
            <span>150+ Countries</span>
          </div>
          <div className="trust-item">
            <IonIcon icon={shieldCheckmark} />
            <span>99.9% Reliability</span>
          </div>
        </div>

        {/* SUPPORT / CONTACT CTA & APP DOWNLOAD */}
        <div id="support-section" className="cta-section">
          <h2>Ready to travel smarter?</h2>
          <p>Join thousands of travelers saving on roaming today.</p>

          <div className="app-download-container">
            <div className="store-badge" onClick={() => window.open(import.meta.env.REACT_APP_APPSTORE_URL || 'https://apps.apple.com/app/id123456789', '_blank')}>
              <IonIcon icon={logoApple} />
              <div className="badge-text">
                <small>Download on the</small>
                <span>App Store</span>
              </div>
            </div>
            <div className="store-badge" onClick={() => window.open(import.meta.env.REACT_APP_PLAYSTORE_URL || 'https://play.google.com/store/apps/details?id=com.esimnumero.app', '_blank')}>
              <IonIcon icon={logoGooglePlaystore} />
              <div className="badge-text">
                <small>GET IT ON</small>
                <span>Google Play</span>
              </div>
            </div>
          </div>

          <div className="cta-secondary-actions">
            <IonButton fill="outline" className="contact-support-btn" onClick={() => window.location.href = 'mailto:<EMAIL>'}>Contact Support</IonButton>
          </div>
        </div>

        {/* FOOTER */}
        <div className="landing-footer">
          <div className="footer-content">
            <div className="footer-col">
              <h4>KrakenSim</h4>
              <p>Global eSIM connectivity made simple.</p>
            </div>
            <div className="footer-col">
              <h4>Legal</h4>
              <span onClick={() => history.push('/terms')}>Terms of Service</span>
              <span onClick={() => history.push('/privacy')}>Privacy Policy</span>
            </div>
            <div className="footer-col">
              <h4>Support</h4>
              <span onClick={() => window.location.href = 'mailto:<EMAIL>'}>Contact Us</span>
              <span>Help Center</span>
            </div>
          </div>
          <p className="copyright">© {new Date().getFullYear()} KrakenSim. All rights reserved.</p>
        </div>

      </IonContent>
    </IonPage>
  );
};

export default Landing;
