<?xml version='1.0' encoding='utf-8'?>
<widget id="com.esimnumero.global" version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>KrakenSim</name>
    <description>
        KrakenSim - Global eSIM connectivity for travelers
    </description>
    <author email="<EMAIL>" href="http://ionicframework.com/">
        KrakenSim Team
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    
    <!-- Universal Links / App Links Configuration -->
    <plugin name="cordova-plugin-customurlscheme" spec="~5.0.2">
        <variable name="URL_SCHEME" value="esimnumero" />
        <variable name="ANDROID_SCHEME" value="esimnumero" />
        <variable name="ANDROID_HOST" value="app" />
        <variable name="ANDROID_PATHPREFIX" value="/login" />
    </plugin>
    
    <!-- Universal Links for iOS -->
    <plugin name="cordova-plugin-universal-links" spec="~1.2.1">
        <variable name="UNIVERSAL_LINKS_DOMAIN" value="esimnumero.com" />
        <variable name="UNIVERSAL_LINKS_PATH" value="/app/*" />
    </plugin>
    
    <!-- Android App Links -->
    <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application/activity[@android:name='MainActivity']">
        <intent-filter android:autoVerify="true">
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="https"
                  android:host="esimnumero.com"
                  android:pathPrefix="/app" />
        </intent-filter>
        <intent-filter>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="esimnumero" />
        </intent-filter>
    </edit-config>
    
    <!-- iOS URL Scheme -->
    <edit-config file="*-Info.plist" mode="merge" target="CFBundleURLTypes">
        <array>
            <dict>
                <key>CFBundleURLName</key>
                <string>com.esimnumero.app</string>
                <key>CFBundleURLSchemes</key>
                <array>
                    <string>esimnumero</string>
                </array>
            </dict>
        </array>
    </edit-config>
    
    <!-- iOS Universal Links -->
    <edit-config file="*-Info.plist" mode="merge" target="com.apple.developer.associated-domains">
        <array>
            <string>applinks:esimnumero.com</string>
        </array>
    </edit-config>
    
    <platform name="android">
        <allow-intent href="market:*" />
        <!-- Android specific configurations -->
        <preference name="StatusBarOverlaysWebView" value="false" />
        <preference name="StatusBarBackgroundColor" value="#3880ff" />
        <preference name="StatusBarStyle" value="lightcontent" />
        
        <!-- Splash screen -->
        <preference name="SplashMaintainAspectRatio" value="true" />
        <preference name="SplashShowOnlyFirstTime" value="false" />
        <preference name="SplashScreen" value="screen" />
        <preference name="SplashScreenDelay" value="3000" />
        
        <!-- Network security config for HTTPS -->
        <resource-file src="resources/android/xml/network_security_config.xml" target="app/src/main/res/xml/network_security_config.xml" />
        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application">
            <application android:networkSecurityConfig="@xml/network_security_config" />
        </edit-config>
    </platform>
    
    <platform name="ios">
        <allow-intent href="itms:*" />
        <allow-intent href="itms-apps:*" />
        <!-- iOS specific configurations -->
        <preference name="StatusBarOverlaysWebView" value="false" />
        <preference name="StatusBarBackgroundColor" value="#3880ff" />
        <preference name="StatusBarStyle" value="lightcontent" />
        
        <!-- Splash screen -->
        <preference name="SplashMaintainAspectRatio" value="true" />
        <preference name="SplashShowOnlyFirstTime" value="false" />
        <preference name="SplashScreen" value="CDVLaunchScreen" />
        <preference name="SplashScreenDelay" value="3000" />
    </platform>
    
    <!-- Core Cordova plugins -->
    <plugin name="cordova-plugin-whitelist" spec="~1.3.5" />
    <plugin name="cordova-plugin-statusbar" spec="~4.0.0" />
    <plugin name="cordova-plugin-device" spec="~2.1.0" />
    <plugin name="cordova-plugin-splashscreen" spec="~6.0.2" />
    <plugin name="cordova-plugin-ionic-webview" spec="~5.0.0" />
    <plugin name="cordova-plugin-ionic-keyboard" spec="~2.2.0" />
    
    <!-- Firebase plugins -->
    <plugin name="cordova-plugin-firebase-authentication" spec="~7.0.0" />
    <plugin name="cordova-plugin-firebase-firestore" spec="~7.0.0" />
    
    <!-- Network and HTTP -->
    <plugin name="cordova-plugin-network-information" spec="~3.0.0" />
    <plugin name="cordova-plugin-advanced-http" spec="~3.3.1" />
    
    <!-- Security -->
    <plugin name="cordova-plugin-app-version" spec="~0.1.14" />
    <plugin name="cordova-plugin-fingerprint-aio" spec="~5.0.1" />
    
    <!-- Preferences -->
    <preference name="ScrollEnabled" value="false" />
    <preference name="BackupWebStorage" value="none" />
    <preference name="SplashMaintainAspectRatio" value="true" />
    <preference name="FadeSplashScreenDuration" value="300" />
    <preference name="SplashShowOnlyFirstTime" value="false" />
    <preference name="SplashScreen" value="screen" />
    <preference name="SplashScreenDelay" value="3000" />
    <preference name="AutoHideSplashScreen" value="false" />
    <preference name="ShowSplashScreenSpinner" value="false" />
</widget>
