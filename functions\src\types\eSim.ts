import * as admin from "firebase-admin";

export interface ESim {
    transactionId: string;
    countryCode: string;
    cost: string;
    plan: number;
    name: string;
    timestamp: string;
    expiration: string;
    dataInGb: number;
    status: number;
    label: string;
    userId: string;
}

export interface ESimPurchase {
    userId: string;
    plan: number;
    transactionId: string;
    timestamp: admin.firestore.FieldValue;
}

export interface ESimProfile {
    activated: number;
    ac: string;
    success: number;
    pin: string;
    topup: number;
    apn: string;
    puk: string;
    smdp: string;
    activationCode: string;
    countryCode: string;
    transactionId: string;
    plan: number;
    label: string | null;
    remainingData: string;
    totalData: string;
}

export interface ESimTopUp {
    userId: string;
    transactionId: string;
    plan: number;
    timestamp: admin.firestore.FieldValue;
}

export interface ESimUsage {
    userId: string;
    transactionId: string;
    usage: string;
    timestamp: admin.firestore.FieldValue;
}