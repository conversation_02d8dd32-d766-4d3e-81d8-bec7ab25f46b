/* MyESims Page Styles */

/* App Bar & Content Wrapper */
.content-wrapper {
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Helvetica, Arial, sans-serif;
}

/* Loading & Empty States */
.loading-container,
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
  flex-direction: column;
  gap: 20px;
  color: var(--ion-color-medium);
}

.empty-state ion-icon {
  font-size: 80px;
  color: var(--ion-color-secondary);
  opacity: 0.8;
}

.empty-state h2 {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  color: var(--ion-color-dark);
}

.empty-state p {
  font-size: 16px;
  max-width: 300px;
  margin: 0;
  line-height: 1.5;
}

/* eSIM Section Headers */
.section-title {
  margin-bottom: 16px;
  padding-left: 4px;
}

.section-title h2 {
  font-size: 20px;
  font-weight: 700;
  color: var(--ion-color-dark);
  margin: 0;
  letter-spacing: -0.5px;
}

/* Card Grid Layout */
.esim-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

/* eSIM Card - Base Styles */
.esim-card {
  background: var(--ion-card-background, #ffffff);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
}

.esim-card:active {
  transform: scale(0.98);
}

/* Card Header with Gradient */
.esim-card-header {
  background: linear-gradient(
    135deg,
    var(--ion-color-primary) 0%,
    var(--ion-color-secondary) 100%
  );
  padding: 20px;
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

/* Abstract shapes for visual interest */
.esim-card-header::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  pointer-events: none;
}

.esim-card-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 1;
}

.esim-card-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.esim-card-subtitle {
  font-size: 13px;
  opacity: 0.9;
  font-weight: 500;
}

/* Status Badges */
.status-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-badge ion-icon {
  font-size: 14px;
}

/* Card Body */
.esim-card-body {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Data Usage Visualization */
.data-usage-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.usage-header {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  font-weight: 600;
  color: var(--ion-color-medium);
}

.usage-amount {
  color: var(--ion-color-dark);
}

/* Custom Progress Bar */
.usage-track {
  height: 10px;
  background: var(--ion-color-light);
  border-radius: 5px;
  overflow: hidden;
  width: 100%;
}

.usage-fill {
  height: 100%;
  border-radius: 5px;
  background: var(--ion-color-primary);
  transition: width 0.5s ease-out, background-color 0.3s ease;
}

.usage-fill.warning {
  background: var(--ion-color-warning);
}

.usage-fill.danger {
  background: var(--ion-color-danger);
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding-top: 8px;
  border-top: 1px solid var(--ion-color-light);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--ion-color-medium);
  font-weight: 600;
}

.info-value {
  font-size: 14px;
  color: var(--ion-color-dark);
  font-weight: 600;
}

/* Action Buttons */
.esim-card-actions {
  display: grid;
  grid-template-columns: 1fr 1fr; /* Two equal columns */
  gap: 12px;
  padding: 16px 20px;
  background: var(--ion-color-light);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.action-btn {
  --border-radius: 12px;
  --box-shadow: none;
  font-weight: 600;
  font-size: 13px;
  margin: 0;
  height: 40px;
}

.action-btn ion-icon {
  font-size: 16px;
  margin-right: 6px;
}

/* Full Width Button for odd items or specific actions */
.action-btn.full-width {
  grid-column: 1 / -1;
}

/* Modal Styles */
.qr-container {
    background: var(--ion-card-background, #ffffff);
    padding: 24px;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px auto;
    max-width: 320px;
}

.qr-wrapper {
    background: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    margin-bottom: 16px;
    width: 100%;
    display: flex;
    justify-content: center;
}

.qr-code-img {
    width: 100%;
    max-width: 200px;
    height: auto;
    display: block;
}

.manual-code-box {
    background: var(--ion-color-step-50, #f4f5f8);
    padding: 16px;
    border-radius: 12px;
    width: 100%;
    margin-top: 8px;
    text-align: center;
    border: 1px solid rgba(0,0,0,0.05);
}

.code-text {
    font-family: 'SF Mono', 'Roboto Mono', monospace;
    font-size: 14px;
    color: var(--ion-text-color, #000000);
    word-break: break-all;
    font-weight: 600;
    line-height: 1.4;
    user-select: text;
}

/* Instruction List */
.instruction-list {
  counter-reset: steps;
  list-style: none;
  padding: 0;
}

.instruction-list li {
  position: relative;
  padding-left: 40px;
  margin-bottom: 20px;
  color: var(--ion-color-dark);
  line-height: 1.5;
}

.instruction-list li::before {
  counter-increment: steps;
  content: counter(steps);
  position: absolute;
  left: 0;
  top: 0;
  width: 28px;
  height: 28px;
  background: var(--ion-color-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 700;
  font-size: 14px;
}
