# Universal Links / App Links Setup Guide

## Overview
This guide helps you set up Universal Links (iOS) and App Links (Android) so that authentication redirects properly to your app instead of localhost.

## Files Created
- `config.xml` - Cordova/Ionic configuration with Universal Links
- `public/.well-known/apple-app-site-association` - iOS Universal Links
- `public/.well-known/assetlinks.json` - Android App Links
- `resources/android/xml/network_security_config.xml` - Android network security

## Setup Steps

### 1. iOS Universal Links
1. **Update Team ID**: In `public/.well-known/apple-app-site-association`, replace `TEAMID` with your Apple Developer Team ID
2. **Upload to domain**: Upload the file to `https://esimnumero.com/.well-known/apple-app-site-association`
3. **Verify**: Test at `https://branch.io/resources/universal-links/`

### 2. Android App Links
1. **Get certificate fingerprint**:
   ```bash
   keytool -list -v -keystore your-release-key.keystore
   ```
2. **Update assetlinks.json**: Replace `REPLACE_WITH_YOUR_APP_SIGNING_CERTIFICATE_SHA256_FINGERPRINT` with your SHA256 fingerprint
3. **Upload to domain**: Upload to `https://esimnumero.com/.well-known/assetlinks.json`
4. **Verify**: Test at `https://digitalassetlinks.googleapis.com/v1/statements:list?source.web.site=https://esimnumero.com`

### 3. Firebase Authentication
The Firebase config has been updated to use:
- **Custom scheme**: `esimnumero://app/login`
- **Domain**: `esimnumero.com`

### 4. Build and Test
1. **Build the app**: `ionic capacitor build android` or `ionic capacitor build ios`
2. **Test authentication**: Try Google sign-in on mobile device
3. **Verify redirect**: Should redirect to app, not localhost

## Troubleshooting

### Still redirecting to localhost?
- Verify the `.well-known` files are accessible at your domain
- Check that the app bundle ID matches the config
- Ensure the certificate fingerprint is correct for Android

### Authentication not working?
- Check Firebase console for proper domain configuration
- Verify the redirect URI in Google Cloud Console
- Test on a real device, not emulator

## Testing URLs
- iOS: `https://esimnumero.com/app/login`
- Android: `https://esimnumero.com/app/login`
- Custom scheme: `esimnumero://app/login`

## Notes
- Universal Links require HTTPS
- App Links require domain verification
- Test on real devices for best results
- May take time for Apple/Google to cache the association files
