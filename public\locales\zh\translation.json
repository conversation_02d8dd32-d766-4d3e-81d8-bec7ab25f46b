{"common": {"loading": "加载中...", "error": "错误", "success": "成功", "cancel": "取消", "confirm": "确认", "save": "保存", "delete": "删除", "edit": "编辑", "close": "关闭", "back": "返回", "next": "下一步", "previous": "上一步", "search": "搜索", "filter": "筛选", "sort": "排序", "refresh": "刷新", "unknown": "Unknown", "user": "User", "ok": "OK"}, "nav": {"home": "首页", "countries": "国家", "credits": "积分", "profile": "个人资料", "settings": "设置", "login": "登录", "logout": "注销", "terms": "服务条款", "privacy": "隐私政策", "esim": "eSIM", "my_esims": "我的eSIM"}, "my_esims": {"no_esims": "未找到eSIM", "no_esims_description": "您还没有购买任何eSIM。浏览我们的套餐以开始。", "browse_plans": "浏览套餐", "esim_profile": "eSIM资料", "activation_code": "激活码", "pin": "PIN", "puk": "PUK", "apn": "APN", "remaining_data": "剩余流量", "total_data": "总流量", "country": "国家", "section_title": "我的eSIM", "purchased_contracts": "已购买的合同", "data_usage": "数据使用", "expires": "到期", "plan": "套餐", "details": "详情", "top_up": "充值", "active": "有效", "purchased": "购买日期", "type": "类型", "one_time": "一次性", "install": "安装", "remove_plan": "删除套餐", "install_esim": "安装eSIM", "scan_camera": "用手机摄像头扫描", "manual_code": "手动激活码", "plan_details": "套餐详情", "not_available": "不适用"}, "settings": {"title": "设置", "notifications": {"title": "通知", "push": "推送通知", "pushDescription": "接收有关您的eSIM订单的更新", "permissionRequired": "需要权限", "enableInSettings": "请在设备设置中启用通知以接收更新。"}, "language": {"title": "语言与地区", "language": "语言"}, "support": {"title": "支持与帮助", "contactSupport": "联系支持", "contactSupportDescription": "获取eSIM订单帮助", "rateApp": "评价 <PERSON><PERSON><PERSON><PERSON><PERSON>", "rateAppDescription": "通过评价应用帮助我们改进", "telegram": "Telegram支持", "telegramDescription": "加入我们的Telegram频道以获得支持", "website": "支持网站", "websiteDescription": "访问我们的支持网站获取常见问题解答", "howToUse": "如何使用eSIM", "howToUseDescription": "设置和使用eSIM的分步指南"}, "app": {"title": "应用", "version": "版本"}}, "countries": {"title": "eSIM国家", "searchPlaceholder": "搜索国家...", "loadingCountries": "正在加载国家...", "noCountries": "未找到国家", "selectCountry": "选择一个国家以查看可用套餐"}, "plans": {"month": "{{count}}个月", "months": "{{count}}个月", "subtitle": "高速eSIM数据套餐", "best_value": "超值", "location": "位置", "network": "网络", "purchase": "购买", "device_required": "需要设备", "device_required_desc": "在添加数据之前，您需要一个eSIM设备。", "get_device_first": "先获取eSIM设备", "esim_active": "eSIM已激活。请在下面选择套餐。", "processing": "处理中...", "purchase_success": "✨ eSIM购买成功！交易ID：{{id}}", "purchase_failed": "❌ 购买失败：{{error}}", "congrats_first": "🎉 祝贺您获得第一个eSIM！现在您可以购买积分以在未获得更优惠的费率。", "esim_purchased_redirect": "🎉 eSIM购买成功！正在重定向到您的eSIM...", "purchase_failed_simple": "❌ eSIM购买失败。请重试。", "esim_purchase_title": "eSIM购买"}, "credits": {"title": "积分", "loadingPackages": "正在加载积分套餐...", "noPackages": "无可用积分套餐", "currentBalance": "当前余额", "purchaseCredits": "购买积分", "yourCredits": "Your Credits", "availableCredits": "Available Credits", "lifetimePurchased": "Lifetime Purchased", "totalSpent": "Total Spent", "buyCredits": "Buy Credits", "choosePackage": "Choose the perfect package for your travel needs", "mostPopular": "Most Popular", "buyCreditsButton": "Buy {{credits}} Credits", "processing": "Processing...", "recentActivity": "Recent Activity", "creditsPurchased": "Credits Purchased", "creditsSpent": "Credits Spent", "errorLoadingData": "Error loading credits data:", "placeholderDataWarning": "Using placeholder data for development. Canadian friend will fix Stripe integration.", "signInToPurchase": "Please sign in to purchase credits.", "purchaseSuccess": "Purchase completed successfully! Your credits will be added shortly.", "purchaseFailed": "<PERSON><PERSON><PERSON> failed. Please try again.", "errorPurchasing": "Error purchasing credits:", "creditsCount": "{{count}} credits", "transaction": "交易", "trust": {"support": "24/7支持", "instant": "即时交付", "secure": "安全支付"}, "legal_disclaimer_v2": "购买此套餐即表示您同意 {{appName}} 的 <1>条款</1> 和 <2>隐私政策</2>。", "packages": {"minnow": {"name": "Minnow", "vibe": "Tiny but mighty", "useCase": "Light travelers, test users"}, "tuna": {"name": "<PERSON><PERSON>", "vibe": "Reliable and steady", "useCase": "Regional users"}, "dolphin": {"name": "Dolphin", "vibe": "Smart and fast", "useCase": "Business travelers"}, "octopus": {"name": "Octopus", "vibe": "Flexible, multi-device", "useCase": "Remote workers"}, "shark": {"name": "Shark", "vibe": "Powerful, premium", "useCase": "Heavy data users"}, "whale": {"name": "Whale", "vibe": "Massive, enterprise-grade", "useCase": "Global nomads, teams"}}}, "privacy": {"title": "隐私政策", "lastUpdated": "最后更新：", "informationWeCollect": "我们收集的信息", "informationWeCollectText": "我们收集您直接提供给我们的信息，例如您创建账户、进行购买或联系我们时。", "howWeUse": "我们如何使用您的信息", "howWeUseText": "我们使用收集的信息用于：", "howWeUseList": ["提供和维护我们的服务", "处理交易", "向您发送技术通知和支持消息", "就产品和服务与您沟通"], "informationSharing": "信息共享", "informationSharingText": "我们不会在未经您同意的情况下向第三方出售、交易或转让您的个人信息，除非本政策中所述情况。", "dataSecurity": "数据安全", "dataSecurityText": "我们实施适当的安全措施，保护您的个人信息免受未经授权的访问、更改、披露或销毁。", "cookiesTracking": "<PERSON><PERSON>和跟踪", "cookiesTrackingText": "我们使用Cookie和类似的跟踪技术来跟踪我们服务上的活动并保存某些信息。", "thirdPartyServices": "第三方服务", "thirdPartyServicesText": "我们的服务可能包含指向第三方网站的链接。我们不对这些外部网站的隐私做法负责。", "childrensPrivacy": "儿童隐私", "childrensPrivacyText": "我们的服务不面向13岁以下的儿童。我们不会有意收集13岁以下儿童的个人信息。", "changesToPolicy": "隐私政策变更", "changesToPolicyText": "我们可能会不时更新我们的隐私政策。我们将通过在此页面上发布新政策来通知您任何变更。", "contactUs": "联系我们", "contactUsText": "如果您对此隐私政策有疑问，请通过**********************联系我们"}, "terms": {"title": "条款和条件", "lastUpdated": "最后更新：", "acceptanceOfTerms": "接受条款", "acceptanceOfTermsText": "访问和使用 KrakenSim，即表示您接受并同意受本协议条款和规定的约束。", "serviceDescription": "服务描述", "serviceDescriptionText": "KrakenSim 为多个国家的移动连接提供数字SIM卡服务。", "userAccounts": "用户账户", "userAccountsText": "您可以创建账户以访问附加功能，但不需要创建账户即可浏览我们的服务。", "privacy": "隐私", "privacyText": "您的隐私对我们很重要。请查看我们的隐私政策，了解我们如何收集和使用您的信息。", "prohibitedUses": "禁止用途", "prohibitedUsesText": "您不得将我们的服务用于任何非法目的或引诱他人进行非法行为。", "serviceAvailability": "服务可用性", "serviceAvailabilityText": "我们努力维持服务可用性，但无法保证不间断的访问。", "limitationOfLiability": "责任限制", "limitationOfLiabilityText": "KrakenSim 不对任何间接、附带、特殊、后果性或惩罚性损害负责。", "changesToTerms": "条款变更", "changesToTermsText": "我们保留随时修改这些条款的权利。变更将在发布后立即生效。", "contactInformation": "联系信息", "contactInformationText": "对于这些条款的问题，请通过**********************联系我们"}, "tutorial": {"title": "如何使用eSIM", "backButton": "设置", "intro": {"title": "什么是eSIM？", "description": "eSIM（嵌入式SIM）是一种数字SIM卡，允许您在不使用物理SIM卡的情况下激活蜂窝计划。非常适合希望即时连接且无需更换SIM卡的旅行者。"}, "steps": {"title": "设置说明", "compatibility": {"title": "第1步：检查设备兼容性", "subtitle": "确保您的设备支持eSIM", "listTitle": "兼容设备：", "iphone": "iPhone：iPhone XS、XR、11、12、13、14、15及更高版本", "samsung": "Samsung：Galaxy S20、S21、S22、S23、Note 20及更高版本", "google": "Google：Pixel 3、4、5、6、7、8及更高版本", "other": "其他：自2019年以来的大多数旗舰手机", "check": "如何检查：设置 → 关于 → 查找“数字SIM”或“eSIM”"}, "purchase": {"title": "第2步：购买您的eSIM", "subtitle": "购买积分并兑换eSIM计划", "step1": "在应用程序中浏览可用国家", "step2": "选择适合您需求的数据计划", "step3": "如果积分不足，请购买积分", "step4": "兑换积分为您选择的eSIM计划", "step5": "在“我的eSIM”中接收激活详情"}, "install": {"title": "第3步：安装eSIM配置文件", "subtitle": "将eSIM添加到您的设备", "iphoneTitle": "对于iPhone：", "iphoneStep1": "设置 → 蜂窝网络 → 添加蜂窝计划", "iphoneStep2": "扫描“我的eSIM”中的二维码或手动输入代码", "iphoneStep3": "按照屏幕上的说明操作", "iphoneStep4": "标记您的计划（例如，“旅行数据”）", "androidTitle": "对于Android：", "androidStep1": "设置 → 网络和互联网 → 移动网络", "androidStep2": "点击“添加运营商”或“添加移动计划”", "androidStep3": "扫描二维码或输入激活码", "androidStep4": "按照设置说明操作"}, "configure": {"title": "第4步：配置设置", "subtitle": "设置数据使用和首选项", "listTitle": "重要设置：", "step1": "数据漫游：为eSIM计划开启", "step2": "默认线路：选择用于通话/短信的SIM卡", "step3": "蜂窝数据：选择eSIM用于互联网", "step4": "APN设置：通常自动配置", "warning": "重要：保持您的原始SIM卡用于通话，除非另有说明，否则仅使用eSIM进行数据传输。"}, "activate": {"title": "第5步：激活并使用", "subtitle": "开始使用您的eSIM数据", "step1": "安装后重新启动设备", "step2": "检查eSIM的信号强度", "step3": "测试互联网连接", "step4": "在设置中监控数据使用情况", "step5": "如果需要，通过应用程序充值", "troubleshootingTitle": "故障排除：", "trouble1": "无信号：检查数据漫游是否已启用", "trouble2": "速度慢：尝试在4G/5G之间切换", "trouble3": "无法连接：重新启动设备或重置网络设置"}}, "tips": {"title": "提示和最佳实践", "tip1Title": "旅行前安装", "tip1Desc": "在旅行前连接WiFi时设置您的eSIM", "tip2Title": "监控数据使用", "tip2Desc": "跟踪您的消耗以避免超额", "tip3Title": "保存激活详情", "tip3Desc": "保存二维码截图", "tip4Title": "测试连接", "tip4Desc": "在离开WiFi覆盖范围之前验证您的eSIM是否工作"}, "support": {"text": "需要帮助？通过设置联系我们的支持团队。", "button": "转到设置"}}}