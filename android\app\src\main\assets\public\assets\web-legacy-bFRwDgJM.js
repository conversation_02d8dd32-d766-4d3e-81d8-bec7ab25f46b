System.register(["./index-legacy-CIr0Blaj.js"],function(e,t){"use strict";var n,r,i,s,a,o,c,l,u,h,d,R,I,p,E,m,g,w,N,S,_,y,O,f,U,C,k,T,v,P,A,W,b,F,G,L,D,V,M,H,B,$,x,Y,j,q,J;return{setters:[e=>{n=e.W,r=e.n,i=e.o,s=e.q,a=e.t,o=e.u,c=e.v,l=e.x,u=e.O,h=e.y,d=e.P,R=e.E,I=e.F,p=e.G,E=e.z,m=e.R,g=e.A,w=e.T,N=e.B,S=e.C,_=e.D,y=e.H,O=e.I,f=e.J,U=e.L,C=e.M,k=e.N,T=e.Q,v=e.S,P=e.U,A=e.V,W=e.X,b=e.Y,F=e.Z,G=e._,L=e.$,D=e.a0,V=e.a1,M=e.a2,H=e.a3,B=e.a4,$=e.a5,x=e.a6,Y=e.a7,j=e.a8,q=e.a9,J=e.aa}],execute:function(){class t extends n{constructor(){super(),this.lastConfirmationResult=new Map;const e=r();e.onAuthStateChanged(e=>this.handleAuthStateChange(e)),e.onIdTokenChanged(e=>{this.handleIdTokenChange(e)})}async applyActionCode(e){const t=r();return i(t,e.oobCode)}async createUserWithEmailAndPassword(e){const t=r(),n=await s(t,e.email,e.password);return this.createSignInResult(n,null)}async confirmPasswordReset(e){const t=r();return a(t,e.oobCode,e.newPassword)}async confirmVerificationCode(e){const{verificationCode:n,verificationId:r}=e,i=this.lastConfirmationResult.get(r);if(!i)throw new Error(t.ERROR_CONFIRMATION_RESULT_MISSING);const s=await i.confirm(n);return this.createSignInResult(s,null)}async deleteUser(){const e=r().currentUser;if(!e)throw new Error(t.ERROR_NO_USER_SIGNED_IN);return o(e)}async fetchSignInMethodsForEmail(e){const t=r();return{signInMethods:await c(t,e.email)}}async getPendingAuthResult(){this.throwNotAvailableError()}async getCurrentUser(){const e=r();return{user:this.createUserResult(e.currentUser)}}async getIdToken(e){const n=r();if(!n.currentUser)throw new Error(t.ERROR_NO_USER_SIGNED_IN);return{token:await n.currentUser.getIdToken(null==e?void 0:e.forceRefresh)||""}}async getRedirectResult(){const e=r(),t=await l(e),n=t?u.credentialFromResult(t):null;return this.createSignInResult(t,n)}async getTenantId(){return{tenantId:r().tenantId}}async isSignInWithEmailLink(e){const t=r();return{isSignInWithEmailLink:h(t,e.emailLink)}}async linkWithApple(e){const t=new u(d.APPLE);this.applySignInOptions(e||{},t);const n=await this.linkCurrentUserWithPopupOrRedirect(t,null==e?void 0:e.mode),r=u.credentialFromResult(n);return this.createSignInResult(n,r)}async linkWithEmailAndPassword(e){const t=R.credential(e.email,e.password),n=await this.linkCurrentUserWithCredential(t);return this.createSignInResult(n,t)}async linkWithEmailLink(e){const t=R.credentialWithLink(e.email,e.emailLink),n=await this.linkCurrentUserWithCredential(t);return this.createSignInResult(n,t)}async linkWithFacebook(e){const t=new I;this.applySignInOptions(e||{},t);const n=await this.linkCurrentUserWithPopupOrRedirect(t,null==e?void 0:e.mode),r=I.credentialFromResult(n);return this.createSignInResult(n,r)}async linkWithGameCenter(){this.throwNotAvailableError()}async linkWithGithub(e){const t=new p;this.applySignInOptions(e||{},t);const n=await this.linkCurrentUserWithPopupOrRedirect(t,null==e?void 0:e.mode),r=p.credentialFromResult(n);return this.createSignInResult(n,r)}async linkWithGoogle(e){const t=new E;this.applySignInOptions(e||{},t);const n=await this.linkCurrentUserWithPopupOrRedirect(t,null==e?void 0:e.mode),r=E.credentialFromResult(n);return this.createSignInResult(n,r)}async linkWithMicrosoft(e){const t=new u(d.MICROSOFT);this.applySignInOptions(e||{},t);const n=await this.linkCurrentUserWithPopupOrRedirect(t,null==e?void 0:e.mode),r=u.credentialFromResult(n);return this.createSignInResult(n,r)}async linkWithOpenIdConnect(e){const t=new u(e.providerId);this.applySignInOptions(e,t);const n=await this.linkCurrentUserWithPopupOrRedirect(t,e.mode),r=u.credentialFromResult(n);return this.createSignInResult(n,r)}async linkWithPhoneNumber(e){const n=r().currentUser;if(!n)throw new Error(t.ERROR_NO_USER_SIGNED_IN);if(!e.phoneNumber)throw new Error(t.ERROR_PHONE_NUMBER_MISSING);if(!(e.recaptchaVerifier&&e.recaptchaVerifier instanceof m))throw new Error(t.ERROR_RECAPTCHA_VERIFIER_MISSING);try{const r=await g(n,e.phoneNumber,e.recaptchaVerifier),{verificationId:i}=r;this.lastConfirmationResult.set(i,r);const s={verificationId:i};this.notifyListeners(t.PHONE_CODE_SENT_EVENT,s)}catch(i){const e={message:this.getErrorMessage(i)};this.notifyListeners(t.PHONE_VERIFICATION_FAILED_EVENT,e)}}async linkWithPlayGames(){this.throwNotAvailableError()}async linkWithTwitter(e){const t=new w;this.applySignInOptions(e||{},t);const n=await this.linkCurrentUserWithPopupOrRedirect(t,null==e?void 0:e.mode),r=w.credentialFromResult(n);return this.createSignInResult(n,r)}async linkWithYahoo(e){const t=new u(d.YAHOO);this.applySignInOptions(e||{},t);const n=await this.linkCurrentUserWithPopupOrRedirect(t,null==e?void 0:e.mode),r=u.credentialFromResult(n);return this.createSignInResult(n,r)}async reload(){const e=r().currentUser;if(!e)throw new Error(t.ERROR_NO_USER_SIGNED_IN);return N(e)}async revokeAccessToken(e){const t=r();return S(t,e.token)}async sendEmailVerification(e){const n=r().currentUser;if(!n)throw new Error(t.ERROR_NO_USER_SIGNED_IN);return _(n,null==e?void 0:e.actionCodeSettings)}async sendPasswordResetEmail(e){const t=r();return y(t,e.email,e.actionCodeSettings)}async sendSignInLinkToEmail(e){const t=r();return O(t,e.email,e.actionCodeSettings)}async setLanguageCode(e){r().languageCode=e.languageCode}async setPersistence(e){const t=r();switch(e.persistence){case f.BrowserLocal:await U(t,J);break;case f.BrowserSession:await U(t,q);break;case f.IndexedDbLocal:await U(t,j);break;case f.InMemory:await U(t,Y)}}async setTenantId(e){r().tenantId=e.tenantId}async signInAnonymously(){const e=r(),t=await C(e);return this.createSignInResult(t,null)}async signInWithApple(e){const t=new u(d.APPLE);this.applySignInOptions(e||{},t);const n=await this.signInWithPopupOrRedirect(t,null==e?void 0:e.mode),r=u.credentialFromResult(n);return this.createSignInResult(n,r)}async signInWithCustomToken(e){const t=r(),n=await k(t,e.token);return this.createSignInResult(n,null)}async signInWithEmailAndPassword(e){const t=r(),n=await T(t,e.email,e.password);return this.createSignInResult(n,null)}async signInWithEmailLink(e){const t=r(),n=await v(t,e.email,e.emailLink);return this.createSignInResult(n,null)}async signInWithFacebook(e){const t=new I;this.applySignInOptions(e||{},t);const n=await this.signInWithPopupOrRedirect(t,null==e?void 0:e.mode),r=I.credentialFromResult(n);return this.createSignInResult(n,r)}async signInWithGithub(e){const t=new p;this.applySignInOptions(e||{},t);const n=await this.signInWithPopupOrRedirect(t,null==e?void 0:e.mode),r=p.credentialFromResult(n);return this.createSignInResult(n,r)}async signInWithGoogle(e){const t=new E;this.applySignInOptions(e||{},t);const n=await this.signInWithPopupOrRedirect(t,null==e?void 0:e.mode),r=E.credentialFromResult(n);return this.createSignInResult(n,r)}async signInWithMicrosoft(e){const t=new u(d.MICROSOFT);this.applySignInOptions(e||{},t);const n=await this.signInWithPopupOrRedirect(t,null==e?void 0:e.mode),r=u.credentialFromResult(n);return this.createSignInResult(n,r)}async signInWithOpenIdConnect(e){const t=new u(e.providerId);this.applySignInOptions(e,t);const n=await this.signInWithPopupOrRedirect(t,e.mode),r=u.credentialFromResult(n);return this.createSignInResult(n,r)}async signInWithPhoneNumber(e){if(!e.phoneNumber)throw new Error(t.ERROR_PHONE_NUMBER_MISSING);if(!(e.recaptchaVerifier&&e.recaptchaVerifier instanceof m))throw new Error(t.ERROR_RECAPTCHA_VERIFIER_MISSING);const n=r();try{const r=await P(n,e.phoneNumber,e.recaptchaVerifier),{verificationId:i}=r;this.lastConfirmationResult.set(i,r);const s={verificationId:i};this.notifyListeners(t.PHONE_CODE_SENT_EVENT,s)}catch(i){const e={message:this.getErrorMessage(i)};this.notifyListeners(t.PHONE_VERIFICATION_FAILED_EVENT,e)}}async signInWithPlayGames(){this.throwNotAvailableError()}async signInWithGameCenter(){this.throwNotAvailableError()}async signInWithTwitter(e){const t=new w;this.applySignInOptions(e||{},t);const n=await this.signInWithPopupOrRedirect(t,null==e?void 0:e.mode),r=w.credentialFromResult(n);return this.createSignInResult(n,r)}async signInWithYahoo(e){const t=new u(d.YAHOO);this.applySignInOptions(e||{},t);const n=await this.signInWithPopupOrRedirect(t,null==e?void 0:e.mode),r=u.credentialFromResult(n);return this.createSignInResult(n,r)}async signOut(){const e=r();await e.signOut()}async unlink(e){const n=r();if(!n.currentUser)throw new Error(t.ERROR_NO_USER_SIGNED_IN);const i=await A(n.currentUser,e.providerId);return{user:this.createUserResult(i)}}async updateEmail(e){const n=r().currentUser;if(!n)throw new Error(t.ERROR_NO_USER_SIGNED_IN);return W(n,e.newEmail)}async updatePassword(e){const n=r().currentUser;if(!n)throw new Error(t.ERROR_NO_USER_SIGNED_IN);return b(n,e.newPassword)}async updateProfile(e){const n=r().currentUser;if(!n)throw new Error(t.ERROR_NO_USER_SIGNED_IN);return F(n,{displayName:e.displayName,photoURL:e.photoUrl})}async useAppLanguage(){r().useDeviceLanguage()}async useEmulator(e){const t=r(),n=e.port||9099,i=e.scheme||"http";e.host.includes("://")?G(t,`${e.host}:${n}`):G(t,`${i}://${e.host}:${n}`)}async verifyBeforeUpdateEmail(e){const n=r().currentUser;if(!n)throw new Error(t.ERROR_NO_USER_SIGNED_IN);return L(n,null==e?void 0:e.newEmail,null==e?void 0:e.actionCodeSettings)}handleAuthStateChange(e){const n={user:this.createUserResult(e)};this.notifyListeners(t.AUTH_STATE_CHANGE_EVENT,n,!0)}async handleIdTokenChange(e){if(!e)return;const n={token:await e.getIdToken(!1)};this.notifyListeners(t.ID_TOKEN_CHANGE_EVENT,n,!0)}applySignInOptions(e,t){if(e.customParameters){const n={};e.customParameters.map(e=>{n[e.key]=e.value}),t.setCustomParameters(n)}if(e.scopes)for(const n of e.scopes)t.addScope(n)}signInWithPopupOrRedirect(e,t){const n=r();return"redirect"===t?D(n,e):V(n,e)}linkCurrentUserWithPopupOrRedirect(e,n){const i=r();if(!i.currentUser)throw new Error(t.ERROR_NO_USER_SIGNED_IN);return"redirect"===n?M(i.currentUser,e):H(i.currentUser,e)}linkCurrentUserWithCredential(e){const n=r();if(!n.currentUser)throw new Error(t.ERROR_NO_USER_SIGNED_IN);return B(n.currentUser,e)}requestAppTrackingTransparencyPermission(){this.throwNotAvailableError()}checkAppTrackingTransparencyPermission(){this.throwNotAvailableError()}createSignInResult(e,t){return{user:this.createUserResult((null==e?void 0:e.user)||null),credential:this.createCredentialResult(t),additionalUserInfo:this.createAdditionalUserInfoResult(e)}}createCredentialResult(e){if(!e)return null;const t={providerId:e.providerId};return e instanceof $&&(t.accessToken=e.accessToken,t.idToken=e.idToken,t.secret=e.secret),t}createUserResult(e){return e?{displayName:e.displayName,email:e.email,emailVerified:e.emailVerified,isAnonymous:e.isAnonymous,metadata:this.createUserMetadataResult(e.metadata),phoneNumber:e.phoneNumber,photoUrl:e.photoURL,providerData:this.createUserProviderDataResult(e.providerData),providerId:e.providerId,tenantId:e.tenantId,uid:e.uid}:null}createUserMetadataResult(e){const t={};return e.creationTime&&(t.creationTime=Date.parse(e.creationTime)),e.lastSignInTime&&(t.lastSignInTime=Date.parse(e.lastSignInTime)),t}createUserProviderDataResult(e){return e.map(e=>({displayName:e.displayName,email:e.email,phoneNumber:e.phoneNumber,photoUrl:e.photoURL,providerId:e.providerId,uid:e.uid}))}createAdditionalUserInfoResult(e){if(!e)return null;const t=x(e);if(!t)return null;const{isNewUser:n,profile:r,providerId:i,username:s}=t,a={isNewUser:n};return null!==i&&(a.providerId=i),null!==r&&(a.profile=r),null!=s&&(a.username=s),a}getErrorMessage(e){return e instanceof Object&&"message"in e&&"string"==typeof e.message?e.message:JSON.stringify(e)}throwNotAvailableError(){throw new Error("Not available on web.")}}e("FirebaseAuthenticationWeb",t),t.AUTH_STATE_CHANGE_EVENT="authStateChange",t.ID_TOKEN_CHANGE_EVENT="idTokenChange",t.PHONE_CODE_SENT_EVENT="phoneCodeSent",t.PHONE_VERIFICATION_FAILED_EVENT="phoneVerificationFailed",t.ERROR_NO_USER_SIGNED_IN="No user is signed in.",t.ERROR_PHONE_NUMBER_MISSING="phoneNumber must be provided.",t.ERROR_RECAPTCHA_VERIFIER_MISSING="recaptchaVerifier must be provided and must be an instance of RecaptchaVerifier.",t.ERROR_CONFIRMATION_RESULT_MISSING="No confirmation result with this verification id was found."}}});
