{"version": 3, "file": "lemonSqueezyService.js", "sourceRoot": "", "sources": ["../src/lemonSqueezyService.ts"], "names": [], "mappings": ";;;AAAA,gDAAgD;AAChD,wCAAwC;AACxC,iCAAiC;AACjC,8CAA+C;AAE/C,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AAgE3C,MAAM,mBAAmB;IAGvB;QACE,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAED,kEAAkE;IAC1D,sBAAsB,CAAC,IAAY,EAAE,SAAiB;QAC5D,+CAA+C;QAC/C,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;QAE9D,IAAI,CAAC,aAAa,EAAE;YAClB,WAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;SACd;QAED,IAAI;YACF,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YACxD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC;YACpE,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;YAE/D,OAAO,MAAM,CAAC,eAAe,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;SAC1D;QAAC,OAAO,KAAK,EAAE;YACd,WAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,oDAAoD;IAC5C,mBAAmB,CAAC,WAAmB;QAC7C,MAAM,UAAU,GAA2D;YACzE,QAAQ,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE;YAC7C,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE;YAC1C,SAAS,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE;YAChD,SAAS,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE;YAChD,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE;YAC5C,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE;SAC7C,CAAC;QAEF,wBAAwB;QACxB,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE;YAC3B,OAAO,UAAU,CAAC,WAAW,CAAC,CAAC;SAChC;QAED,iEAAiE;QACjE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACrD,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC7B,OAAO,KAAK,CAAC;aACd;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,qDAAqD;IAC7C,KAAK,CAAC,kBAAkB,CAAC,KAA+B;;QAC9D,MAAM,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;QAE7C,mEAAmE;QACnE,MAAM,YAAY,GAAG,CAAA,MAAA,KAAK,CAAC,IAAI,CAAC,WAAW,0CAAE,OAAO;aAChC,MAAA,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,0CAAE,OAAO,CAAA,CAAC;QAE1D,oCAAoC;QACpC,IAAI,YAAY,EAAE;YAChB,WAAM,CAAC,IAAI,CAAC,iCAAiC,YAAY,EAAE,CAAC,CAAC;YAC7D,OAAO,YAAY,CAAC;SACrB;QAED,uCAAuC;QACvC,IAAI,UAAU,EAAE;YACd,IAAI;gBACF,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBACjE,WAAM,CAAC,IAAI,CAAC,uBAAuB,UAAU,KAAK,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;gBACpE,OAAO,UAAU,CAAC,GAAG,CAAC;aACvB;YAAC,OAAO,KAAK,EAAE;gBACd,WAAM,CAAC,IAAI,CAAC,kCAAkC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;gBACpE,OAAO,IAAI,CAAC;aACb;SACF;QAED,WAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,8BAA8B;IACtB,KAAK,CAAC,gBAAgB,CAC5B,MAAc,EACd,SAAiB,EACjB,OAAe,EACf,aAAqB,EACrB,SAAc;QAEd,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QAE9B,gDAAgD;QAChD,MAAM,iBAAiB,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACjG,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE;YAC3B,OAAO,EAAE,aAAa;YACtB,MAAM;YACN,SAAS;YACT,OAAO;YACP,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;YACzD,SAAS,EAAE;gBACT,WAAW,EAAE,SAAS,CAAC,YAAY;gBACnC,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,SAAS,EAAE,SAAS,CAAC,UAAU;aAChC;SACF,CAAC,CAAC;QAEH,sBAAsB;QACtB,MAAM,cAAc,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEpG,sBAAsB;QACtB,MAAM,iBAAiB,GAAG,MAAM,cAAc,CAAC,GAAG,EAAE,CAAC;QACrD,MAAM,cAAc,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC3E,YAAY,EAAE,CAAC;YACf,eAAe,EAAE,CAAC;YAClB,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SAC1D,CAAC;QAEF,iBAAiB;QACjB,MAAM,UAAU,GAAG;YACjB,YAAY,EAAE,CAAC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,YAAY,KAAI,CAAC,CAAC,GAAG,OAAO;YAC3D,eAAe,EAAE,CAAC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,eAAe,KAAI,CAAC,CAAC,GAAG,OAAO;YACjE,aAAa,EAAE,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,aAAa,KAAI,CAAC;YACjD,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SAC1D,CAAC;QAEF,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QAEvD,4BAA4B;QAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,EAAE;aAC3B,UAAU,CAAC,OAAO,CAAC;aACnB,GAAG,CAAC,MAAM,CAAC;aACX,UAAU,CAAC,qBAAqB,CAAC;aACjC,GAAG,EAAE,CAAC;QAET,MAAM,WAAW,GAAG;YAClB,EAAE,EAAE,cAAc,CAAC,EAAE;YACrB,MAAM;YACN,IAAI,EAAE,UAAU;YAChB,MAAM,EAAE,OAAO;YACf,YAAY,EAAE,UAAU,CAAC,YAAY;YACrC,QAAQ,EAAE;gBACR,SAAS;gBACT,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;gBACnE,QAAQ,EAAE,cAAc;gBACxB,aAAa;gBACb,WAAW,EAAE,SAAS,CAAC,YAAY;gBACnC,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;aAC7B;YACD,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;SACxD,CAAC;QAEF,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAEvC,mBAAmB;QACnB,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;QAErB,WAAM,CAAC,IAAI,CAAC,SAAS,OAAO,oBAAoB,MAAM,2BAA2B,aAAa,EAAE,CAAC,CAAC;IACpG,CAAC;IAED,8BAA8B;IAC9B,KAAK,CAAC,aAAa,CAAC,IAAY,EAAE,SAAkB;QAClD,IAAI;YACF,uCAAuC;YACvC,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;gBAC9D,WAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBAC1C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC;aACvD;YAED,4BAA4B;YAC5B,MAAM,KAAK,GAA6B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEzD,WAAM,CAAC,IAAI,CAAC,oCAAoC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAExF,oCAAoC;YACpC,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,KAAK,eAAe,EAAE;gBAC7C,WAAM,CAAC,IAAI,CAAC,2BAA2B,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;gBAChE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;aAC1B;YAED,yBAAyB;YACzB,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,MAAM,EAAE;gBAC3C,WAAM,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,6BAA6B,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC/F,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;aAC1B;YAED,cAAc;YACd,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,CAAC,MAAM,EAAE;gBACX,WAAM,CAAC,KAAK,CAAC,yCAAyC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;gBACvE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,EAAE,CAAC;aACjE;YAED,gCAAgC;YAChC,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,YAAY,CAAC;YACxE,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAE1D,IAAI,CAAC,WAAW,EAAE;gBAChB,WAAM,CAAC,KAAK,CAAC,oBAAoB,WAAW,EAAE,CAAC,CAAC;gBAChD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAoB,WAAW,EAAE,EAAE,CAAC;aACrE;YAED,0EAA0E;YAC1E,MAAM,iBAAiB,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjG,MAAM,aAAa,GAAG,MAAM,iBAAiB,CAAC,GAAG,EAAE,CAAC;YAEpD,IAAI,aAAa,CAAC,MAAM,EAAE;gBACxB,WAAM,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,oBAAoB,CAAC,CAAC;gBACxD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;aAC1B;YAED,8BAA8B;YAC9B,MAAM,IAAI,CAAC,gBAAgB,CACzB,MAAM,EACN,WAAW,CAAC,SAAS,EACrB,WAAW,CAAC,OAAO,EACnB,KAAK,CAAC,IAAI,CAAC,EAAE,EACb,KAAK,CAAC,IAAI,CAAC,UAAU,CACtB,CAAC;YAEF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SAE1B;QAAC,OAAO,KAAU,EAAE;YACnB,WAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,2BAA2B;aACpD,CAAC;SACH;IACH,CAAC;CACF;AAED,qBAAqB;AACrB,IAAI,mBAAwC,CAAC;AAE7C,SAAS,sBAAsB;IAC7B,IAAI,CAAC,mBAAmB,EAAE;QACxB,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC;KACjD;IACD,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAED,6BAA6B;AAChB,QAAA,mBAAmB,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9E,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,IAAI,EAAE;QAC/B,IAAI;YACF,mEAAmE;YACnE,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAW,CAAC;YAEvD,0CAA0C;YAC1C,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE7E,MAAM,MAAM,GAAG,MAAM,sBAAsB,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAE7E,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aACzC;iBAAM;gBACL,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;aAC/D;SACF;QAAC,OAAO,KAAU,EAAE;YACnB,WAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;SAC1E;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}