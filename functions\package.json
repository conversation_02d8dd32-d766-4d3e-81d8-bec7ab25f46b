{"name": "functions", "scripts": {"lint": "eslint .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.2", "firebase-admin": "^13.5.0", "firebase-functions": "^6.0.1", "form-data": "^4.0.4", "stripe": "^18.5.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/node-apple-receipt-verify": "^1.7.5", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}