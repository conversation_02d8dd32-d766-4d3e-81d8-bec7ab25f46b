import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.esimnumero.global',
  appName: 'KrakenSim',
  webDir: 'dist',
  server: {
    // For production-like behavior, don't use localhost
    // androidScheme: 'https'
  },
  plugins: {
    PushNotifications: {
      presentationOptions: ["badge", "sound", "alert"]
    },
    FirebaseAuthentication: {
      skipNativeAuth: false,
      providers: ["google.com"],
      webClientId: "377397638527-f499f52d25be408b89164a.apps.googleusercontent.com",
    },
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: "#000000",
      androidScaleType: "CENTER_CROP",
    },
  },
  android: {
    backgroundColor: "#000000",
  }
};

export default config;