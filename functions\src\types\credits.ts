// Credits System Type Definitions for Cloud Functions

export interface CreditPackage {
  id: string;                    // 'minnow', 'tuna', 'dolphin', 'octopus', 'shark', 'whale'
  name: string;                  // 'Minnow', 'Tuna', etc.
  animal: string;                // '🐟 Minnow', '🐟 Tuna', etc.
  price: number;                 // USD price: 5, 10, 20, 35, 50, 75
  credits: number;               // Credits awarded: 5, 10, 20, 35, 50, 75
  vibe: string;                  // 'Tiny but mighty', 'Reliable and steady', etc.
  useCase: string;               // 'Light travelers, test users', etc.
  // Stripe configuration (new)
  stripePriceId: string;         // Stripe price ID
  stripeProductId: string;       // Stripe product ID
  // LemonSqueezy configuration (deprecated, keeping for migration)
  lemonSqueezyVariantId?: string; // LemonSqueezy product variant ID (optional)
  lemonSqueezyProductId?: string; // LemonSqueezy product ID (optional)
  active: boolean;               // Whether package is available for purchase
  createdAt: Date;
  updatedAt: Date;
}

export interface UserCredits {
  userId: string;
  totalCredits: number;          // Current available credits
  lifetimeCredits: number;       // Total credits ever purchased
  lifetimeSpent: number;         // Total credits ever spent
  pendingCredits: number;        // Credits from pending payments
  lastUpdated: Date;
  createdAt: Date;
}

export interface CreditTransaction {
  id: string;                    // Auto-generated document ID
  userId: string;
  type: 'purchase' | 'redemption' | 'refund' | 'bonus' | 'topup';
  amount: number;                // Credits added (positive) or spent (negative)

  // Purchase details
  packageId?: string;            // Credit package purchased
  stripePaymentIntentId?: string;
  stripePriceId?: string;

  // Redemption details
  esimOrderId?: string;          // Related eSIM order
  esimPlanId?: string;
  esimCountryCode?: string;

  // Top-up details
  topupOrderId?: string;

  // Legacy fields (deprecated)
  orderId?: string;              // Related order ID (LemonSqueezy or in-app)
  lemonSqueezyOrderId?: string;  // LemonSqueezy order ID for purchases
  transactionId?: string;        // Platform transaction ID (iOS/Android)

  platform: 'web' | 'ios' | 'android';
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ESimCreditCost {
  id: string;                    // Auto-generated or planId_countryCode
  planId: number;                // Original eSIM plan ID
  countryCode: string;           // Country code
  countryName: string;           // Human-readable country name
  dataInGb: number;              // Data amount in GB
  creditCost: number;            // How many credits this eSIM costs
  originalPriceUsd: number;      // Original USD price for reference
  active: boolean;               // Whether this eSIM is available for redemption
  createdAt: Date;
  updatedAt: Date;
}

// Enhanced eSIM Order Management
export interface ESimOrder {
  orderId: string;
  userId: string;

  // eSIM Details
  planId: number;                // Provider plan ID
  countryCode: string;
  countryName: string;
  dataInGb: number;
  durationDays: number;

  // Pricing
  creditCost: number;            // Credits spent
  originalPriceUsd: number;      // Reference price

  // Provider Integration
  providerTransactionId?: string; // Provider's transaction ID
  activationCode?: string;
  qrCode?: string;

  // Status & Lifecycle
  status: 'pending' | 'provisioned' | 'activated' | 'expired' | 'cancelled';
  purchasedAt: Date;
  activatedAt?: Date;
  expiresAt?: Date;

  // Usage Tracking
  dataUsed: number;              // GB used
  lastUsageUpdate: Date;

  metadata: Record<string, any>;
}

export interface ESimTopup {
  topupId: string;
  userId: string;
  esimOrderId: string;           // Parent eSIM order

  // Top-up Details
  dataInGb: number;              // Additional data
  creditCost: number;            // Credits spent

  // Provider Integration
  providerTopupId?: string;

  status: 'pending' | 'completed' | 'failed';
  createdAt: Date;
  completedAt?: Date;
}

export interface CreditOrder {
  orderId: string;               // Unique order ID
  userId: string;
  type: 'credit_purchase' | 'esim_redemption';
  
  // For credit purchases
  packageId?: string;            // 'minnow', 'tuna', etc.
  creditsAwarded?: number;       // Credits given to user
  
  // For eSIM redemptions
  esimPlanId?: string;
  esimCountryCode?: string;
  creditsSpent?: number;         // Credits deducted from user
  
  // Payment/Platform info
  platform: 'web' | 'ios' | 'android';
  stripePaymentIntentId?: string; // For Stripe payments
  stripeSessionId?: string;      // For Stripe checkout sessions
  transactionId?: string;        // For in-app purchases
  
  // Order details
  amountUsd?: number;            // USD amount for purchases
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  
  // Additional data
  metadata?: {
    customerEmail?: string;
    customerName?: string;
    webhookData?: any;
    [key: string]: any;
  };
}

// Service interfaces for credit operations
export interface CreditPurchaseRequest {
  userId: string;
  packageId: string;
  platform: 'web' | 'ios' | 'android';
  customerEmail: string;
  customerName: string;
  stripePaymentIntentId?: string;
  stripeSessionId?: string;
  transactionId?: string; // For mobile in-app purchases
}

export interface CreditPurchaseResult {
  success: boolean;
  orderId?: string;
  creditsAwarded?: number;
  newBalance?: number;
  error?: string;
}

export interface ESimRedemptionRequest {
  userId: string;
  planId: number;
  countryCode: string;
}

export interface ESimRedemptionResult {
  success: boolean;
  orderId?: string;
  creditsSpent?: number;
  remainingCredits?: number;
  esimData?: any;
  error?: string;
}

// LemonSqueezy webhook data for credits
export interface LemonSqueezyCreditsWebhookData {
  meta: {
    event_name: string;
    custom_data?: {
      packageId?: string;
      userId?: string;
    };
  };
  data: {
    id: string;
    type: string;
    attributes: {
      store_id: number;
      customer_id: number;
      order_number: number;
      user_name: string;
      user_email: string;
      currency: string;
      currency_rate: string;
      subtotal: number;
      discount_total: number;
      tax: number;
      total: number;
      subtotal_usd: number;
      discount_total_usd: number;
      tax_usd: number;
      total_usd: number;
      tax_name: string;
      tax_rate: string;
      status: string;
      status_formatted: string;
      refunded: boolean;
      refunded_at: string | null;
      subtotal_formatted: string;
      discount_total_formatted: string;
      tax_formatted: string;
      total_formatted: string;
      first_order_item: {
        id: number;
        order_id: number;
        product_id: number;
        variant_id: number;
        product_name: string;
        variant_name: string;
        price: number;
        created_at: string;
        updated_at: string;
      };
      urls: {
        receipt: string;
      };
      created_at: string;
      updated_at: string;
    };
  };
}
