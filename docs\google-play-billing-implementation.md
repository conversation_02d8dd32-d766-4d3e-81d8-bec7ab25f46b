# Google Play Billing Implementation Guide

## Prerequisites

Before starting implementation, ensure you have:
- Google Play Console access with app published (at least in internal testing)
- Google Cloud Project with billing enabled
- Service account with Google Play Developer API access
- Android app configured with proper signing key

## Step-by-Step Implementation

### Step 1: Google Play Console Configuration

#### 1.1 Create In-App Products

Navigate to Google Play Console → Your App → Monetization → Products → In-app products

Create these products:

```
Product ID: com.esimnumero.global.credits.minnow
Name: Minnow Credits Package
Description: 5 credits for light usage
Price: $4.99
Status: Active

Product ID: com.esimnumero.global.credits.tuna  
Name: Tuna Credits Package
Description: 10 credits for regular usage
Price: $9.99
Status: Active

Product ID: com.esimnumero.global.credits.dolphin
Name: Dolphin Credits Package  
Description: 20 credits for business travelers
Price: $19.99
Status: Active

Product ID: com.esimnumero.global.credits.octopus
Name: Octopus Credits Package
Description: 35 credits for heavy usage
Price: $34.99
Status: Active

Product ID: com.esimnumero.global.credits.shark
Name: Shark Credits Package
Description: 50 credits for power users
Price: $49.99
Status: Active

Product ID: com.esimnumero.global.credits.whale
Name: Whale Credits Package
Description: 75 credits for maximum value
Price: $74.99
Status: Active
```

#### 1.2 Set Up Service Account

1. Go to Google Cloud Console → IAM & Admin → Service Accounts
2. Create new service account: `esim-numero-billing`
3. Download JSON key file
4. In Google Play Console → API access → Link project
5. Grant service account "View financial data" permission

### Step 2: Android Project Configuration

#### 2.1 Update build.gradle

```gradle
// android/app/build.gradle
dependencies {
    // Existing dependencies...
    implementation 'com.android.billingclient:billing:6.1.0'
}
```

#### 2.2 Add Billing Permission

```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="com.android.vending.BILLING" />
```

#### 2.3 Install Capacitor Plugin

```bash
npm install @capacitor-community/in-app-purchases
npx cap sync android
```

### Step 3: Frontend Implementation

#### 3.1 Create Google Play Billing Service

```typescript
// src/services/googlePlayBillingService.ts
import { InAppPurchases, PurchaseResult, ProductDetails } from '@capacitor-community/in-app-purchases';
import { CreditPackage } from '../types/credits';

export class GooglePlayBillingService {
  private static instance: GooglePlayBillingService;
  private isInitialized = false;
  private products: ProductDetails[] = [];

  static getInstance(): GooglePlayBillingService {
    if (!GooglePlayBillingService.instance) {
      GooglePlayBillingService.instance = new GooglePlayBillingService();
    }
    return GooglePlayBillingService.instance;
  }

  async initialize(): Promise<boolean> {
    try {
      await InAppPurchases.initialize();
      this.isInitialized = true;
      await this.loadProducts();
      return true;
    } catch (error) {
      console.error('Failed to initialize Google Play Billing:', error);
      return false;
    }
  }

  private async loadProducts(): Promise<void> {
    const productIds = [
      'com.esimnumero.global.credits.minnow',
      'com.esimnumero.global.credits.tuna',
      'com.esimnumero.global.credits.dolphin',
      'com.esimnumero.global.credits.octopus',
      'com.esimnumero.global.credits.shark',
      'com.esimnumero.global.credits.whale'
    ];

    try {
      const result = await InAppPurchases.getProducts({ productIds });
      this.products = result.products;
    } catch (error) {
      console.error('Failed to load products:', error);
    }
  }

  getProductDetails(productId: string): ProductDetails | undefined {
    return this.products.find(p => p.productId === productId);
  }

  async purchaseCredits(creditPackage: CreditPackage): Promise<PurchaseResult> {
    if (!this.isInitialized) {
      throw new Error('Google Play Billing not initialized');
    }

    if (!creditPackage.googlePlayProductId) {
      throw new Error('No Google Play product ID configured');
    }

    try {
      const result = await InAppPurchases.purchase({
        productId: creditPackage.googlePlayProductId
      });

      return result;
    } catch (error) {
      console.error('Purchase failed:', error);
      throw error;
    }
  }

  async restorePurchases(): Promise<void> {
    try {
      await InAppPurchases.restorePurchases();
    } catch (error) {
      console.error('Failed to restore purchases:', error);
    }
  }
}

export const googlePlayBillingService = GooglePlayBillingService.getInstance();
```

#### 3.2 Update Credit Package Types

```typescript
// src/types/credits.ts - Add to existing interface
export interface CreditPackage {
  // Existing fields...
  googlePlayProductId?: string;
  googlePlayPrice?: string;
  googlePlayPriceAmountMicros?: number;
  googlePlayCurrencyCode?: string;
}
```

#### 3.3 Update Credits Page

```typescript
// src/pages/Credits.tsx - Key changes
import { googlePlayBillingService } from '../services/googlePlayBillingService';
import { Capacitor } from '@capacitor/core';

// Add to component state
const [googlePlayAvailable, setGooglePlayAvailable] = useState(false);

// Initialize Google Play Billing
useEffect(() => {
  const initializeGooglePlay = async () => {
    if (Capacitor.getPlatform() === 'android') {
      const initialized = await googlePlayBillingService.initialize();
      setGooglePlayAvailable(initialized);
    }
  };

  initializeGooglePlay();
}, []);

// Update purchase handler
const handlePurchase = async (creditPackage: CreditPackage) => {
  if (Capacitor.getPlatform() === 'android' && googlePlayAvailable) {
    return handleGooglePlayPurchase(creditPackage);
  } else {
    return handleStripePurchase(creditPackage); // Keep for web
  }
};

const handleGooglePlayPurchase = async (creditPackage: CreditPackage) => {
  setPurchasing(creditPackage.id);
  
  try {
    const result = await googlePlayBillingService.purchaseCredits(creditPackage);
    
    if (result.success) {
      // Verify purchase with backend
      await verifyGooglePlayPurchase(result.purchaseToken, creditPackage);
      setAlertMessage(t('credits.purchaseSuccess'));
      setShowAlert(true);
    }
  } catch (error) {
    console.error('Google Play purchase failed:', error);
    setAlertMessage(t('credits.purchaseError'));
    setShowAlert(true);
  } finally {
    setPurchasing(null);
  }
};
```

### Step 4: Backend Implementation

#### 4.1 Install Dependencies

```bash
# In functions directory
npm install googleapis
```

#### 4.2 Create Google Play Verification Service

```typescript
// functions/src/googlePlayBillingService.ts
import { google } from 'googleapis';
import * as admin from 'firebase-admin';

export class GooglePlayBillingService {
  private androidPublisher: any;
  private db = admin.firestore();

  constructor() {
    // Initialize Google Play API client
    const auth = new google.auth.GoogleAuth({
      keyFile: './service-account-key.json', // Path to service account key
      scopes: ['https://www.googleapis.com/auth/androidpublisher']
    });

    this.androidPublisher = google.androidpublisher({
      version: 'v3',
      auth
    });
  }

  async verifyPurchase(
    packageName: string,
    productId: string,
    purchaseToken: string
  ): Promise<any> {
    try {
      const result = await this.androidPublisher.purchases.products.get({
        packageName,
        productId,
        token: purchaseToken
      });

      return result.data;
    } catch (error) {
      console.error('Purchase verification failed:', error);
      throw error;
    }
  }

  async acknowledgePurchase(
    packageName: string,
    productId: string,
    purchaseToken: string
  ): Promise<void> {
    try {
      await this.androidPublisher.purchases.products.acknowledge({
        packageName,
        productId,
        token: purchaseToken
      });
    } catch (error) {
      console.error('Purchase acknowledgment failed:', error);
      throw error;
    }
  }
}
```

#### 4.3 Create Purchase Processing Function

```typescript
// functions/src/processGooglePlayPurchase.ts
import * as functions from 'firebase-functions/v2';
import { GooglePlayBillingService } from './googlePlayBillingService';
import { getCreditsService } from './creditsService';

const googlePlayService = new GooglePlayBillingService();

export const processGooglePlayPurchase = functions.https.onCall(async (request) => {
  const { userId, productId, purchaseToken, packageId } = request.data;

  try {
    // Verify purchase with Google Play
    const purchaseData = await googlePlayService.verifyPurchase(
      'com.esimnumero.global',
      productId,
      purchaseToken
    );

    // Check if purchase is valid
    if (purchaseData.purchaseState !== 1) { // 1 = Purchased
      throw new Error('Invalid purchase state');
    }

    // Check if already processed
    if (purchaseData.acknowledgementState === 1) { // Already acknowledged
      throw new Error('Purchase already processed');
    }

    // Get credit package details
    const packageDoc = await admin.firestore()
      .collection('credit_packages')
      .doc(packageId)
      .get();

    if (!packageDoc.exists) {
      throw new Error('Credit package not found');
    }

    const creditPackage = packageDoc.data();

    // Add credits to user account
    await getCreditsService().addCreditsToUser({
      userId,
      packageId,
      googlePlayPurchaseToken: purchaseToken,
      googlePlayProductId: productId,
      platform: 'android'
    });

    // Acknowledge purchase
    await googlePlayService.acknowledgePurchase(
      'com.esimnumero.global',
      productId,
      purchaseToken
    );

    return { success: true };
  } catch (error) {
    console.error('Google Play purchase processing failed:', error);
    throw new functions.https.HttpsError('internal', error.message);
  }
});
```

### Step 5: Testing Strategy

#### 5.1 Test Account Setup
1. Add test accounts in Google Play Console
2. Create test purchases with $0.01 pricing
3. Test complete purchase flow

#### 5.2 Testing Checklist
- [ ] Products load correctly
- [ ] Purchase flow initiates
- [ ] Payment completes successfully  
- [ ] Credits added to account
- [ ] Purchase acknowledged
- [ ] Error handling works
- [ ] Network failure recovery

### Step 6: Deployment

#### 6.1 Environment Variables
Add to Firebase Functions:
```bash
firebase functions:config:set googleplay.package_name="com.esimnumero.global"
```

#### 6.2 Deploy Functions
```bash
firebase deploy --only functions
```

#### 6.3 Update Android App
```bash
npx cap build android
```

## Security Best Practices

1. **Always verify server-side**: Never trust client-side purchase data
2. **Store service account key securely**: Use Firebase Functions config or Secret Manager
3. **Implement idempotency**: Handle duplicate purchase attempts
4. **Log all transactions**: Maintain audit trail for debugging
5. **Handle edge cases**: Network failures, cancelled purchases, etc.

## Monitoring and Analytics

- Track purchase success/failure rates
- Monitor Google Play API quota usage
- Set up alerts for failed verifications
- Track credit package popularity
- Monitor user purchase patterns

This implementation provides a robust, secure Google Play Billing integration that maintains your existing credit system while providing a native Android payment experience.
