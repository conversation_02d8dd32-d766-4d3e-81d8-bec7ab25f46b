﻿
> Configure project :app
WARNING: Using flatDir should be avoided because it doesn't support any meta-data formats.

> Configure project :capacitor-cordova-android-plugins
WARNING: Using flatDir should be avoided because it doesn't support any meta-data formats.

> Task :app:preBuild UP-TO-DATE
> Task :app:preDebugBuild UP-TO-DATE
> Task :app:mergeDebugNativeDebugMetadata NO-SOURCE
> Task :app:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :capacitor-android:preBuild UP-TO-DATE
> Task :capacitor-android:preDebugBuild UP-TO-DATE
> Task :capacitor-android:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-app:preBuild UP-TO-DATE
> Task :capacitor-app:preDebugBuild UP-TO-DATE
> Task :capacitor-app:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-community-stripe:preBuild UP-TO-DATE
> Task :capacitor-community-stripe:preDebugBuild UP-TO-DATE
> Task :capacitor-community-stripe:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-cordova-android-plugins:preBuild UP-TO-DATE
> Task :capacitor-cordova-android-plugins:preDebugBuild UP-TO-DATE
> Task :capacitor-cordova-android-plugins:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-firebase-app:preBuild UP-TO-DATE
> Task :capacitor-firebase-app:preDebugBuild UP-TO-DATE
> Task :capacitor-firebase-app:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-firebase-authentication:preBuild UP-TO-DATE
> Task :capacitor-firebase-authentication:preDebugBuild UP-TO-DATE
> Task :capacitor-firebase-authentication:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-haptics:preBuild UP-TO-DATE
> Task :capacitor-haptics:preDebugBuild UP-TO-DATE
> Task :capacitor-haptics:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-keyboard:preBuild UP-TO-DATE
> Task :capacitor-keyboard:preDebugBuild UP-TO-DATE
> Task :capacitor-keyboard:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-push-notifications:preBuild UP-TO-DATE
> Task :capacitor-push-notifications:preDebugBuild UP-TO-DATE
> Task :capacitor-push-notifications:writeDebugAarMetadata UP-TO-DATE
> Task :capacitor-status-bar:preBuild UP-TO-DATE
> Task :capacitor-status-bar:preDebugBuild UP-TO-DATE
> Task :capacitor-status-bar:writeDebugAarMetadata UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:preBuild UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:preDebugBuild UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:writeDebugAarMetadata UP-TO-DATE
> Task :app:checkDebugAarMetadata UP-TO-DATE
> Task :app:generateDebugResValues UP-TO-DATE
> Task :app:processDebugGoogleServices UP-TO-DATE
> Task :capacitor-android:generateDebugResValues UP-TO-DATE
> Task :capacitor-android:generateDebugResources UP-TO-DATE
> Task :capacitor-android:packageDebugResources UP-TO-DATE
> Task :capacitor-app:generateDebugResValues UP-TO-DATE
> Task :capacitor-app:generateDebugResources UP-TO-DATE
> Task :capacitor-app:packageDebugResources UP-TO-DATE
> Task :capacitor-community-stripe:generateDebugResValues UP-TO-DATE
> Task :capacitor-community-stripe:generateDebugResources UP-TO-DATE
> Task :capacitor-community-stripe:packageDebugResources UP-TO-DATE
> Task :capacitor-cordova-android-plugins:generateDebugResValues UP-TO-DATE
> Task :capacitor-cordova-android-plugins:generateDebugResources UP-TO-DATE
> Task :capacitor-cordova-android-plugins:packageDebugResources UP-TO-DATE
> Task :capacitor-firebase-app:generateDebugResValues UP-TO-DATE
> Task :capacitor-firebase-app:generateDebugResources UP-TO-DATE
> Task :capacitor-firebase-app:packageDebugResources UP-TO-DATE
> Task :capacitor-firebase-authentication:generateDebugResValues UP-TO-DATE
> Task :capacitor-firebase-authentication:generateDebugResources UP-TO-DATE
> Task :capacitor-firebase-authentication:packageDebugResources UP-TO-DATE
> Task :capacitor-haptics:generateDebugResValues UP-TO-DATE
> Task :capacitor-haptics:generateDebugResources UP-TO-DATE
> Task :capacitor-haptics:packageDebugResources UP-TO-DATE
> Task :capacitor-keyboard:generateDebugResValues UP-TO-DATE
> Task :capacitor-keyboard:generateDebugResources UP-TO-DATE
> Task :capacitor-keyboard:packageDebugResources UP-TO-DATE
> Task :capacitor-push-notifications:generateDebugResValues UP-TO-DATE
> Task :capacitor-push-notifications:generateDebugResources UP-TO-DATE
> Task :capacitor-push-notifications:packageDebugResources UP-TO-DATE
> Task :capacitor-status-bar:generateDebugResValues UP-TO-DATE
> Task :capacitor-status-bar:generateDebugResources UP-TO-DATE
> Task :capacitor-status-bar:packageDebugResources UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:generateDebugResValues UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:generateDebugResources UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:packageDebugResources UP-TO-DATE
> Task :app:mapDebugSourceSetPaths
> Task :app:generateDebugResources
> Task :app:packageDebugResources
> Task :app:createDebugCompatibleScreenManifests UP-TO-DATE
> Task :app:extractDeepLinksDebug UP-TO-DATE
> Task :capacitor-android:extractDeepLinksDebug UP-TO-DATE
> Task :capacitor-android:processDebugManifest UP-TO-DATE
> Task :capacitor-app:extractDeepLinksDebug UP-TO-DATE
> Task :capacitor-app:processDebugManifest UP-TO-DATE
> Task :capacitor-community-stripe:extractDeepLinksDebug UP-TO-DATE
> Task :capacitor-community-stripe:processDebugManifest UP-TO-DATE
> Task :capacitor-cordova-android-plugins:extractDeepLinksDebug
> Task :app:parseDebugLocalResources
> Task :capacitor-firebase-app:extractDeepLinksDebug UP-TO-DATE
> Task :capacitor-firebase-app:processDebugManifest UP-TO-DATE
> Task :capacitor-firebase-authentication:extractDeepLinksDebug UP-TO-DATE
> Task :capacitor-firebase-authentication:processDebugManifest UP-TO-DATE
> Task :capacitor-haptics:extractDeepLinksDebug UP-TO-DATE
> Task :capacitor-cordova-android-plugins:processDebugManifest
> Task :capacitor-haptics:processDebugManifest UP-TO-DATE
> Task :capacitor-keyboard:extractDeepLinksDebug UP-TO-DATE
> Task :capacitor-keyboard:processDebugManifest UP-TO-DATE
> Task :capacitor-push-notifications:extractDeepLinksDebug UP-TO-DATE
> Task :capacitor-push-notifications:processDebugManifest UP-TO-DATE
> Task :capacitor-status-bar:extractDeepLinksDebug UP-TO-DATE
> Task :capacitor-status-bar:processDebugManifest UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:extractDeepLinksDebug UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:processDebugManifest UP-TO-DATE
> Task :app:mergeDebugResources

> Task :app:processDebugMainManifest
package="com.esimnumero.global" found in source AndroidManifest.xml: C:\Users\<USER>\source\repos\esim-numero\android\app\src\main\AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.esimnumero.global" from the source AndroidManifest.xml: C:\Users\<USER>\source\repos\esim-numero\android\app\src\main\AndroidManifest.xml.

> Task :app:processDebugManifest
> Task :capacitor-android:compileDebugLibraryResources UP-TO-DATE
> Task :capacitor-android:parseDebugLocalResources UP-TO-DATE
> Task :capacitor-android:generateDebugRFile UP-TO-DATE
> Task :capacitor-app:compileDebugLibraryResources UP-TO-DATE
> Task :capacitor-app:parseDebugLocalResources UP-TO-DATE
> Task :capacitor-app:generateDebugRFile UP-TO-DATE
> Task :capacitor-community-stripe:compileDebugLibraryResources UP-TO-DATE
> Task :capacitor-community-stripe:parseDebugLocalResources UP-TO-DATE
> Task :capacitor-community-stripe:generateDebugRFile UP-TO-DATE
> Task :capacitor-cordova-android-plugins:compileDebugLibraryResources
> Task :capacitor-firebase-app:compileDebugLibraryResources UP-TO-DATE
> Task :capacitor-cordova-android-plugins:parseDebugLocalResources
> Task :capacitor-firebase-app:parseDebugLocalResources UP-TO-DATE
> Task :capacitor-firebase-app:generateDebugRFile UP-TO-DATE
> Task :capacitor-firebase-authentication:compileDebugLibraryResources UP-TO-DATE
> Task :capacitor-firebase-authentication:parseDebugLocalResources UP-TO-DATE
> Task :capacitor-cordova-android-plugins:generateDebugRFile
> Task :capacitor-firebase-authentication:generateDebugRFile UP-TO-DATE
> Task :capacitor-haptics:compileDebugLibraryResources UP-TO-DATE
> Task :capacitor-haptics:parseDebugLocalResources UP-TO-DATE
> Task :capacitor-haptics:generateDebugRFile UP-TO-DATE
> Task :capacitor-keyboard:compileDebugLibraryResources UP-TO-DATE
> Task :capacitor-keyboard:parseDebugLocalResources UP-TO-DATE
> Task :capacitor-keyboard:generateDebugRFile UP-TO-DATE
> Task :capacitor-push-notifications:compileDebugLibraryResources UP-TO-DATE
> Task :capacitor-push-notifications:parseDebugLocalResources UP-TO-DATE
> Task :capacitor-push-notifications:generateDebugRFile UP-TO-DATE
> Task :capacitor-status-bar:compileDebugLibraryResources UP-TO-DATE
> Task :capacitor-status-bar:parseDebugLocalResources UP-TO-DATE
> Task :capacitor-status-bar:generateDebugRFile UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:compileDebugLibraryResources UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:parseDebugLocalResources UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:generateDebugRFile UP-TO-DATE
> Task :capacitor-android:javaPreCompileDebug UP-TO-DATE
> Task :capacitor-android:compileDebugJavaWithJavac UP-TO-DATE
> Task :capacitor-android:bundleLibCompileToJarDebug UP-TO-DATE
> Task :capacitor-app:javaPreCompileDebug UP-TO-DATE
> Task :capacitor-app:compileDebugJavaWithJavac UP-TO-DATE
> Task :capacitor-app:bundleLibCompileToJarDebug UP-TO-DATE
> Task :capacitor-community-stripe:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :capacitor-community-stripe:compileDebugKotlin UP-TO-DATE
> Task :capacitor-community-stripe:javaPreCompileDebug UP-TO-DATE
> Task :capacitor-community-stripe:compileDebugJavaWithJavac NO-SOURCE
> Task :capacitor-community-stripe:bundleLibCompileToJarDebug UP-TO-DATE
> Task :capacitor-firebase-app:javaPreCompileDebug UP-TO-DATE
> Task :capacitor-cordova-android-plugins:javaPreCompileDebug
> Task :capacitor-firebase-app:compileDebugJavaWithJavac UP-TO-DATE

> Task :capacitor-cordova-android-plugins:compileDebugJavaWithJavac
cmd : Note: C:\Users\<USER>\source\repos\es
im-numero\android\cap
acitor-cordova-androi
d-plugins\src\main\ja
va\cc\fovea\PurchaseP
lugin.java uses or 
overrides a 
deprecated API.
At line:1 char:1
+ cmd /c gradlew 
assembleDebug > 
build_log_2.txt 2>&1
+ ~~~~~~~~~~~~~~~~~~~
~~~~~~~~~~~~~~~~~~~~~
~~~~~~~~~~~
    + CategoryInfo   
           : NotSpe  
  cified: (Note: C   
 :\Users\...depre    
cated API.:Strin    
g) [], RemoteExc    
eption
    + FullyQualified 
   ErrorId : Native  
  CommandError
 
Note: Recompile with 
-Xlint:deprecation 
for details.

> Task :app:processDebugManifestForPackage
> Task :capacitor-cordova-android-plugins:bundleLibCompileToJarDebug
> Task :capacitor-firebase-app:bundleLibCompileToJarDebug UP-TO-DATE
> Task :capacitor-firebase-authentication:javaPreCompileDebug UP-TO-DATE
> Task :capacitor-firebase-authentication:compileDebugJavaWithJavac UP-TO-DATE
> Task :capacitor-firebase-authentication:bundleLibCompileToJarDebug UP-TO-DATE
> Task :capacitor-haptics:javaPreCompileDebug UP-TO-DATE
> Task :capacitor-haptics:compileDebugJavaWithJavac UP-TO-DATE
> Task :capacitor-haptics:bundleLibCompileToJarDebug UP-TO-DATE
> Task :capacitor-keyboard:javaPreCompileDebug UP-TO-DATE
> Task :capacitor-keyboard:compileDebugJavaWithJavac UP-TO-DATE
> Task :capacitor-keyboard:bundleLibCompileToJarDebug UP-TO-DATE
> Task :capacitor-push-notifications:javaPreCompileDebug UP-TO-DATE
> Task :capacitor-push-notifications:compileDebugJavaWithJavac UP-TO-DATE
> Task :capacitor-push-notifications:bundleLibCompileToJarDebug UP-TO-DATE
> Task :capacitor-status-bar:javaPreCompileDebug UP-TO-DATE
> Task :capacitor-status-bar:compileDebugJavaWithJavac UP-TO-DATE
> Task :capacitor-status-bar:bundleLibCompileToJarDebug UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:javaPreCompileDebug UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:compileDebugJavaWithJavac UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:bundleLibCompileToJarDebug UP-TO-DATE
> Task :app:javaPreCompileDebug UP-TO-DATE
> Task :app:mergeDebugShaders UP-TO-DATE
> Task :app:compileDebugShaders NO-SOURCE
> Task :app:generateDebugAssets UP-TO-DATE
> Task :capacitor-android:mergeDebugShaders UP-TO-DATE
> Task :capacitor-android:compileDebugShaders NO-SOURCE
> Task :capacitor-android:generateDebugAssets UP-TO-DATE
> Task :capacitor-android:packageDebugAssets UP-TO-DATE
> Task :capacitor-app:mergeDebugShaders UP-TO-DATE
> Task :capacitor-app:compileDebugShaders NO-SOURCE
> Task :capacitor-app:generateDebugAssets UP-TO-DATE
> Task :capacitor-app:packageDebugAssets UP-TO-DATE
> Task :capacitor-community-stripe:mergeDebugShaders UP-TO-DATE
> Task :capacitor-community-stripe:compileDebugShaders NO-SOURCE
> Task :capacitor-community-stripe:generateDebugAssets UP-TO-DATE
> Task :capacitor-community-stripe:packageDebugAssets UP-TO-DATE
> Task :capacitor-cordova-android-plugins:mergeDebugShaders
> Task :capacitor-cordova-android-plugins:compileDebugShaders NO-SOURCE
> Task :capacitor-cordova-android-plugins:generateDebugAssets UP-TO-DATE
> Task :capacitor-cordova-android-plugins:packageDebugAssets
> Task :capacitor-firebase-app:mergeDebugShaders UP-TO-DATE
> Task :capacitor-firebase-app:compileDebugShaders NO-SOURCE
> Task :capacitor-firebase-app:generateDebugAssets UP-TO-DATE
> Task :capacitor-firebase-app:packageDebugAssets UP-TO-DATE
> Task :capacitor-firebase-authentication:mergeDebugShaders UP-TO-DATE
> Task :capacitor-firebase-authentication:compileDebugShaders NO-SOURCE
> Task :capacitor-firebase-authentication:generateDebugAssets UP-TO-DATE
> Task :capacitor-firebase-authentication:packageDebugAssets UP-TO-DATE
> Task :capacitor-haptics:mergeDebugShaders UP-TO-DATE
> Task :capacitor-haptics:compileDebugShaders NO-SOURCE
> Task :capacitor-haptics:generateDebugAssets UP-TO-DATE
> Task :capacitor-haptics:packageDebugAssets UP-TO-DATE
> Task :capacitor-keyboard:mergeDebugShaders UP-TO-DATE
> Task :capacitor-keyboard:compileDebugShaders NO-SOURCE
> Task :capacitor-keyboard:generateDebugAssets UP-TO-DATE
> Task :capacitor-keyboard:packageDebugAssets UP-TO-DATE
> Task :capacitor-push-notifications:mergeDebugShaders UP-TO-DATE
> Task :capacitor-push-notifications:compileDebugShaders NO-SOURCE
> Task :capacitor-push-notifications:generateDebugAssets UP-TO-DATE
> Task :capacitor-push-notifications:packageDebugAssets UP-TO-DATE
> Task :capacitor-status-bar:mergeDebugShaders UP-TO-DATE
> Task :capacitor-status-bar:compileDebugShaders NO-SOURCE
> Task :capacitor-status-bar:generateDebugAssets UP-TO-DATE
> Task :capacitor-status-bar:packageDebugAssets UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:mergeDebugShaders UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:compileDebugShaders NO-SOURCE
> Task :codetrix-studio-capacitor-google-auth:generateDebugAssets UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:packageDebugAssets UP-TO-DATE
> Task :app:mergeDebugAssets UP-TO-DATE
> Task :app:compressDebugAssets UP-TO-DATE
> Task :capacitor-community-stripe:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :capacitor-firebase-app:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :capacitor-firebase-authentication:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :capacitor-app:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :capacitor-haptics:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :capacitor-keyboard:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :capacitor-push-notifications:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :capacitor-status-bar:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :capacitor-android:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :app:desugarDebugFileDependencies UP-TO-DATE
> Task :capacitor-android:processDebugJavaRes NO-SOURCE
> Task :capacitor-app:processDebugJavaRes NO-SOURCE
> Task :capacitor-cordova-android-plugins:bundleLibRuntimeToJarDebug
> Task :capacitor-community-stripe:processDebugJavaRes UP-TO-DATE
> Task :capacitor-cordova-android-plugins:processDebugJavaRes NO-SOURCE
> Task :capacitor-firebase-app:processDebugJavaRes NO-SOURCE
> Task :capacitor-firebase-authentication:processDebugJavaRes NO-SOURCE
> Task :capacitor-haptics:processDebugJavaRes NO-SOURCE
> Task :capacitor-keyboard:processDebugJavaRes NO-SOURCE
> Task :capacitor-push-notifications:processDebugJavaRes NO-SOURCE
> Task :capacitor-status-bar:processDebugJavaRes NO-SOURCE
> Task :codetrix-studio-capacitor-google-auth:processDebugJavaRes NO-SOURCE
> Task :app:checkDebugDuplicateClasses UP-TO-DATE
> Task :app:mergeExtDexDebug UP-TO-DATE
> Task :capacitor-community-stripe:bundleLibRuntimeToDirDebug UP-TO-DATE
> Task :capacitor-firebase-app:bundleLibRuntimeToDirDebug UP-TO-DATE
> Task :capacitor-firebase-authentication:bundleLibRuntimeToDirDebug UP-TO-DATE
> Task :capacitor-app:bundleLibRuntimeToDirDebug UP-TO-DATE
> Task :capacitor-haptics:bundleLibRuntimeToDirDebug UP-TO-DATE
> Task :capacitor-keyboard:bundleLibRuntimeToDirDebug UP-TO-DATE
> Task :capacitor-push-notifications:bundleLibRuntimeToDirDebug UP-TO-DATE
> Task :capacitor-status-bar:bundleLibRuntimeToDirDebug UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:bundleLibRuntimeToDirDebug UP-TO-DATE
> Task :capacitor-android:bundleLibRuntimeToDirDebug UP-TO-DATE
> Task :app:mergeDebugJniLibFolders UP-TO-DATE
> Task :capacitor-android:mergeDebugJniLibFolders UP-TO-DATE
> Task :capacitor-android:mergeDebugNativeLibs NO-SOURCE
> Task :capacitor-android:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :capacitor-app:mergeDebugJniLibFolders UP-TO-DATE
> Task :capacitor-app:mergeDebugNativeLibs NO-SOURCE
> Task :capacitor-app:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :capacitor-community-stripe:mergeDebugJniLibFolders UP-TO-DATE
> Task :capacitor-community-stripe:mergeDebugNativeLibs NO-SOURCE
> Task :capacitor-community-stripe:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :capacitor-cordova-android-plugins:mergeDebugJniLibFolders
> Task :capacitor-cordova-android-plugins:mergeDebugNativeLibs NO-SOURCE
> Task :capacitor-cordova-android-plugins:bundleLibRuntimeToDirDebug
> Task :capacitor-cordova-android-plugins:copyDebugJniLibsProjectOnly
> Task :capacitor-firebase-app:mergeDebugJniLibFolders UP-TO-DATE
> Task :capacitor-firebase-app:mergeDebugNativeLibs NO-SOURCE
> Task :capacitor-firebase-app:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :capacitor-firebase-authentication:mergeDebugJniLibFolders UP-TO-DATE
> Task :capacitor-firebase-authentication:mergeDebugNativeLibs NO-SOURCE
> Task :capacitor-firebase-authentication:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :capacitor-haptics:mergeDebugJniLibFolders UP-TO-DATE
> Task :capacitor-haptics:mergeDebugNativeLibs NO-SOURCE
> Task :capacitor-haptics:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :capacitor-keyboard:mergeDebugJniLibFolders UP-TO-DATE
> Task :capacitor-keyboard:mergeDebugNativeLibs NO-SOURCE
> Task :capacitor-keyboard:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :capacitor-push-notifications:mergeDebugJniLibFolders UP-TO-DATE
> Task :capacitor-push-notifications:mergeDebugNativeLibs NO-SOURCE
> Task :capacitor-push-notifications:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :capacitor-status-bar:mergeDebugJniLibFolders UP-TO-DATE
> Task :capacitor-status-bar:mergeDebugNativeLibs NO-SOURCE
> Task :capacitor-status-bar:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:mergeDebugJniLibFolders UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:mergeDebugNativeLibs NO-SOURCE
> Task :codetrix-studio-capacitor-google-auth:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :app:mergeDebugNativeLibs UP-TO-DATE
> Task :app:stripDebugDebugSymbols UP-TO-DATE
> Task :app:validateSigningDebug UP-TO-DATE
> Task :app:writeDebugAppMetadata UP-TO-DATE
> Task :app:writeDebugSigningConfigVersions UP-TO-DATE
> Task :capacitor-android:stripDebugDebugSymbols NO-SOURCE
> Task :capacitor-android:copyDebugJniLibsProjectAndLocalJars UP-TO-DATE
> Task :capacitor-android:extractDebugAnnotations UP-TO-DATE
> Task :capacitor-android:extractDeepLinksForAarDebug UP-TO-DATE
> Task :capacitor-android:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :capacitor-android:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :capacitor-android:prepareDebugArtProfile UP-TO-DATE
> Task :capacitor-android:prepareLintJarForPublish UP-TO-DATE
> Task :capacitor-android:mergeDebugJavaResource UP-TO-DATE
> Task :capacitor-android:syncDebugLibJars UP-TO-DATE
> Task :capacitor-android:bundleDebugAar UP-TO-DATE
> Task :capacitor-android:assembleDebug UP-TO-DATE
> Task :capacitor-app:stripDebugDebugSymbols NO-SOURCE
> Task :capacitor-app:copyDebugJniLibsProjectAndLocalJars UP-TO-DATE
> Task :capacitor-app:extractDebugAnnotations UP-TO-DATE
> Task :capacitor-app:extractDeepLinksForAarDebug UP-TO-DATE
> Task :capacitor-app:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :capacitor-app:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :capacitor-app:prepareDebugArtProfile UP-TO-DATE
> Task :app:mergeLibDexDebug UP-TO-DATE
> Task :capacitor-app:prepareLintJarForPublish UP-TO-DATE
> Task :capacitor-app:mergeDebugJavaResource UP-TO-DATE
> Task :capacitor-app:syncDebugLibJars UP-TO-DATE
> Task :capacitor-app:bundleDebugAar UP-TO-DATE
> Task :capacitor-app:assembleDebug UP-TO-DATE
> Task :capacitor-community-stripe:stripDebugDebugSymbols NO-SOURCE
> Task :capacitor-community-stripe:copyDebugJniLibsProjectAndLocalJars UP-TO-DATE
> Task :capacitor-community-stripe:extractDebugAnnotations UP-TO-DATE
> Task :capacitor-community-stripe:extractDeepLinksForAarDebug UP-TO-DATE
> Task :capacitor-community-stripe:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :capacitor-community-stripe:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :capacitor-community-stripe:prepareDebugArtProfile UP-TO-DATE
> Task :capacitor-community-stripe:prepareLintJarForPublish UP-TO-DATE
> Task :capacitor-community-stripe:mergeDebugJavaResource UP-TO-DATE
> Task :capacitor-community-stripe:syncDebugLibJars UP-TO-DATE
> Task :capacitor-community-stripe:bundleDebugAar UP-TO-DATE
> Task :capacitor-community-stripe:assembleDebug UP-TO-DATE
> Task :capacitor-cordova-android-plugins:stripDebugDebugSymbols NO-SOURCE
> Task :capacitor-cordova-android-plugins:copyDebugJniLibsProjectAndLocalJars
> Task :capacitor-cordova-android-plugins:extractDebugAnnotations
> Task :capacitor-cordova-android-plugins:extractDeepLinksForAarDebug UP-TO-DATE
> Task :capacitor-cordova-android-plugins:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :capacitor-cordova-android-plugins:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :capacitor-cordova-android-plugins:prepareDebugArtProfile UP-TO-DATE
> Task :capacitor-cordova-android-plugins:prepareLintJarForPublish UP-TO-DATE
> Task :capacitor-firebase-app:stripDebugDebugSymbols NO-SOURCE
> Task :capacitor-firebase-app:copyDebugJniLibsProjectAndLocalJars UP-TO-DATE
> Task :capacitor-cordova-android-plugins:mergeDebugJavaResource
> Task :capacitor-firebase-app:extractDebugAnnotations UP-TO-DATE
> Task :capacitor-cordova-android-plugins:syncDebugLibJars
> Task :capacitor-cordova-android-plugins:bundleDebugAar
> Task :capacitor-cordova-android-plugins:assembleDebug
> Task :capacitor-firebase-app:extractDeepLinksForAarDebug UP-TO-DATE
> Task :capacitor-firebase-app:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :capacitor-firebase-app:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :capacitor-firebase-app:prepareDebugArtProfile UP-TO-DATE
> Task :capacitor-firebase-app:prepareLintJarForPublish UP-TO-DATE
> Task :capacitor-firebase-app:mergeDebugJavaResource UP-TO-DATE
> Task :capacitor-firebase-app:syncDebugLibJars UP-TO-DATE
> Task :capacitor-firebase-app:bundleDebugAar UP-TO-DATE
> Task :capacitor-firebase-app:assembleDebug UP-TO-DATE
> Task :capacitor-firebase-authentication:stripDebugDebugSymbols NO-SOURCE
> Task :capacitor-firebase-authentication:copyDebugJniLibsProjectAndLocalJars UP-TO-DATE
> Task :capacitor-firebase-authentication:extractDebugAnnotations UP-TO-DATE
> Task :capacitor-firebase-authentication:extractDeepLinksForAarDebug UP-TO-DATE
> Task :capacitor-firebase-authentication:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :capacitor-firebase-authentication:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :capacitor-firebase-authentication:prepareDebugArtProfile UP-TO-DATE
> Task :capacitor-firebase-authentication:prepareLintJarForPublish UP-TO-DATE
> Task :capacitor-firebase-authentication:mergeDebugJavaResource UP-TO-DATE
> Task :capacitor-firebase-authentication:syncDebugLibJars UP-TO-DATE
> Task :capacitor-firebase-authentication:bundleDebugAar UP-TO-DATE
> Task :capacitor-firebase-authentication:assembleDebug UP-TO-DATE
> Task :capacitor-haptics:stripDebugDebugSymbols NO-SOURCE
> Task :capacitor-haptics:copyDebugJniLibsProjectAndLocalJars UP-TO-DATE
> Task :capacitor-haptics:extractDebugAnnotations UP-TO-DATE
> Task :capacitor-haptics:extractDeepLinksForAarDebug UP-TO-DATE
> Task :capacitor-haptics:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :capacitor-haptics:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :capacitor-haptics:prepareDebugArtProfile UP-TO-DATE
> Task :capacitor-haptics:prepareLintJarForPublish UP-TO-DATE
> Task :capacitor-haptics:mergeDebugJavaResource UP-TO-DATE
> Task :capacitor-haptics:syncDebugLibJars UP-TO-DATE
> Task :capacitor-haptics:bundleDebugAar UP-TO-DATE
> Task :capacitor-haptics:assembleDebug UP-TO-DATE
> Task :capacitor-keyboard:stripDebugDebugSymbols NO-SOURCE
> Task :capacitor-keyboard:copyDebugJniLibsProjectAndLocalJars UP-TO-DATE
> Task :capacitor-keyboard:extractDebugAnnotations UP-TO-DATE
> Task :capacitor-keyboard:extractDeepLinksForAarDebug UP-TO-DATE
> Task :capacitor-keyboard:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :capacitor-keyboard:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :capacitor-keyboard:prepareDebugArtProfile UP-TO-DATE
> Task :capacitor-keyboard:prepareLintJarForPublish UP-TO-DATE
> Task :capacitor-keyboard:mergeDebugJavaResource UP-TO-DATE
> Task :capacitor-keyboard:syncDebugLibJars UP-TO-DATE
> Task :capacitor-keyboard:bundleDebugAar UP-TO-DATE
> Task :capacitor-keyboard:assembleDebug UP-TO-DATE
> Task :capacitor-push-notifications:stripDebugDebugSymbols NO-SOURCE
> Task :capacitor-push-notifications:copyDebugJniLibsProjectAndLocalJars UP-TO-DATE
> Task :capacitor-push-notifications:extractDebugAnnotations UP-TO-DATE
> Task :capacitor-push-notifications:extractDeepLinksForAarDebug UP-TO-DATE
> Task :capacitor-push-notifications:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :capacitor-push-notifications:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :capacitor-push-notifications:prepareDebugArtProfile UP-TO-DATE
> Task :capacitor-push-notifications:prepareLintJarForPublish UP-TO-DATE
> Task :capacitor-push-notifications:mergeDebugJavaResource UP-TO-DATE
> Task :capacitor-push-notifications:syncDebugLibJars UP-TO-DATE
> Task :capacitor-push-notifications:bundleDebugAar UP-TO-DATE
> Task :capacitor-push-notifications:assembleDebug UP-TO-DATE
> Task :capacitor-status-bar:stripDebugDebugSymbols NO-SOURCE
> Task :capacitor-status-bar:copyDebugJniLibsProjectAndLocalJars UP-TO-DATE
> Task :capacitor-status-bar:extractDebugAnnotations UP-TO-DATE
> Task :capacitor-status-bar:extractDeepLinksForAarDebug UP-TO-DATE
> Task :capacitor-status-bar:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :capacitor-status-bar:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :capacitor-status-bar:prepareDebugArtProfile UP-TO-DATE
> Task :capacitor-status-bar:prepareLintJarForPublish UP-TO-DATE
> Task :capacitor-status-bar:mergeDebugJavaResource UP-TO-DATE
> Task :capacitor-status-bar:syncDebugLibJars UP-TO-DATE
> Task :capacitor-status-bar:bundleDebugAar UP-TO-DATE
> Task :capacitor-status-bar:assembleDebug UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:stripDebugDebugSymbols NO-SOURCE
> Task :codetrix-studio-capacitor-google-auth:copyDebugJniLibsProjectAndLocalJars UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:extractDebugAnnotations UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:extractDeepLinksForAarDebug UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:mergeDebugGeneratedProguardFiles UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:mergeDebugConsumerProguardFiles UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:prepareDebugArtProfile UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:prepareLintJarForPublish UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:mergeDebugJavaResource UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:syncDebugLibJars UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:bundleDebugAar UP-TO-DATE
> Task :codetrix-studio-capacitor-google-auth:assembleDebug UP-TO-DATE
> Task :app:processDebugResources
> Task :app:compileDebugKotlin NO-SOURCE
> Task :app:compileDebugJavaWithJavac UP-TO-DATE
> Task :app:dexBuilderDebug
> Task :app:mergeDebugGlobalSynthetics UP-TO-DATE
> Task :app:processDebugJavaRes NO-SOURCE
> Task :app:mergeDebugJavaResource UP-TO-DATE
> Task :app:mergeProjectDexDebug
> Task :app:packageDebug
> Task :app:createDebugApkListingFileRedirect UP-TO-DATE
> Task :app:assembleDebug
[Incubating] Problems report is available at: file:///C:/Users/<USER>/source/repos/esim-numero/android/build/reports/problems/problems-report.html

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.11.1/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD SUCCESSFUL in 15s
344 actionable tasks: 31 executed, 313 up-to-date
