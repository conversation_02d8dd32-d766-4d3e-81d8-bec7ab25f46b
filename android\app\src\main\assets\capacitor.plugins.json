[{"pkg": "@capacitor-community/stripe", "classpath": "com.getcapacitor.community.stripe.StripePlugin"}, {"pkg": "@capacitor-firebase/app", "classpath": "io.capawesome.capacitorjs.plugins.firebase.app.FirebaseAppPlugin"}, {"pkg": "@capacitor-firebase/authentication", "classpath": "io.capawesome.capacitorjs.plugins.firebase.authentication.FirebaseAuthenticationPlugin"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/haptics", "classpath": "com.capacitorjs.plugins.haptics.HapticsPlugin"}, {"pkg": "@capacitor/keyboard", "classpath": "com.capacitorjs.plugins.keyboard.KeyboardPlugin"}, {"pkg": "@capacitor/push-notifications", "classpath": "com.capacitorjs.plugins.pushnotifications.PushNotificationsPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}, {"pkg": "@codetrix-studio/capacitor-google-auth", "classpath": "com.codetrixstudio.capacitor.GoogleAuth.GoogleAuth"}]