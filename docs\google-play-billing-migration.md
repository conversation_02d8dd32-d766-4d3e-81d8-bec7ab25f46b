# Google Play Billing Migration Plan

## Overview

This document outlines the complete migration from Stripe to Google Play Billing for in-app credit purchases in the eSIM Numero app. The migration will maintain the existing credit system while replacing the payment processing mechanism.

## Current State Analysis

### Existing Stripe Implementation
- **Frontend**: Uses `@capacitor-community/stripe` package
- **Payment Flow**: Stripe Payment Sheet for mobile, Payment Element for web
- **Backend**: Firebase Functions with Stripe webhooks for payment processing
- **Credit System**: Well-established credit packages and user credit management
- **UI Components**: Credits page with Stripe-based purchase flow

### Credit Packages
Current credit packages that need Google Play products:
- **Minnow**: $5 → 5 credits
- **Tuna**: $10 → 10 credits  
- **Dolphin**: $20 → 20 credits
- **Octopus**: $35 → 35 credits
- **Shark**: $50 → 50 credits
- **Whale**: $75 → 75 credits

## Migration Strategy

### Key Principles
1. **Preserve Credit System**: Keep existing credit management and Firebase structure
2. **Gradual Migration**: Implement Google Play Billing alongside Stripe initially
3. **Platform-Specific**: Google Play Billing only for Android, keep web payments separate
4. **Backward Compatibility**: Maintain existing backend functions during transition

## Phase 1: Google Play Console Setup

### 1.1 Create In-App Products
Create managed products in Google Play Console for each credit package:

```
Product IDs:
- com.esimnumero.global.credits.minnow (5 credits - $4.99)
- com.esimnumero.global.credits.tuna (10 credits - $9.99)
- com.esimnumero.global.credits.dolphin (20 credits - $19.99)
- com.esimnumero.global.credits.octopus (35 credits - $34.99)
- com.esimnumero.global.credits.shark (50 credits - $49.99)
- com.esimnumero.global.credits.whale (75 credits - $74.99)
```

### 1.2 Configure Service Account
- Create service account for server-side purchase verification
- Download service account JSON key
- Grant necessary permissions for Google Play Developer API

### 1.3 Enable APIs
- Google Play Developer API
- Google Play Android Developer API

## Phase 2: Android Dependencies & Configuration

### 2.1 Add Dependencies
Update `android/app/build.gradle`:
```gradle
dependencies {
    // Existing dependencies...
    implementation 'com.android.billingclient:billing:6.1.0'
}
```

### 2.2 Add Permissions
Update `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="com.android.vending.BILLING" />
```

### 2.3 Capacitor Plugin
Install Google Play Billing Capacitor plugin:
```bash
npm install @capacitor-community/in-app-purchases
```

## Phase 3: Frontend Implementation

### 3.1 Google Play Billing Service
Create `src/services/googlePlayBillingService.ts`:
- Initialize billing client
- Query available products
- Handle purchase flow
- Manage purchase verification

### 3.2 Update Credit Types
Extend `src/types/credits.ts`:
```typescript
export interface CreditPackage {
  // Existing fields...
  googlePlayProductId?: string;  // Google Play product ID
  googlePlayPrice?: string;      // Formatted price from Google Play
}
```

### 3.3 Update Credits Page
Modify `src/pages/Credits.tsx`:
- Detect platform (Android vs Web)
- Use Google Play Billing for Android
- Keep Stripe for web (or disable web purchases)
- Update purchase button logic

## Phase 4: Backend Integration

### 4.1 Google Play Purchase Verification
Create `functions/src/googlePlayBillingService.ts`:
- Verify purchase tokens with Google Play API
- Validate purchase authenticity
- Handle purchase acknowledgment

### 4.2 New Firebase Function
Create `functions/src/processGooglePlayPurchase.ts`:
- Receive purchase data from client
- Verify with Google Play API
- Add credits to user account
- Record transaction in Firebase

### 4.3 Update Credit Package Configuration
Add Google Play product IDs to Firebase credit packages collection.

## Phase 5: Testing & Validation

### 5.1 Test Environment Setup
- Configure test accounts in Google Play Console
- Set up test credit packages
- Test purchase flow end-to-end

### 5.2 Validation Checklist
- [ ] Products load correctly from Google Play
- [ ] Purchase flow completes successfully
- [ ] Credits are added to user account
- [ ] Purchase verification works
- [ ] Error handling functions properly

## Phase 6: UI Migration & Cleanup

### 6.1 Remove Stripe UI Components
- Remove Stripe-specific purchase buttons
- Update payment method selection logic
- Clean up Stripe imports and dependencies

### 6.2 Update User Experience
- Show appropriate payment method based on platform
- Update messaging for Android users
- Maintain web experience (or redirect to credits system)

## Technical Implementation Details

### Google Play Billing Flow
1. **Initialize**: Connect to Google Play Billing service
2. **Query Products**: Fetch available in-app products and prices
3. **Launch Purchase**: Start Google Play purchase flow
4. **Handle Result**: Process purchase result and token
5. **Verify Backend**: Send purchase token to Firebase function
6. **Add Credits**: Update user credit balance
7. **Acknowledge**: Acknowledge purchase with Google Play

### Security Considerations
- Always verify purchases server-side
- Use Google Play Developer API for validation
- Implement proper error handling and retry logic
- Store purchase tokens securely
- Handle edge cases (network failures, duplicate purchases)

### Error Handling
- Network connectivity issues
- Google Play service unavailable
- Invalid purchase tokens
- Duplicate purchase attempts
- User cancellation

## Migration Timeline

**Week 1**: Google Play Console setup and Android configuration
**Week 2**: Frontend Google Play Billing service implementation  
**Week 3**: Backend verification and Firebase functions
**Week 4**: Testing, validation, and bug fixes
**Week 5**: UI cleanup and final migration

## Rollback Plan

If issues arise:
1. Keep Stripe implementation as fallback
2. Feature flag to switch between payment methods
3. Monitor error rates and user feedback
4. Quick rollback capability via configuration

## Benefits of Migration

1. **Native Experience**: Better integration with Android ecosystem
2. **Reduced Fees**: Lower transaction fees compared to Stripe
3. **Simplified Flow**: No external payment forms or redirects
4. **Better Security**: Google Play handles sensitive payment data
5. **Compliance**: Meets Google Play Store requirements for in-app purchases

## Next Steps

1. Review and approve this migration plan
2. Set up Google Play Console and create in-app products
3. Begin Phase 1 implementation
4. Schedule regular check-ins during development
5. Plan testing strategy with stakeholders
