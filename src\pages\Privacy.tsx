import React from 'react';
import {
  <PERSON>Content,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonBackButton,
  IonButtons,
  IonText
} from '@ionic/react';
import { useTranslation } from 'react-i18next';

const Privacy: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonBackButton />
          </IonButtons>
          <IonTitle>{t('privacy.title')}</IonTitle>
        </IonToolbar>
      </IonHeader>
      
      <IonContent className="ion-padding">
        <IonText>
          <h1>{t('privacy.title')}</h1>
          <p><strong>{t('privacy.lastUpdated')}</strong> {new Date().toLocaleDateString()}</p>
          
          <h2>1. {t('privacy.informationWeCollect')}</h2>
          <p>{t('privacy.informationWeCollectText')}</p>
          
          <h2>2. {t('privacy.howWeUse')}</h2>
          <p>{t('privacy.howWeUseText')}</p>
          <ul>
            <li>{t('privacy.howWeUseList.0')}</li>
            <li>{t('privacy.howWeUseList.1')}</li>
            <li>{t('privacy.howWeUseList.2')}</li>
            <li>{t('privacy.howWeUseList.3')}</li>
          </ul>
          
          <h2>3. {t('privacy.informationSharing')}</h2>
          <p>{t('privacy.informationSharingText')}</p>
          
          <h2>4. {t('privacy.dataSecurity')}</h2>
          <p>{t('privacy.dataSecurityText')}</p>
          
          <h2>5. {t('privacy.cookiesTracking')}</h2>
          <p>{t('privacy.cookiesTrackingText')}</p>
          
          <h2>6. {t('privacy.thirdPartyServices')}</h2>
          <p>{t('privacy.thirdPartyServicesText')}</p>
          
          <h2>7. {t('privacy.childrensPrivacy')}</h2>
          <p>{t('privacy.childrensPrivacyText')}</p>
          
          <h2>8. {t('privacy.changesToPolicy')}</h2>
          <p>{t('privacy.changesToPolicyText')}</p>
          
          <h2>9. {t('privacy.contactUs')}</h2>
          <p>{t('privacy.contactUsText')}</p>
        </IonText>
      </IonContent>
    </IonPage>
  );
};

export default Privacy;