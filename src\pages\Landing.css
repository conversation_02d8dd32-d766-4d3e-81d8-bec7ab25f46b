/* Landing.css */

.landing-page {
  --background: #000000;
  --ion-background-color: #000000;
  font-family: 'Inter', sans-serif; /* Recommend adding Inter font if not present */
}

/* NAVBAR */
.landing-navbar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 3rem;
  z-index: 100;
}

.navbar-logo {
  font-size: 1.5rem;
  font-weight: 800;
  color: #fff;
  letter-spacing: -0.5px;
}

.navbar-links {
  display: none; /* Hide on mobile by default */
}

@media (min-width: 768px) {
  .navbar-links {
    display: flex;
    gap: 2rem;
  }
  
  .navbar-links span {
    color: #ccc;
    cursor: pointer;
    font-size: 0.95rem;
    transition: color 0.2s;
  }
  
  .navbar-links span:hover {
    color: #fff;
  }
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.get-app-btn {
  --background: #fff;
  --color: #000;
  font-weight: 600;
  --padding-start: 1.5rem;
  --padding-end: 1.5rem;
}

/* HERO SECTION - Keep existing styles */
.hero-section {
  position: relative;
  min-height: 90vh; /* Increased height */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  padding: 2rem;
  overflow: hidden;
  background: radial-gradient(circle at 50% 50%, #1a1a2e 0%, #000000 100%);
}

.hero-content {
  z-index: 2;
  max-width: 800px;
  margin-top: 4rem; /* Offset for navbar */
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  letter-spacing: -0.05em;
}

.gradient-text {
  background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  -webkit-background-clip: text;
  background-clip: text; /* Standard property */
  -webkit-text-fill-color: transparent;
}

.white-text {
  color: #ffffff;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #a0a0a0;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  --background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  --border-radius: 50px;
  --box-shadow: 0 4px 15px rgba(0, 210, 255, 0.4);
  font-weight: 700;
  letter-spacing: 0.5px;
  width: 100%;
  max-width: 300px;
  height: 56px;
  font-size: 1.1rem;
}

.hero-visual {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
  opacity: 0.6;
}

.glow-sphere {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(58,123,213,0.15) 0%, rgba(0,0,0,0) 70%);
  border-radius: 50%;
}

.floating-globe {
  font-size: 20rem;
  color: rgba(255, 255, 255, 0.03);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translate(-50%, -50%) translateY(0px) rotate(0deg) scale(1); }
  50% { transform: translate(-50%, -50%) translateY(-30px) rotate(10deg) scale(1.1); }
  100% { transform: translate(-50%, -50%) translateY(0px) rotate(0deg) scale(1); }
}

.animate-fade-in {
  animation: fadeIn 1.2s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* BROWSE SECTION */
.browse-section {
  padding: 5rem 1rem;
  background: #000;
  text-align: center;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #fff;
}

.section-subtitle {
  color: #888;
  font-size: 1.1rem;
  margin-bottom: 3rem;
}

.destinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

.destination-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.destination-card:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 10px 30px rgba(0, 210, 255, 0.2);
  border-color: rgba(0, 210, 255, 0.3);
}

.flag-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.destination-card h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #fff;
  margin: 0 0 0.5rem;
}

.destination-card p {
  color: #00d2ff;
  font-weight: 600;
  margin: 0;
}

/* FEATURES SECTION */
.features-section {
  padding: 4rem 1rem;
  background: #050505;
}

.feature-card {
  padding: 2rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  text-align: center;
  height: 100%;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.05);
}

.icon-box {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 1.8rem;
}

.icon-box.blue { background: rgba(0, 210, 255, 0.1); color: #00d2ff; }
.icon-box.purple { background: rgba(147, 51, 234, 0.1); color: #9333ea; }
.icon-box.green { background: rgba(34, 197, 94, 0.1); color: #22c55e; }

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #fff;
}

.feature-card p {
  color: #a0a0a0;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* TESTIMONIALS SECTION */
.testimonials-section {
  padding: 5rem 1rem;
  background: linear-gradient(to bottom, #050505, #0a0a0a);
  text-align: center;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 3rem auto 0;
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.03);
  padding: 2rem;
  border-radius: 16px;
  text-align: left;
  border: 1px solid rgba(255, 255, 255, 0.03);
}

.stars {
  font-size: 1rem;
  margin-bottom: 1rem;
  color: #ffc107;
}

.testimonial-card p {
  font-size: 1.1rem;
  color: #ddd;
  font-style: italic;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.user {
  color: #888;
  font-weight: 600;
  font-size: 0.9rem;
}

/* TRUST STRIP */
.trust-strip {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 3rem;
  padding: 3rem 1rem;
  background: #000;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #888;
  font-size: 1.1rem;
  font-weight: 500;
}

.trust-item ion-icon {
  color: #00d2ff; /* Accent color */
  font-size: 1.4rem;
}

/* CTA SECTION */
.cta-section {
  padding: 6rem 1rem;
  text-align: center;
  background: radial-gradient(circle at 50% 50%, #111, #000);
}

.cta-section h2 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: #fff;
}

.cta-section p {
  color: #aaa;
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
}

/* FOOTER */
.landing-footer {
  background: #000;
  padding: 4rem 1rem 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.footer-content {
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
  margin-bottom: 3rem;
}

.footer-col {
  flex: 1;
  min-width: 200px;
}

.footer-col h4 {
  color: #fff;
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}

.footer-col p {
  color: #666;
  line-height: 1.6;
}

.footer-col span {
  display: block;
  color: #888;
  margin-bottom: 0.75rem;
  cursor: pointer;
  transition: color 0.2s;
}

.footer-col span:hover {
  color: #fff;
}

.copyright {
  text-align: center;
  color: #444;
  font-size: 0.9rem;
  margin-top: 3rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  padding-top: 2rem;
}

/* APP DOWNLOAD BADGES */
.app-download-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.store-badge {
  display: flex;
  align-items: center;
  background: #ffffff;
  color: #000000;
  padding: 0.8rem 1.8rem;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 220px;
  box-shadow: 0 4px 15px rgba(255,255,255,0.05);
  border: 1px solid rgba(255,255,255,0.1);
  text-decoration: none;
}

.store-badge:hover {
  transform: translateY(-5px) scale(1.02);
  background: #f8f8f8;
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.15);
}

.store-badge ion-icon {
  font-size: 2.5rem;
  margin-right: 1.2rem;
}

.badge-text {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.badge-text small {
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1.2;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

.badge-text span {
  font-size: 1.5rem;
  font-weight: 800;
  letter-spacing: -0.5px;
  line-height: 1.2;
}

.cta-secondary-actions {
  margin-top: 3rem;
  display: flex;
  justify-content: center;
}

/* Navbar Login Button */
.navbar-actions .login-btn {
  --color: #ffffff;
  color: #ffffff;
  font-weight: 600;
  margin-right: 0.5rem;
  --ripple-color: rgba(255, 255, 255, 0.3);
}

.contact-support-btn {
  --color: #ffffff !important;
  color: #ffffff !important;
  --border-color: #ffffff !important;
  --border-style: solid;
  --border-width: 1px;
  --ripple-color: #ffffff;
  
  font-weight: 700 !important;
  letter-spacing: 1px;
  font-size: 1rem;
  opacity: 1 !important;
  text-transform: uppercase;
  margin-top: 1rem;
  width: auto;
  min-width: 200px;
}

.contact-support-btn:hover {
  --background: rgba(255, 255, 255, 0.1);
}

/* MOBILE RESPONSIVE TWEAKS */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .landing-navbar {
    padding: 1rem;
  }
  
  .navbar-logo {
     font-size: 1.2rem;
  }
  
  .hero-content {
    margin-top: 2rem;
  }
}
