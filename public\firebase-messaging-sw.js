importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

firebase.initializeApp({
  apiKey: "AIzaSyCUl3xL9eGSxlAf_-aQNYWzEwlzaQwKn0Q",
  authDomain: "esim-numero.firebaseapp.com",
  projectId: "esim-numero",
  storageBucket: "esim-numero.firebasestorage.app",
  messagingSenderId: "377397638527",
  appId: "1:377397638527:web:f499f52d25be408b89164a"
});

const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log('Received background message ', payload);
  
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/assets/icon/favicon.png',
    badge: '/assets/icon/favicon.png',
    tag: 'esim-notification',
    data: payload.data
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

self.addEventListener('notificationclick', (event) => {
  console.log('Notification click received.');
  
  event.notification.close();
  
  // Handle notification click
  if (event.notification.data && event.notification.data.route) {
    event.waitUntil(
      clients.openWindow(event.notification.data.route)
    );
  } else {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});