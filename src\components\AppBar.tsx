import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonMenuButton,
  IonBackButton,
  IonButton,
  IonIcon
} from '@ionic/react';
import { arrowBack } from 'ionicons/icons';
// import './AppBar.css';

interface AppBarProps {
  title: string;
  showBackButton?: boolean;
  backButtonHref?: string;
  showMenuButton?: boolean;
  rightButtons?: React.ReactNode;
  color?: string;
  translucent?: boolean;
}

const AppBar: React.FC<AppBarProps> = ({
  title,
  showBackButton = false,
  backButtonHref,
  showMenuButton = true,
  rightButtons,
  color = 'light',
  translucent = true
}) => {
  return (
    <IonHeader className="app-bar" translucent={translucent}>
      <IonToolbar color={color}>
        <IonButtons slot="start">
          {showBackButton ? (
            backButtonHref ? (
              <IonBackButton defaultHref={backButtonHref} />
            ) : (
              <IonButton fill="clear" onClick={() => window.history.back()}>
                <IonIcon icon={arrowBack} />
              </IonButton>
            )
          ) : showMenuButton ? (
            <IonMenuButton />
          ) : null}
        </IonButtons>
        
        <IonTitle className="app-bar-title">{title}</IonTitle>
        
        {rightButtons && (
          <IonButtons slot="end">
            {rightButtons}
          </IonButtons>
        )}
      </IonToolbar>
    </IonHeader>
  );
};

export default AppBar;
