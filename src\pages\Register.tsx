import React, { useState } from 'react';
import {
  IonContent,
  IonPage,
  IonItem,
  IonLabel,
  IonInput,
  IonButton,
  IonText,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonSpinner,
  IonCheckbox,
  IonBackButton,
  IonButtons
} from '@ionic/react';
import { mail, lockClosed, person, logoGoogle, arrowBack } from 'ionicons/icons';
import { createUserWithEmailAndPassword, updateProfile, signInWithPopup } from 'firebase/auth';
import { auth } from '../firebase';
import { Capacitor } from '@capacitor/core';
import { useHistory } from 'react-router-dom';
import { CapacitorAuthService } from '../services/capacitorAuthService';
import './Auth.css';

const Register: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [acceptTerms, setAcceptTerms] = useState(false);
  const history = useHistory();

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (!acceptTerms) {
      setError('Please accept the terms and conditions');
      return;
    }

    setLoading(true);
    setError('');

    try {
      if (Capacitor.isNativePlatform()) {
        const result = await CapacitorAuthService.createUserWithEmailAndPassword(email, password);

        if (result.success) {
          history.push('/countries');
        } else {
          setError(result.error || 'Registration failed');
        }
      } else {
        const userCredential = await createUserWithEmailAndPassword(auth, email, password);

        if (displayName) {
          await updateProfile(userCredential.user, {
            displayName: displayName
          });
        }

        history.push('/countries');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    if (!acceptTerms) {
      setError('Please accept the terms and conditions');
      return;
    }

    setLoading(true);
    setError('');

    try {
      if (Capacitor.isNativePlatform()) {
        const capacitorResult = await CapacitorAuthService.signInWithGoogle();

        if (capacitorResult.success) {
          history.push('/countries');
        } else {
          setError(capacitorResult.error || 'Google sign-in failed');
        }
      } else {
        const { googleProvider } = await import('../firebase');
        await signInWithPopup(auth, googleProvider);
        history.push('/countries');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <IonPage className="auth-page">
      <IonContent fullscreen className="auth-content">
        <div className="auth-wrapper auth-fade-in">
          <div className="auth-sidebar">
            <div className="sidebar-content">
              <div className="sidebar-logo">KrakenSim</div>
              <h2 className="sidebar-tagline">
                Ready to travel <br />
                <span style={{ opacity: 0.7 }}>Smarter?</span>
              </h2>
              <p className="sidebar-description">
                Get instant access to global mobile data. Save up to 90% on roaming costs. Join the community of smart travelers today.
              </p>
            </div>
          </div>

          <div className="auth-container">
            <IonCard className="auth-card" style={{ width: '100%', maxWidth: '440px', boxShadow: 'none', background: 'transparent', border: 'none' }}>
              <IonCardHeader className="auth-header">
                <div style={{ textAlign: 'center' }}>
                  <IonCardTitle className="auth-title">
                    Create Account
                  </IonCardTitle>
                  <p className="auth-subtitle">Join KrakenSim and stay connected globally</p>
                </div>
              </IonCardHeader>

              <IonCardContent>
                <form onSubmit={handleRegister} className="auth-form">
                  <IonItem className="auth-item" lines="none">
                    <IonIcon icon={person} slot="start" />
                    <IonLabel position="stacked">Full Name</IonLabel>
                    <IonInput
                      type="text"
                      value={displayName}
                      onIonInput={(e) => setDisplayName(e.detail.value!)}
                      placeholder="John Doe"
                    />
                  </IonItem>

                  <IonItem className="auth-item" lines="none">
                    <IonIcon icon={mail} slot="start" />
                    <IonLabel position="stacked">Email</IonLabel>
                    <IonInput
                      type="email"
                      value={email}
                      onIonInput={(e) => setEmail(e.detail.value!)}
                      required
                      placeholder="<EMAIL>"
                    />
                  </IonItem>

                  <IonItem className="auth-item" lines="none">
                    <IonIcon icon={lockClosed} slot="start" />
                    <IonLabel position="stacked">Password</IonLabel>
                    <IonInput
                      type="password"
                      value={password}
                      onIonInput={(e) => setPassword(e.detail.value!)}
                      required
                      placeholder="••••••••"
                    />
                  </IonItem>

                  <IonItem className="auth-item" lines="none">
                    <IonIcon icon={lockClosed} slot="start" />
                    <IonLabel position="stacked">Confirm Password</IonLabel>
                    <IonInput
                      type="password"
                      value={confirmPassword}
                      onIonInput={(e) => setConfirmPassword(e.detail.value!)}
                      required
                      placeholder="••••••••"
                    />
                  </IonItem>

                  <IonItem className="terms-item" lines="none">
                    <IonCheckbox
                      slot="start"
                      className="terms-checkbox"
                      checked={acceptTerms}
                      onIonChange={(e) => setAcceptTerms(e.detail.checked)}
                    />
                    <IonLabel className="terms-label">
                      I agree to the <span onClick={() => history.push('/terms')}>Terms & Conditions</span> and <span onClick={() => history.push('/privacy')}>Privacy Policy</span>
                    </IonLabel>
                  </IonItem>

                  {error && (
                    <div className="auth-error">
                      {error}
                    </div>
                  )}

                  <IonButton
                    expand="block"
                    type="submit"
                    disabled={loading}
                    className="auth-button-main"
                  >
                    {loading ? <IonSpinner name="crescent" /> : 'Create Account'}
                  </IonButton>
                </form>

                <IonButton
                  expand="block"
                  fill="clear"
                  onClick={handleGoogleAuth}
                  disabled={loading}
                  className="auth-button-google"
                >
                  <IonIcon icon={logoGoogle} />
                  Continue with Google
                </IonButton>

                <div className="auth-footer">
                  <p>
                    Already have an account?{' '}
                    <span
                      className="auth-link"
                      onClick={() => history.push('/login')}
                    >
                      Sign In
                    </span>
                  </p>
                </div>
              </IonCardContent>
            </IonCard>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default Register;
