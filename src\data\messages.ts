export interface Message {
  fromName: string;
  subject: string;
  date: string;
  id: number;
}

const messages: Message[] = [
  {
    fromName: '<PERSON>',
    subject: 'New event: Trip to Vegas',
    date: '9:32 AM',
    id: 0
  },
  {
    fromName: '<PERSON>',
    subject: 'Long time no chat',
    date: '6:12 AM',
    id: 1
  },
  {
    fromName: '<PERSON>',
    subject: 'Report Results',
    date: '4:55 AM',
    id: 2

  },
  {
    fromName: '<PERSON>',
    subject: 'The situation',
    date: 'Yesterday',
    id: 3
  },
  {
    fromName: '<PERSON>',
    subject: 'Updated invitation: Swim lessons',
    date: 'Yesterday',
    id: 4
  },
  {
    fromName: '<PERSON>',
    subject: 'Last minute ask',
    date: 'Yesterday',
    id: 5
  },
  {
    fromName: '<PERSON>',
    subject: 'Family Calendar - Version 1',
    date: 'Last Week',
    id: 6
  },
  {
    fromName: '<PERSON>',
    subject: 'Placeholder Headhots',
    date: 'Last Week',
    id: 7
  }
];

export const getMessages = () => messages;

export const getMessage = (id: number) => messages.find(m => m.id === id);
