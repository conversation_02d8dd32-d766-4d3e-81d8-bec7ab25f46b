"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDataPlans = exports.getESimPricing = exports.ESimService = void 0;
const functions = require("firebase-functions");
const cors = require("cors");
const dotenv = require("dotenv");
const utils_1 = require("./utils");
const corsHandler = cors({ origin: true });
class ESimService {
    constructor() {
        // Load environment variables when service is actually instantiated
        dotenv.config();
        this.apiKey = process.env.SMSPOOL_API_KEY || '';
        this.baseUrl = process.env.SMSPOOL_BASE_URL || 'https://api.smspool.net';
        this.defaultLength = process.env.SMSPOOL_DEFAULT_LENGTH || '200';
        if (!this.apiKey) {
            throw new Error('SMSPOOL_API_KEY is required');
        }
    }
    async getESimPricing(params = {}) {
        var _a;
        const formData = new URLSearchParams();
        formData.append('key', this.apiKey);
        formData.append('start', params.start || '');
        formData.append('length', params.length || this.defaultLength);
        formData.append('Search', params.search || '');
        try {
            const response = await fetch(`${this.baseUrl}/esim/pricing`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData.toString(),
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            console.log('Original API response sample:', (_a = data.data) === null || _a === void 0 ? void 0 : _a[0]);
            // Apply profit margins
            data.data = data.data.map(plan => {
                console.log('Original price:', plan.price);
                const calculatedPrice = utils_1.PricingUtils.calculateESimPrice(plan.price);
                console.log('Calculated price:', calculatedPrice);
                return Object.assign(Object.assign({}, plan), { originalPrice: plan.price, priceUsd: calculatedPrice.toString() });
            });
            return data;
        }
        catch (error) {
            console.error('Error fetching eSIM pricing:', error);
            throw new Error(`Failed to fetch eSIM pricing: ${error}`);
        }
    }
    async getDataPlans(planId) {
        const formData = new URLSearchParams();
        formData.append('plan', planId);
        try {
            const response = await fetch(`${this.baseUrl}/esim/topup_plans`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData.toString(),
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            // Apply profit margins
            return data.map(plan => (Object.assign(Object.assign({}, plan), { originalPrice: plan.price, priceUsd: utils_1.PricingUtils.calculateDataPlanPrice(plan.price).toString() })));
        }
        catch (error) {
            console.error('Error fetching data plans:', error);
            throw new Error(`Failed to fetch data plans: ${error}`);
        }
    }
}
exports.ESimService = ESimService;
// Firebase Functions exports
let eSimService;
function getESimService() {
    if (!eSimService) {
        eSimService = new ESimService();
    }
    return eSimService;
}
exports.getESimPricing = functions.https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        try {
            const data = req.method === 'POST' ? req.body : req.query;
            const result = await getESimService().getESimPricing(data);
            res.status(200).json(result);
        }
        catch (error) {
            console.error('Error in getESimPricing:', error);
            res.status(500).json({ error: error.message || 'Internal server error' });
        }
    });
});
exports.getDataPlans = functions.https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        var _a, _b;
        try {
            const planId = req.query.plan || ((_a = req.body) === null || _a === void 0 ? void 0 : _a.plan) || ((_b = req.body) === null || _b === void 0 ? void 0 : _b.planId);
            if (!planId) {
                throw new Error('Plan ID is required');
            }
            const result = await getESimService().getDataPlans(planId);
            res.status(200).json(result);
        }
        catch (error) {
            console.error('Error in getDataPlans:', error);
            res.status(500).json({ error: error.message || 'Internal server error' });
        }
    });
});
//# sourceMappingURL=service.js.map