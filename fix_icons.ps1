$resDir = "android\app\src\main\res"
Get-ChildItem -Path $resDir -Filter "ic_launcher*" -Recurse | ForEach-Object {
    $newName = $_.Name -replace "ic_launcher", "ic_kraken_launcher"
    $destPath = [System.IO.Path]::Combine($_.DirectoryName, $newName)
    if (-not (Test-Path $destPath)) {
        Write-Host "Copying $($_.FullName) to $destPath"
        Copy-Item $_.FullName $destPath -Force
    }
}
