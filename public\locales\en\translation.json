{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "unknown": "Unknown", "user": "User", "ok": "OK"}, "nav": {"home": "Home", "countries": "Countries", "credits": "Credits", "profile": "Profile", "settings": "Settings", "login": "<PERSON><PERSON>", "logout": "Logout", "terms": "Terms of Service", "privacy": "Privacy Policy", "esim": "eSIM", "my_esims": "My eSIMs"}, "my_esims": {"no_esims": "No eSIMs Found", "no_esims_description": "You haven't purchased any eSIMs yet. Browse our plans to get started.", "browse_plans": "Browse Plans", "esim_profile": "eSIM Profile", "activation_code": "Activation Code", "pin": "PIN", "puk": "PUK", "apn": "APN", "remaining_data": "Remaining Data", "total_data": "Total Data", "country": "Country", "section_title": "My eSIMs", "purchased_contracts": "Purchased Contracts", "data_usage": "Data Usage", "expires": "Expires", "plan": "Plan", "details": "Details", "top_up": "Top Up", "active": "Active", "purchased": "Purchased", "type": "Type", "one_time": "One-time", "install": "Install", "remove_plan": "Remove Plan", "install_esim": "Install eSIM", "scan_camera": "Scan with your phone camera", "manual_code": "Manual Activation Code", "plan_details": "Plan Details", "not_available": "N/A"}, "settings": {"title": "Settings", "notifications": {"title": "Notifications", "push": "Push Notifications", "pushDescription": "Get notified about data usage and expiring plans", "permissionRequired": "Permission Required", "enableInSettings": "Please enable notifications in your device settings to receive updates."}, "language": {"title": "Language & Region", "language": "Language"}, "support": {"title": "Support & Help", "contactSupport": "Contact Support", "contactSupportDescription": "Get help with your eSIM orders", "rateApp": "<PERSON>", "rateAppDescription": "Help us improve by rating the app", "telegram": "Telegram Support", "telegramDescription": "Join our Telegram channel for support", "website": "Support Website", "websiteDescription": "Visit our support website for FAQs", "howToUse": "How to Use eSIMs", "howToUseDescription": "Step-by-step guide for setting up and using eSIMs"}, "app": {"title": "App", "version": "Version"}}, "credits": {"title": "Credits", "loadingPackages": "Loading credit packages...", "noPackages": "No credit packages available", "currentBalance": "Current Balance", "purchaseCredits": "Purchase Credits", "yourCredits": "Your Credits", "availableCredits": "Available Credits", "lifetimePurchased": "Lifetime Purchased", "totalSpent": "Total Spent", "buyCredits": "Purchase Credits", "choosePackage": "Select the perfect credit package for your eSIM needs", "mostPopular": "Most Popular", "buyCreditsButton": "Buy {{credits}} Credits", "processing": "Processing...", "recentActivity": "Recent Activity", "creditsPurchased": "Credits Purchased", "creditsSpent": "Credits Spent", "errorLoadingData": "Error loading credits data:", "placeholderDataWarning": "Using placeholder data for development. Canadian friend will fix Stripe integration.", "signInToPurchase": "Please sign in to purchase credits.", "purchaseSuccess": "Purchase completed successfully! Your credits will be added shortly.", "purchaseFailed": "<PERSON><PERSON><PERSON> failed. Please try again.", "purchaseError": "<PERSON><PERSON><PERSON> failed. Please try again.", "purchaseCanceled": "Purchase was canceled.", "errorPurchasing": "Error purchasing credits:", "creditsCount": "{{count}} credits", "transaction": "Transaction", "trust": {"support": "24/7 Support", "instant": "Instant Delivery", "secure": "Secure Payments"}, "legal_disclaimer_v2": "By purchasing the package, you agree to {{appName}} <1>Terms</1> and <2>Privacy Policy</2>.", "packages": {"minnow": {"name": "Minnow", "vibe": "Tiny but mighty", "useCase": "Light travelers, test users"}, "tuna": {"name": "<PERSON><PERSON>", "vibe": "Reliable and steady", "useCase": "Regional users"}, "dolphin": {"name": "Dolphin", "vibe": "Smart and fast", "useCase": "Business travelers"}, "octopus": {"name": "Octopus", "vibe": "Flexible, multi-device", "useCase": "Remote workers"}, "shark": {"name": "Shark", "vibe": "Powerful, premium", "useCase": "Heavy data users"}, "whale": {"name": "Whale", "vibe": "Massive, enterprise-grade", "useCase": "Global nomads, teams"}}}, "countries": {"title": "eSIM Countries", "searchPlaceholder": "Search countries...", "loadingCountries": "Loading countries...", "noCountries": "No countries found", "selectCountry": "Select a country to view available plans", "filterName": "Name", "filterPrice": "Price", "filterData": "Data", "unknownCountry": "Unknown Country", "unknownSpeed": "Unknown"}, "plans": {"title": "{{country}} Plans", "subtitle": "High-speed eSIM data packages", "loadingPlans": "Loading plans...", "noPlans": "No plans available", "noPlansDescription": "We're working on adding plans for this destination", "buyNow": "Buy Now", "redeemCredits": "Redeem Credits", "data": "Data", "duration": "Duration", "speed": "Speed", "ipType": "IP Type", "validity": "Validity", "price": "Price", "mostPopular": "Most Popular", "networkProviders": "Network Providers", "day": "{{count}} day", "days": "{{count}} days", "month": "{{count}} month", "months": "{{count}} months", "best_value": "Best Value", "location": "Location", "network": "Network", "purchase": "Purchase", "device_required": "<PERSON><PERSON> Required", "device_required_desc": "You need an eSIM device before adding data.", "get_device_first": "Get eSIM Device First", "esim_active": "eSIM Active. Select a plan below.", "processing": "Processing...", "purchase_success": "✨ eSIM purchased successfully! Transaction ID: {{id}}", "purchase_failed": "❌ Purchase failed: {{error}}", "congrats_first": "🎉 Congratulations on your first eSIM! You can now purchase credits for better rates on future eSIMs.", "esim_purchased_redirect": "🎉 eSIM purchased successfully! Redirecting to your eSIMs...", "purchase_failed_simple": "❌ eSIM purchase failed. Please try again.", "esim_purchase_title": "eSIM Purchase", "requires_login": "Requires Login"}, "profile": {"title": "Profile", "loginRequired": "Please log in to view your profile.", "profileInformation": "Profile Information", "changePhoto": "Change Photo", "displayName": "Display Name", "enterName": "Enter your name", "email": "Email", "memberSince": "Member Since", "updateProfile": "Update Profile", "orderHistory": "Order History", "noOrders": "No orders found. Start browsing our eSIM plans!", "profileUpdate": "Profile Update", "profileUpdated": "Profile updated successfully!", "profileUpdateFailed": "Failed to update profile. Please try again.", "unknown": "Unknown"}, "privacy": {"title": "Privacy Policy", "lastUpdated": "Last updated:", "informationWeCollect": "Information We Collect", "informationWeCollectText": "We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us.", "howWeUse": "How We Use Your Information", "howWeUseText": "We use the information we collect to:", "howWeUseList": ["Provide and maintain our services", "Process transactions", "Send you technical notices and support messages", "Communicate with you about products and services"], "informationSharing": "Information Sharing", "informationSharingText": "We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.", "dataSecurity": "Data Security", "dataSecurityText": "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.", "cookiesTracking": "Cookies and Tracking", "cookiesTrackingText": "We use cookies and similar tracking technologies to track activity on our service and hold certain information.", "thirdPartyServices": "Third-Party Services", "thirdPartyServicesText": "Our service may contain links to third-party websites. We are not responsible for the privacy practices of these external sites.", "childrensPrivacy": "Children's Privacy", "childrensPrivacyText": "Our service is not intended for children under 13. We do not knowingly collect personal information from children under 13.", "changesToPolicy": "Changes to Privacy Policy", "changesToPolicyText": "We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new policy on this page.", "contactUs": "Contact Us", "contactUsText": "If you have questions about this Privacy Policy, please contact <NAME_EMAIL>"}, "terms": {"title": "Terms & Conditions", "lastUpdated": "Last updated:", "acceptanceOfTerms": "Acceptance of Terms", "acceptanceOfTermsText": "By accessing and using KrakenSim, you accept and agree to be bound by the terms and provision of this agreement.", "serviceDescription": "Service Description", "serviceDescriptionText": "KrakenSim provides digital SIM card services for mobile connectivity across various countries.", "userAccounts": "User Accounts", "userAccountsText": "You may create an account to access additional features, but account creation is not required to browse our services.", "privacy": "Privacy", "privacyText": "Your privacy is important to us. Please review our Privacy Policy to understand how we collect and use your information.", "prohibitedUses": "Prohibited Uses", "prohibitedUsesText": "You may not use our service for any unlawful purpose or to solicit others to perform unlawful acts.", "serviceAvailability": "Service Availability", "serviceAvailabilityText": "We strive to maintain service availability but cannot guarantee uninterrupted access.", "limitationOfLiability": "Limitation of Liability", "limitationOfLiabilityText": "<PERSON><PERSON><PERSON><PERSON><PERSON> shall not be liable for any indirect, incidental, special, consequential, or punitive damages.", "changesToTerms": "Changes to Terms", "changesToTermsText": "We reserve the right to modify these terms at any time. Changes will be effective immediately upon posting.", "contactInformation": "Contact Information", "contactInformationText": "For questions about these Terms, please contact <NAME_EMAIL>"}, "tutorial": {"title": "How to Use eSIMs", "backButton": "Settings", "intro": {"title": "What is an eSIM?", "description": "An eSIM (embedded SIM) is a digital SIM card that allows you to activate a cellular plan without using a physical SIM card. Perfect for travelers who want instant connectivity without swapping SIM cards."}, "steps": {"title": "Setup Instructions", "compatibility": {"title": "Step 1: Check Device Compatibility", "subtitle": "Ensure your device supports eSIM", "listTitle": "Compatible Devices:", "iphone": "iPhone: iPhone XS, XR, 11, 12, 13, 14, 15 and newer", "samsung": "Samsung: Galaxy S20, S21, S22, S23, Note 20 and newer", "google": "Google: Pixel 3, 4, 5, 6, 7, 8 and newer", "other": "Other: Most flagship phones from 2019 onwards", "check": "How to check: Go to Settings → About → Look for 'Digital SIM' or 'eSIM'"}, "purchase": {"title": "Step 2: Purchase Your eSIM", "subtitle": "Buy credits and redeem for eSIM plans", "step1": "Browse available countries in the app", "step2": "Select a data plan that suits your needs", "step3": "Purchase credits if you don't have enough", "step4": "Redeem credits for your chosen eSIM plan", "step5": "Receive activation details in 'My eSIMs'"}, "install": {"title": "Step 3: Install eSIM Profile", "subtitle": "Add the eSIM to your device", "iphoneTitle": "For iPhone:", "iphoneStep1": "Go to Settings → Cellular → Add Cellular Plan", "iphoneStep2": "Scan the QR code from 'My eSIMs' or enter activation code manually", "iphoneStep3": "Follow the on-screen instructions", "iphoneStep4": "Label your plan (e.g., 'Travel Data')", "androidTitle": "For Android:", "androidStep1": "Go to Settings → Network & Internet → Mobile Network", "androidStep2": "Tap 'Add carrier' or 'Add mobile plan'", "androidStep3": "Scan QR code or enter activation code", "androidStep4": "Follow setup instructions"}, "configure": {"title": "Step 4: Configure Settings", "subtitle": "Set up data usage and preferences", "listTitle": "Important Settings:", "step1": "Data Roaming: Turn ON for the eSIM plan", "step2": "Default Line: Choose which SIM for calls/texts", "step3": "Cellular Data: Select the eSIM for internet", "step4": "APN Settings: Usually configured automatically", "warning": "Important: Keep your original SIM active for calls and texts, use eSIM only for data unless specified otherwise."}, "activate": {"title": "Step 5: Activate & Use", "subtitle": "Start using your eSIM data", "step1": "Restart your device after installation", "step2": "Check signal strength for the eSIM", "step3": "Test internet connectivity", "step4": "Monitor data usage in device settings", "step5": "Top up if needed through the app", "troubleshootingTitle": "Troubleshooting:", "trouble1": "No signal: Check if data roaming is enabled", "trouble2": "Slow speeds: Try switching between 4G/5G", "trouble3": "Can't connect: Restart device or reset network settings"}}, "tips": {"title": "Tips & Best Practices", "tip1Title": "Install Before Travel", "tip1Desc": "Set up your eSIM while connected to WiFi before your trip", "tip2Title": "Monitor Data Usage", "tip2Desc": "Keep track of your data consumption to avoid overages", "tip3Title": "Keep Activation Details", "tip3Desc": "Save screenshots of QR codes and activation codes", "tip4Title": "Test Connection", "tip4Desc": "Verify your eSIM works before leaving WiFi coverage"}, "support": {"text": "Need help? Contact our support team through the Settings page or visit our help center for more detailed guides.", "button": "Go to Settings"}}}