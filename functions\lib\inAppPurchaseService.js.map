{"version": 3, "file": "inAppPurchaseService.js", "sourceRoot": "", "sources": ["../src/inAppPurchaseService.ts"], "names": [], "mappings": ";;;AAAA,wCAAwC;AACxC,gDAAgD;AAEhD,uDAAuD;AACvD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;IACtB,KAAK,CAAC,aAAa,EAAE,CAAC;CACvB;AA2BD,MAAa,oBAAoB;IAG/B;QACE,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAED,gFAAgF;IAChF,+DAA+D;IAC/D,KAAK,CAAC,gBAAgB,CACpB,aAAqB,EACrB,SAAiB,EACjB,QAA2B,EAC3B,YAAmB;QAEnB,IAAI;YACF,+DAA+D;YAC/D,yCAAyC;YACzC,OAAO,CAAC,GAAG,CAAC,uBAAuB,aAAa,gBAAgB,SAAS,OAAO,QAAQ,EAAE,CAAC,CAAC;YAE5F,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,aAAa;gBACb,SAAS;gBACT,YAAY,EAAE,YAAY,IAAI,IAAI,IAAI,EAAE;gBACxC,QAAQ;aACT,CAAC;SACH;QAAC,OAAO,KAAU,EAAE;YACnB,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,4BAA4B;gBACpD,QAAQ;aACT,CAAC;SACH;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,SAA+B;QACrD,IAAI;YACF,MAAM,OAAO,GAAG,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YAEpF,MAAM,WAAW,GAAgB;gBAC/B,OAAO;gBACP,MAAM,EAAE,SAAS,CAAC,MAAO;gBACzB,MAAM,EAAE,SAAS,CAAC,MAAO;gBACzB,WAAW,EAAE,SAAS,CAAC,WAAY;gBACnC,aAAa,EAAE,SAAS,CAAC,aAAc;gBACvC,QAAQ,EAAE,SAAS,CAAC,QAAS;gBAC7B,YAAY,EAAE,SAAS,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE;gBAClD,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,gBAAgB,EAAE,SAAS,CAAC,gBAAgB;gBAC5C,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACjE,OAAO,OAAO,CAAC;SAChB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,OAAe,EAAE,MAA6B,EAAE,cAAoB;QAC1F,IAAI;YACF,MAAM,UAAU,GAAQ;gBACtB,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,IAAI,cAAc,EAAE;gBAClB,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;aAC3C;YAED,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SACpE;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc;QAClC,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,EAAE;iBAC3B,UAAU,CAAC,QAAQ,CAAC;iBACpB,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;iBAC7B,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;iBAC5B,GAAG,EAAE,CAAC;YAET,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAiB,CAAC,CAAC;SAC5D;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,OAAe;QAChC,IAAI;YACF,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;YAClE,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAE,GAAG,CAAC,IAAI,EAAkB,CAAC,CAAC,CAAC,IAAI,CAAC;SACxD;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,MAAc,EACd,MAAc,EACd,WAAmB,EACnB,QAA2B,EAC3B,aAAqB,EACrB,SAAiB,EACjB,YAAmB;QAEnB,IAAI;YACF,sFAAsF;YACtF,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAClD,aAAa,EACb,SAAS,EACT,QAAQ,EACR,YAAY,CACb,CAAC;YAEF,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE;gBAC7B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,gBAAgB,CAAC,KAAK,IAAI,4BAA4B;iBAC9D,CAAC;aACH;YAED,sCAAsC;YACtC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,EAAE;iBAChC,UAAU,CAAC,QAAQ,CAAC;iBACpB,KAAK,CAAC,eAAe,EAAE,IAAI,EAAE,aAAa,CAAC;iBAC3C,GAAG,EAAE,CAAC;YAET,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;gBACxB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,+BAA+B;iBACvC,CAAC;aACH;YAED,sBAAsB;YACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC3C,MAAM;gBACN,MAAM;gBACN,WAAW;gBACX,aAAa;gBACb,QAAQ;gBACR,YAAY,EAAE,gBAAgB,CAAC,YAAY;gBAC3C,gBAAgB;aACjB,CAAC,CAAC;YAEH,mCAAmC;YACnC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAEnD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO;aACR,CAAC;SACH;QAAC,OAAO,KAAU,EAAE;YACnB,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,4BAA4B;aACrD,CAAC;SACH;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,aAAqB;QACtC,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,EAAE;iBAC3B,UAAU,CAAC,QAAQ,CAAC;iBACpB,KAAK,CAAC,eAAe,EAAE,IAAI,EAAE,aAAa,CAAC;iBAC3C,GAAG,EAAE,CAAC;YAET,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,OAAO,CAAC,IAAI,CAAC,mCAAmC,aAAa,EAAE,CAAC,CAAC;gBACjE,OAAO,KAAK,CAAC;aACd;YAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;YAEtD,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,KAAK,CAAC;SACd;IACH,CAAC;CACF;AAjMD,oDAiMC;AAED,6BAA6B;AAC7B,IAAI,oBAA0C,CAAC;AAE/C,SAAS,uBAAuB;IAC9B,IAAI,CAAC,oBAAoB,EAAE;QACzB,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;KACnD;IACD,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAEY,QAAA,oBAAoB,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAS,EAAE,OAAY,EAAE,EAAE;IAC3F,IAAI;QACF,MAAM,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,CAAA,CAAC;QAC7E,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,yBAAyB,CAAC,CAAC;SACpF;QAED,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,IAOjF,CAAC;QAEF,MAAM,kBAAkB,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE7E,MAAM,MAAM,GAAG,MAAM,uBAAuB,EAAE,CAAC,oBAAoB,CACjE,MAAM,EACN,MAAM,EACN,WAAW,EACX,QAAQ,EACR,aAAa,EACb,SAAS,EACT,kBAAkB,CACnB,CAAC;QACF,OAAO,MAAM,CAAC;KACf;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QAClE,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,KAAI,uBAAuB,CAAC,CAAC;KAC7F;AACH,CAAC,CAAC,CAAC;AAEU,QAAA,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAS,EAAE,OAAY,EAAE,EAAE;IACpF,IAAI;QACF,MAAM,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,CAAA,CAAC;QAC7E,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,yBAAyB,CAAC,CAAC;SACpF;QACD,MAAM,MAAM,GAAG,MAAM,uBAAuB,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACvE,OAAO,MAAM,CAAC;KACf;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,KAAI,uBAAuB,CAAC,CAAC;KAC7F;AACH,CAAC,CAAC,CAAC;AAEU,QAAA,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAS,EAAE,OAAY,EAAE,EAAE;IACnF,IAAI;QACF,MAAM,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,CAAA,CAAC;QAC7E,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,yBAAyB,CAAC,CAAC;SACpF;QACD,MAAM,EAAE,OAAO,EAAE,GAAG,IAA4B,CAAC;QACjD,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,CAAC;SACjF;QACD,MAAM,MAAM,GAAG,MAAM,uBAAuB,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACrE,OAAO,MAAM,CAAC;KACf;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,KAAI,uBAAuB,CAAC,CAAC;KAC7F;AACH,CAAC,CAAC,CAAC;AAEU,QAAA,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAS,EAAE,OAAY,EAAE,EAAE;IACnF,IAAI;QACF,oDAAoD;QACpD,MAAM,aAAa,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAC;QAC1C,IAAI,CAAC,aAAa,EAAE;YAClB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,2BAA2B,CAAC,CAAC;SACvF;QACD,MAAM,MAAM,GAAG,MAAM,uBAAuB,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAC3E,OAAO,MAAM,CAAC;KACf;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,KAAI,uBAAuB,CAAC,CAAC;KAC7F;AACH,CAAC,CAAC,CAAC"}