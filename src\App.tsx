import React, { useEffect } from 'react';
import { <PERSON>A<PERSON>, IonRouterOutlet, setupIonicReact } from '@ionic/react';
import { IonReactRouter } from '@ionic/react-router';
import { Redirect, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { GoogleAuthService } from './services/googleAuthService';
// import { CartProvider } from './contexts/CartContext';
import { ThemeProvider } from './contexts/ThemeContext';
// import { LocalizationProvider } from './contexts/LocalizationContext';
import { notificationService } from './services/notificationService';
import { creditsApiService } from './services/creditsApiService';
import Sidebar from './components/Sidebar';


// Import pages
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import Terms from './pages/Terms';
import Privacy from './pages/Privacy';
import Profile from './pages/Profile';
import Settings from './pages/Settings';
import ESimTutorial from './pages/ESimTutorial';
import Countries from './pages/Countries';
import Plans from './pages/Plans';
import Credits from './pages/Credits';
import MyESims from './pages/MyESims';
import Landing from './pages/Landing';
import Onboarding from './pages/Onboarding';
// import Checkout from './pages/Checkout';
// import ViewMessage from './pages/ViewMessage';

/* Core CSS required for Ionic components to work properly */
import '@ionic/react/css/core.css';
import '@ionic/react/css/normalize.css';
import '@ionic/react/css/structure.css';
import '@ionic/react/css/typography.css';
import '@ionic/react/css/padding.css';
import '@ionic/react/css/float-elements.css';
import '@ionic/react/css/text-alignment.css';
import '@ionic/react/css/text-transformation.css';
import '@ionic/react/css/flex-utils.css';
import '@ionic/react/css/display.css';
import '@ionic/react/css/palettes/dark.system.css';
import './theme/variables.css';

setupIonicReact();

const App: React.FC = () => {
  useEffect(() => {
    // Initialize app services when app starts
    const initializeApp = async () => {
      try {
        // Initialize Google Auth for native platforms
        await GoogleAuthService.initialize();

        // Sync Stripe products on app start
        await creditsApiService.getCreditPackages();

        // TODO: Re-enable notifications when Firebase messaging is properly configured
        // await notificationService.initializeNotifications();
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, []);



  return (
    <IonApp>
      <ThemeProvider>
        <AuthProvider>
          {/* <CartProvider> */}
          <IonReactRouter>
            <Sidebar />
            <IonRouterOutlet id="main-content">
              <Route path="/" exact={true}>
                <Landing />
              </Route>
              <Route path="/onboarding" exact={true}>
                <Onboarding />
              </Route>
              <Route path="/login" exact={true}>
                <Login />
              </Route>
              <Route path="/register" exact={true}>
                <Register />
              </Route>
              <Route path="/forgot-password" exact={true}>
                <ForgotPassword />
              </Route>
              <Route path="/terms" exact={true}>
                <Terms />
              </Route>
              <Route path="/privacy" exact={true}>
                <Privacy />
              </Route>
              <Route path="/profile" exact={true}>
                <Profile />
              </Route>
              <Route path="/settings" exact={true}>
                <Settings />
              </Route>
              <Route path="/esim-tutorial" exact={true}>
                <ESimTutorial />
              </Route>
              <Route path="/countries" exact={true}>
                <Countries />
              </Route>
              <Route path="/plans/:id" exact={true}>
                <Plans />
              </Route>
              <Route path="/credits" exact={true}>
                <Credits />
              </Route>
              <Route path="/my-esims" exact={true}>
                <MyESims />
              </Route>
              {/* <Route path="/checkout" exact={true}>
                <Checkout />
              </Route> */}
              <Route path="/home" exact={true}>
                <Countries />
              </Route>

            </IonRouterOutlet>
          </IonReactRouter>
          {/* </CartProvider> */}
        </AuthProvider>
      </ThemeProvider>
    </IonApp>
  );
};

export default App;




