/* Mobile-optimized app bar styles */
.app-bar {
  /* Ensure proper z-index to stay above content */
  z-index: 1000;
  /* No padding - extend to very top */
  padding-top: 0;
  /* Blend with app background */
  --background: var(--ion-background-color);
  /* Add subtle border */
  border-bottom: 1px solid var(--ion-color-light-shade);
}

.app-bar ion-toolbar {
  /* Much larger, more professional height */
  --min-height: 80px;
  --padding-start: 20px;
  --padding-end: 20px;
  /* Very generous padding for professional look */
  --padding-top: 20px;
  --padding-bottom: 20px;
  /* Professional gradient background */
  --background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);
  --color: var(--ion-color-primary-contrast);
  /* Enhanced shadow for more depth */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.app-bar-title {
  font-weight: 800;
  font-size: 1.5rem;
  text-align: center;
  color: var(--ion-color-primary-contrast);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.8px;
  margin-top: 8px;
  margin-bottom: 8px;
}

/* Menu button styling for better mobile touch */
.app-bar ion-menu-button {
  --padding-start: 24px;
  --padding-end: 24px;
  --color: var(--ion-color-primary-contrast);
  /* Enhanced professional spacing */
  margin-top: 16px;
  margin-bottom: 12px;
  /* Much larger touch target */
  min-width: 56px;
  min-height: 56px;
  /* Add subtle background for better visibility */
  /* --background: rgba(255, 255, 255, 0.1); */
  --border-radius: 8px;
}

/* Back button styling */
.app-bar ion-back-button {
  --padding-start: 8px;
  --padding-end: 8px;
  --color: var(--ion-color-primary-contrast);
}

/* Right side buttons */
.app-bar ion-buttons[slot="end"] ion-button {
  --padding-start: 8px;
  --padding-end: 8px;
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
  .app-bar {
    /* No padding on mobile - extend to very top */
    padding-top: 0;
  }

  .app-bar ion-toolbar {
    /* Extra large mobile header for premium feel */
    --min-height: 88px;
    --padding-top: 24px;
    --padding-bottom: 24px;
  }

  .app-bar-title {
    font-size: 1.4rem;
    margin-top: 8px;
    margin-bottom: 8px;
    font-weight: 800;
  }

  /* Compact menu button */
  .app-bar ion-menu-button {
    --padding-start: 12px;
    --padding-end: 12px;
    min-width: 44px;
    min-height: 44px;
    margin-top: 2px;
    margin-bottom: 2px;
  }

  .app-bar ion-back-button {
    --padding-start: 12px;
    --padding-end: 12px;
    min-width: 44px;
    min-height: 44px;
    margin-top: 2px;
    margin-bottom: 2px;
  }
}

/* Safe area adjustments for notched devices */
@supports (padding-top: env(safe-area-inset-top)) {
  .app-bar {
    /* Extend to very top, only account for notch */
    padding-top: env(safe-area-inset-top);
  }

  @media (max-width: 768px) {
    .app-bar {
      /* Extend to very top on mobile */
      padding-top: env(safe-area-inset-top);
    }
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .app-bar ion-toolbar {
    --background: var(--ion-color-dark);
    --color: var(--ion-color-dark-contrast);
  }
}
