// Credits System Type Definitions

export interface CreditPackage {
  id: string;                    // 'minnow', 'tuna', 'dolphin', 'octopus', 'shark', 'whale'
  name: string;                  // 'Minnow', 'Tuna', etc.
  animal: string;                // '🐟 Minnow', '🐟 Tuna', etc.
  price: number;                 // USD price: 5, 10, 20, 35, 50, 75
  credits: number;               // Credits awarded: 5, 10, 20, 35, 50, 75
  vibe: string;                  // 'Tiny but mighty', 'Reliable and steady', etc.
  useCase: string;               // 'Light travelers, test users', etc.
  // LemonSqueezy configuration (Active)
  lemonSqueezyVariantId?: string; // LemonSqueezy product variant ID (optional)
  lemonSqueezyProductId?: string; // LemonSqueezy product ID (optional)
  active: boolean;               // Whether package is available for purchase
  createdAt: Date;
  updatedAt: Date;
}

export interface UserCredits {
  userId: string;
  totalCredits: number;          // Current available credits
  lifetimeCredits: number;       // Total credits ever purchased
  lifetimeSpent: number;         // Total credits ever spent
  pendingCredits: number;        // Credits from pending payments
  lastUpdated: Date;
  createdAt: Date;
}

export interface CreditTransaction {
  id: string;                    // Auto-generated document ID
  userId: string;
  type: 'purchase' | 'redemption' | 'refund' | 'bonus' | 'topup';
  amount: number;                // Credits added (positive) or spent (negative)

  // Purchase details
  packageId?: string;            // Credit package purchased
  
  // Redemption details
  esimOrderId?: string;          // Related eSIM order
  esimPlanId?: string;
  esimCountryCode?: string;

  // Top-up details
  topupOrderId?: string;

  // Legacy fields (deprecated)
  orderId?: string;              // Related order ID (LemonSqueezy or in-app)
  lemonSqueezyOrderId?: string;  // LemonSqueezy order ID for purchases
  transactionId?: string;        // Platform transaction ID (iOS/Android)

  platform: 'web' | 'ios' | 'android';
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ESimCreditCost {
  id: string;                    // Auto-generated or planId_countryCode
  planId: number;                // Original eSIM plan ID
  countryCode: string;           // Country code
  countryName: string;           // Human-readable country name
  dataInGb: number;              // Data amount in GB
  creditCost: number;            // How many credits this eSIM costs
  originalPriceUsd: number;      // Original USD price for reference
  active: boolean;               // Whether this eSIM is available for redemption
  createdAt: Date;
  updatedAt: Date;
}

// Enhanced eSIM Order Management
export interface ESimOrder {
  orderId: string;
  userId: string;

  // eSIM Details
  planId: number;                // Provider plan ID
  countryCode: string;
  countryName: string;
  dataInGb: number;
  durationDays: number;

  // Pricing
  creditCost: number;            // Credits spent
  originalPriceUsd: number;      // Reference price

  // Provider Integration
  providerTransactionId?: string; // Provider's transaction ID
  activationCode?: string;
  qrCode?: string;

  // Status & Lifecycle
  status: 'pending' | 'provisioned' | 'activated' | 'expired' | 'cancelled';
  purchasedAt: Date;
  activatedAt?: Date;
  expiresAt?: Date;

  // Usage Tracking
  dataUsed: number;              // GB used
  lastUsageUpdate: Date;

  metadata: Record<string, any>;
}

export interface ESimTopup {
  topupId: string;
  userId: string;
  esimOrderId: string;           // Parent eSIM order

  // Top-up Details
  dataInGb: number;              // Additional data
  creditCost: number;            // Credits spent

  // Provider Integration
  providerTopupId?: string;

  status: 'pending' | 'completed' | 'failed';
  createdAt: Date;
  completedAt?: Date;
}

export interface ESimPricing {
  countryCode: string;
  countryName: string;
  plans: Array<{
    planId: number;
    dataInGb: number;
    durationDays: number;
    creditCost: number;
    originalPriceUsd: number;
    topupOptions: Array<{
      dataInGb: number;
      creditCost: number;
    }>;
  }>;
  active: boolean;
  lastUpdated: Date;
}

export interface CreditOrder {
  orderId: string;               // Unique order ID
  userId: string;
  type: 'credit_purchase' | 'esim_redemption';
  
  // For credit purchases
  packageId?: string;            // 'minnow', 'tuna', etc.
  creditsAwarded?: number;       // Credits given to user
  
  // For eSIM redemptions
  esimPlanId?: string;
  esimCountryCode?: string;
  creditsSpent?: number;         // Credits deducted from user
  
  // Payment/Platform info
  platform: 'web' | 'ios' | 'android';
  lemonSqueezyOrderId?: string;  // For web purchases
  transactionId?: string;        // For in-app purchases
  
  // Order details
  amountUsd?: number;            // USD amount for purchases
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  
  // Additional data
  metadata?: {
    customerEmail?: string;
    customerName?: string;
    webhookData?: any;
    [key: string]: any;
  };
}

// Helper types for UI components
export interface CreditPackageDisplay extends CreditPackage {
  isPopular?: boolean;           // For highlighting recommended packages
  savings?: string;              // Display text for savings/value
}

export interface UserCreditsSummary {
  currentBalance: number;
  lifetimeCredits: number;
  lifetimeSpent: number;
  recentTransactions: CreditTransaction[];
}

// Credit redemption types
export interface ESimRedemptionRequest {
  userId: string;
  planId: number;
  countryCode: string;
  creditsToSpend: number;
}

export interface ESimRedemptionResult {
  success: boolean;
  orderId?: string;
  creditsSpent?: number;
  remainingCredits?: number;
  error?: string;
}

// Constants for the credit packages
export const CREDIT_PACKAGES: Omit<CreditPackage, 'createdAt' | 'updatedAt'>[] = [
  {
    id: 'minnow',
    name: 'Minnow',
    animal: '🐟 Minnow',
    price: 5,
    credits: 5,
    vibe: 'Tiny but mighty',
    useCase: 'Light travelers, test users',
    active: true
  },
  {
    id: 'tuna',
    name: 'Tuna',
    animal: '🐟 Tuna',
    price: 10,
    credits: 10,
    vibe: 'Reliable and steady',
    useCase: 'Regional users',
    active: true
  },
  {
    id: 'dolphin',
    name: 'Dolphin',
    animal: '🐬 Dolphin',
    price: 20,
    credits: 20,
    vibe: 'Smart and fast',
    useCase: 'Business travelers',
    active: true
  },
  {
    id: 'octopus',
    name: 'Octopus',
    animal: '🐙 Octopus',
    price: 35,
    credits: 35,
    vibe: 'Flexible, multi-device',
    useCase: 'Remote workers',
    active: true
  },
  {
    id: 'shark',
    name: 'Shark',
    animal: '🦈 Shark',
    price: 50,
    credits: 50,
    vibe: 'Powerful, premium',
    useCase: 'Heavy data users',
    active: true
  },
  {
    id: 'whale',
    name: 'Whale',
    animal: '🐋 Whale',
    price: 75,
    credits: 75,
    vibe: 'Massive, enterprise-grade',
    useCase: 'Global nomads, teams',
    active: true
  }
];
