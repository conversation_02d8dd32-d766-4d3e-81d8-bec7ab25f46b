import React from 'react';
import {
  IonButton,
  IonIcon,
  IonPopover,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonRadio,
  IonRadioGroup,
} from '@ionic/react';
import { moon, sunny, phonePortrait } from 'ionicons/icons';
import { useTheme, Theme } from '../contexts/ThemeContext';

interface ThemeToggleProps {
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ className }) => {
  const { theme, actualTheme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = React.useState(false);

  const getThemeIcon = () => {
    if (theme === 'auto') {
      return phonePortrait;
    }
    return actualTheme === 'dark' ? moon : sunny;
  };

  const getThemeLabel = (themeOption: Theme) => {
    switch (themeOption) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'auto':
        return 'System';
      default:
        return 'System';
    }
  };

  return (
    <>
      <IonButton
        id="theme-trigger"
        fill="clear"
        className={className}
        onClick={() => setIsOpen(true)}
      >
        <IonIcon icon={getThemeIcon()} />
      </IonButton>

      <IonPopover
        trigger="theme-trigger"
        isOpen={isOpen}
        onDidDismiss={() => setIsOpen(false)}
        showBackdrop={true}
      >
        <IonContent>
          <IonList>
            <IonRadioGroup
              value={theme}
              onIonChange={(e) => setTheme(e.detail.value as Theme)}
            >
              <IonItem>
                <IonIcon icon={sunny} slot="start" />
                <IonLabel>Light</IonLabel>
                <IonRadio slot="end" value="light" />
              </IonItem>
              <IonItem>
                <IonIcon icon={moon} slot="start" />
                <IonLabel>Dark</IonLabel>
                <IonRadio slot="end" value="dark" />
              </IonItem>
              <IonItem>
                <IonIcon icon={phonePortrait} slot="start" />
                <IonLabel>System</IonLabel>
                <IonRadio slot="end" value="auto" />
              </IonItem>
            </IonRadioGroup>
          </IonList>
        </IonContent>
      </IonPopover>
    </>
  );
};

export default ThemeToggle;
