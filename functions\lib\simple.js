"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getESimPricingSimple = void 0;
const functions = require("firebase-functions");
exports.getESimPricingSimple = functions.https.onRequest(async (req, res) => {
    // Set CORS headers
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type');
    // Handle preflight OPTIONS request
    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
    }
    try {
        // Load environment variables only when function is called
        const apiKey = process.env.SMSPOOL_API_KEY;
        if (!apiKey) {
            throw new Error('SMSPOOL_API_KEY is required');
        }
        const formData = new FormData();
        formData.append('key', apiKey);
        formData.append('start', '');
        formData.append('length', '200');
        formData.append('Search', '');
        const response = await fetch('https://api.smspool.net/esim/pricing', {
            method: 'POST',
            body: formData,
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        // Simple price calculation without external utils
        if (data.data) {
            data.data = data.data.map((plan) => (Object.assign(Object.assign({}, plan), { originalPrice: plan.price, priceUsd: (parseFloat(plan.price) * 1.5).toFixed(2) })));
        }
        res.status(200).json(data);
    }
    catch (error) {
        console.error('Error in getESimPricingSimple:', error);
        res.status(500).json({ error: error.message || 'Internal server error' });
    }
});
//# sourceMappingURL=simple.js.map