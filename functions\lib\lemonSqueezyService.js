"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.lemonSqueezyWebhook = void 0;
const functions = require("firebase-functions");
const admin = require("firebase-admin");
const crypto = require("crypto");
const v2_1 = require("firebase-functions/v2");
const cors = require('cors');
const corsHandler = cors({ origin: true });
class LemonSqueezyService {
    constructor() {
        this.db = admin.firestore();
    }
    // Verify webhook signature (following LemonSqueezy official docs)
    verifyWebhookSignature(body, signature) {
        // Get webhook secret from environment variable
        const webhookSecret = process.env.LEMONSQUEEZY_WEBHOOK_SECRET;
        if (!webhookSecret) {
            v2_1.logger.warn('LemonSqueezy webhook secret not configured');
            return false;
        }
        try {
            const hmac = crypto.createHmac('sha256', webhookSecret);
            const digest = Buffer.from(hmac.update(body).digest('hex'), 'utf8');
            const receivedSignature = Buffer.from(signature || '', 'utf8');
            return crypto.timingSafeEqual(digest, receivedSignature);
        }
        catch (error) {
            v2_1.logger.error('Error verifying webhook signature:', error);
            return false;
        }
    }
    // Map LemonSqueezy product names to credit packages
    mapProductToPackage(productName) {
        const productMap = {
            'Minnow': { packageId: 'minnow', credits: 5 },
            'Tuna': { packageId: 'tuna', credits: 10 },
            'Dolphin': { packageId: 'dolphin', credits: 25 },
            'Octopus': { packageId: 'octopus', credits: 40 },
            'Shark': { packageId: 'shark', credits: 60 },
            'Whale': { packageId: 'whale', credits: 75 }
        };
        // Try exact match first
        if (productMap[productName]) {
            return productMap[productName];
        }
        // Try partial match (in case product name includes "Buy" prefix)
        for (const [key, value] of Object.entries(productMap)) {
            if (productName.includes(key)) {
                return value;
            }
        }
        return null;
    }
    // Extract user ID from customer email or custom data
    async getUserIdFromOrder(event) {
        var _a, _b;
        const { user_email } = event.data.attributes;
        // Check for custom user ID in meta.custom_data or order attributes
        const customUserId = ((_a = event.meta.custom_data) === null || _a === void 0 ? void 0 : _a.user_id) ||
            ((_b = event.data.attributes.custom) === null || _b === void 0 ? void 0 : _b.user_id);
        // If we have custom user ID, use it
        if (customUserId) {
            v2_1.logger.info(`Found user ID in custom data: ${customUserId}`);
            return customUserId;
        }
        // Otherwise, try to find user by email
        if (user_email) {
            try {
                const userRecord = await admin.auth().getUserByEmail(user_email);
                v2_1.logger.info(`Found user by email ${user_email}: ${userRecord.uid}`);
                return userRecord.uid;
            }
            catch (error) {
                v2_1.logger.warn(`Could not find user with email ${user_email}:`, error);
                return null;
            }
        }
        v2_1.logger.warn('No user identification method available in webhook');
        return null;
    }
    // Add credits to user account
    async addCreditsToUser(userId, packageId, credits, transactionId, orderData) {
        const batch = this.db.batch();
        // Mark order as processed to prevent duplicates
        const processedOrderRef = this.db.collection('processed_lemonsqueezy_orders').doc(transactionId);
        batch.set(processedOrderRef, {
            orderId: transactionId,
            userId,
            packageId,
            credits,
            processedAt: admin.firestore.FieldValue.serverTimestamp(),
            orderData: {
                orderNumber: orderData.order_number,
                totalPaid: orderData.total_usd,
                currency: orderData.currency,
                userEmail: orderData.user_email
            }
        });
        // Update user credits
        const userCreditsRef = this.db.collection('users').doc(userId).collection('credits').doc('balance');
        // Get current credits
        const currentCreditsDoc = await userCreditsRef.get();
        const currentCredits = currentCreditsDoc.exists ? currentCreditsDoc.data() : {
            totalCredits: 0,
            lifetimeCredits: 0,
            lifetimeSpent: 0,
            lastUpdated: admin.firestore.FieldValue.serverTimestamp()
        };
        // Update credits
        const newCredits = {
            totalCredits: ((currentCredits === null || currentCredits === void 0 ? void 0 : currentCredits.totalCredits) || 0) + credits,
            lifetimeCredits: ((currentCredits === null || currentCredits === void 0 ? void 0 : currentCredits.lifetimeCredits) || 0) + credits,
            lifetimeSpent: (currentCredits === null || currentCredits === void 0 ? void 0 : currentCredits.lifetimeSpent) || 0,
            lastUpdated: admin.firestore.FieldValue.serverTimestamp()
        };
        batch.set(userCreditsRef, newCredits, { merge: true });
        // Create transaction record
        const transactionRef = this.db
            .collection('users')
            .doc(userId)
            .collection('credit_transactions')
            .doc();
        const transaction = {
            id: transactionRef.id,
            userId,
            type: 'purchase',
            amount: credits,
            balanceAfter: newCredits.totalCredits,
            metadata: {
                packageId,
                packageName: packageId.charAt(0).toUpperCase() + packageId.slice(1),
                provider: 'lemonsqueezy',
                transactionId,
                orderNumber: orderData.order_number,
                totalPaid: orderData.total_usd,
                currency: orderData.currency
            },
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        };
        batch.set(transactionRef, transaction);
        // Commit the batch
        await batch.commit();
        v2_1.logger.info(`Added ${credits} credits to user ${userId} for LemonSqueezy order ${transactionId}`);
    }
    // Handle LemonSqueezy webhook
    async handleWebhook(body, signature) {
        try {
            // Verify webhook signature if provided
            if (signature && !this.verifyWebhookSignature(body, signature)) {
                v2_1.logger.error('Invalid webhook signature');
                return { success: false, error: 'Invalid signature' };
            }
            // Parse the webhook payload
            const event = JSON.parse(body);
            v2_1.logger.info(`Processing LemonSqueezy webhook: ${event.meta.event_name}`, event.data.id);
            // Only process order_created events
            if (event.meta.event_name !== 'order_created') {
                v2_1.logger.info(`Ignoring webhook event: ${event.meta.event_name}`);
                return { success: true };
            }
            // Check if order is paid
            if (event.data.attributes.status !== 'paid') {
                v2_1.logger.info(`Order ${event.data.id} is not paid yet, status: ${event.data.attributes.status}`);
                return { success: true };
            }
            // Get user ID
            const userId = await this.getUserIdFromOrder(event);
            if (!userId) {
                v2_1.logger.error(`Could not determine user ID for order ${event.data.id}`);
                return { success: false, error: 'Could not determine user ID' };
            }
            // Map product to credit package
            const productName = event.data.attributes.first_order_item.product_name;
            const packageInfo = this.mapProductToPackage(productName);
            if (!packageInfo) {
                v2_1.logger.error(`Unknown product: ${productName}`);
                return { success: false, error: `Unknown product: ${productName}` };
            }
            // Check if we've already processed this order by storing processed orders
            const processedOrderRef = this.db.collection('processed_lemonsqueezy_orders').doc(event.data.id);
            const existingOrder = await processedOrderRef.get();
            if (existingOrder.exists) {
                v2_1.logger.info(`Order ${event.data.id} already processed`);
                return { success: true };
            }
            // Add credits to user account
            await this.addCreditsToUser(userId, packageInfo.packageId, packageInfo.credits, event.data.id, event.data.attributes);
            return { success: true };
        }
        catch (error) {
            v2_1.logger.error('Failed to handle LemonSqueezy webhook:', error);
            return {
                success: false,
                error: error.message || 'Failed to process webhook'
            };
        }
    }
}
// Singleton instance
let lemonSqueezyService;
function getLemonSqueezyService() {
    if (!lemonSqueezyService) {
        lemonSqueezyService = new LemonSqueezyService();
    }
    return lemonSqueezyService;
}
// Export the webhook handler
exports.lemonSqueezyWebhook = functions.https.onRequest(async (req, res) => {
    corsHandler(req, res, async () => {
        try {
            // Get signature from X-Signature header (as per LemonSqueezy docs)
            const signature = req.headers['x-signature'];
            // Use raw body for signature verification
            const body = req.rawBody ? req.rawBody.toString() : JSON.stringify(req.body);
            const result = await getLemonSqueezyService().handleWebhook(body, signature);
            if (result.success) {
                res.status(200).json({ success: true });
            }
            else {
                res.status(400).json({ success: false, error: result.error });
            }
        }
        catch (error) {
            v2_1.logger.error('LemonSqueezy webhook error:', error);
            res.status(500).json({ success: false, error: 'Internal server error' });
        }
    });
});
//# sourceMappingURL=lemonSqueezyService.js.map