.intro-card {
  background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade));
  color: var(--ion-color-primary-contrast);
  margin: 1rem;
  border-radius: 16px;
}

.intro-card ion-card-title {
  color: var(--ion-color-primary-contrast);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.intro-card ion-icon {
  color: var(--ion-color-primary-contrast);
}

.intro-card p {
  color: var(--ion-color-primary-contrast);
  line-height: 1.6;
  margin: 0;
}

.warning-box {
  background: rgba(var(--ion-color-warning-rgb), 0.1);
  border: 1px solid var(--ion-color-warning);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.warning-box ion-icon {
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.warning-box p {
  margin: 0;
  line-height: 1.4;
}

.text-center {
  text-align: center;
}

ion-accordion-group {
  border-radius: 12px;
  overflow: hidden;
}

ion-accordion ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
}

ion-accordion[slot="content"] {
  padding: 0;
}

ion-accordion[slot="content"] h3 {
  color: var(--ion-color-primary);
  font-size: 1rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
}

ion-accordion[slot="content"] ul,
ion-accordion[slot="content"] ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

ion-accordion[slot="content"] li {
  margin: 0.5rem 0;
  line-height: 1.4;
}

ion-accordion[slot="content"] strong {
  color: var(--ion-color-dark);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .intro-card {
    margin: 0.5rem;
  }
  
  ion-accordion[slot="content"] {
    font-size: 0.9rem;
  }
}
