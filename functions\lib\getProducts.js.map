{"version": 3, "file": "getProducts.js", "sourceRoot": "", "sources": ["../src/getProducts.ts"], "names": [], "mappings": ";;;AAAA,gDAAgD;AAChD,wCAAwC;AAE3B,QAAA,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACtE,GAAG,CAAC,GAAG,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;IAC5C,GAAG,CAAC,GAAG,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC;IAC9D,GAAG,CAAC,GAAG,CAAC,8BAA8B,EAAE,6BAA6B,CAAC,CAAC;IACvE,GAAG,CAAC,GAAG,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;IAE1C,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE;QAC5B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzB,OAAO;KACR;IAED,IAAI;QACF,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,MAAM,EAAE;aACtB,UAAU,CAAC,iBAAiB,CAAC;aAC7B,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC;aAC3B,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC;aACvB,GAAG,EAAE,CAAC;QAET,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QACtD,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAEpB;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC5C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,CAAC,CAAC;KAC3D;AACH,CAAC,CAAC,CAAC"}