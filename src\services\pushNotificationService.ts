import { 
  ActionPerformed, 
  PushNotificationSchema, 
  PushNotifications, 
  Token,
  PermissionStatus as CapPermissionStatus
} from '@capacitor/push-notifications';
import { Capacitor } from '@capacitor/core';

export class PushNotificationService {
  private static instance: PushNotificationService;
  private isInitialized = false;

  static getInstance(): PushNotificationService {
    if (!PushNotificationService.instance) {
      PushNotificationService.instance = new PushNotificationService();
    }
    return PushNotificationService.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized || !Capacitor.isNativePlatform()) {
      return;
    }

    try {
      // Check if we already have permission
      const result = await PushNotifications.checkPermissions();
      
      if (result.receive === 'granted') {
        // Register with Apple / Google to receive push via APNS/FCM
        await PushNotifications.register();
        console.log('Push notifications registered successfully');
        
        // Set up listeners
        this.setupListeners();
        this.isInitialized = true;
      } else {
        console.log('Push notification permission not granted yet');
      }

    } catch (error) {
      console.error('Error initializing push notifications:', error);
    }
  }

  async checkPermissions(): Promise<CapPermissionStatus> {
    if (!Capacitor.isNativePlatform()) {
      return { receive: 'granted' } as CapPermissionStatus;
    }
    return await PushNotifications.checkPermissions();
  }

  async requestPermissions(): Promise<CapPermissionStatus> {
    if (!Capacitor.isNativePlatform()) {
      return { receive: 'granted' } as CapPermissionStatus;
    }
    return await PushNotifications.requestPermissions();
  }

  private setupListeners(): void {
    // On success, we should be able to receive notifications
    PushNotifications.addListener('registration', (token: Token) => {
      console.log('Push registration success, token:', token.value);
      // TODO: Send token to your backend to associate with user
      this.saveTokenToBackend(token.value);
    });

    // Some issue with our setup and push will not work
    PushNotifications.addListener('registrationError', (error: any) => {
      console.error('Error on registration:', error);
    });

    // Show us the notification payload if the app is open on our device
    PushNotifications.addListener('pushNotificationReceived', (notification: PushNotificationSchema) => {
      console.log('Push received:', notification);
      // Handle notification when app is in foreground
      this.handleForegroundNotification(notification);
    });

    // Method called when tapping on a notification
    PushNotifications.addListener('pushNotificationActionPerformed', (notification: ActionPerformed) => {
      console.log('Push action performed:', notification);
      // Handle notification tap
      this.handleNotificationTap(notification);
    });
  }

  private async saveTokenToBackend(token: string): Promise<void> {
    try {
      // TODO: Implement API call to save token to user's profile
      console.log('Saving push token to backend:', token);
      
      // Example implementation:
      // const user = auth.currentUser;
      // if (user) {
      //   await updateDoc(doc(db, 'users', user.uid), {
      //     pushToken: token,
      //     pushTokenUpdatedAt: new Date()
      //   });
      // }
    } catch (error) {
      console.error('Error saving push token:', error);
    }
  }

  private handleForegroundNotification(notification: PushNotificationSchema): void {
    // Show a toast or modal when notification is received while app is open
    console.log('Handling foreground notification:', notification.title, notification.body);
    
    // You can show a toast or alert here
    // Example: presentToast(notification.title + ': ' + notification.body);
  }

  private handleNotificationTap(notification: ActionPerformed): void {
    // Navigate to specific page based on notification data
    console.log('Handling notification tap:', notification.notification);
    
    const data = notification.notification.data;
    
    // Example navigation based on notification type
    if (data?.type === 'esim_ready') {
      // Navigate to MyESims page
      window.location.href = '/my-esims';
    } else if (data?.type === 'credit_added') {
      // Navigate to Credits page
      window.location.href = '/credits';
    }
    // Add more navigation logic as needed
  }

  async getDeliveredNotifications(): Promise<any[]> {
    if (!Capacitor.isNativePlatform()) {
      return [];
    }

    try {
      const result = await PushNotifications.getDeliveredNotifications();
      return result.notifications;
    } catch (error) {
      console.error('Error getting delivered notifications:', error);
      return [];
    }
  }

  async removeDeliveredNotifications(notifications: any[]): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    try {
      await PushNotifications.removeDeliveredNotifications({ notifications });
    } catch (error) {
      console.error('Error removing delivered notifications:', error);
    }
  }

  async removeAllDeliveredNotifications(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    try {
      await PushNotifications.removeAllDeliveredNotifications();
    } catch (error) {
      console.error('Error removing all delivered notifications:', error);
    }
  }
}

export const pushNotificationService = PushNotificationService.getInstance();
