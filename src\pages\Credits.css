/* Credits Page Styling */

/* --- Page Container --- */
.credits-page-content {
  --background: #000000; /* True black */
}

.credits-container {
  padding-bottom: 80px;
}

.credits-header-section {
  text-align: center;
  margin-bottom: 24px;
  padding: 16px 16px 0;
}

.credits-header-section h1 {
  font-size: 1.8rem;
  font-weight: 800;
  margin-bottom: 4px;
  color: #ffffff;
}

.credits-header-section p {
  color: #888888;
  font-size: 0.9rem;
  margin: 0;
}

/* --- Credits Summary Card (Top) --- */
.credits-summary {
  margin: 16px;
  background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade)) !important;
  color: var(--ion-color-primary-contrast) !important;
  border-radius: 20px !important;
  box-shadow: 0 8px 24px rgba(var(--ion-color-primary-rgb), 0.3) !important;
  border: none !important;
}

.credits-summary ion-card-title {
  color: var(--ion-color-primary-contrast) !important;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.credits-summary ion-icon {
  color: var(--ion-color-primary-contrast) !important;
}

.credits-balance {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.balance-item h1 {
  margin: 0;
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--ion-color-primary-contrast) !important;
  line-height: 1;
}

.balance-item p {
  margin: 4px 0 0 0;
  font-size: 0.9rem;
  opacity: 0.9;
  color: var(--ion-color-primary-contrast) !important;
}

.balance-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-item small {
  font-size: 0.75rem;
  opacity: 0.8;
  color: var(--ion-color-primary-contrast) !important;
  text-transform: uppercase;
}

.stat-item p {
  margin: 2px 0 0 0;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--ion-color-primary-contrast) !important;
}

/* --- Section Titles --- */
.section-title {
  text-align: center;
  margin-bottom: 20px;
  padding: 0 16px;
}

.section-title h2 {
  font-size: 1.4rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 4px;
}

.section-title p {
  color: #888888;
  font-size: 0.9rem;
  margin: 0;
}

/* --- Credit Packages Grid --- */
.credits-packages-grid {
  padding: 0 8px;
}

/* --- The Card (Matches Plans.css .plan-card) --- */
.credit-card {
  margin: 0 8px 16px 8px;
  border-radius: 20px;
  background: linear-gradient(145deg, #1a1a1a 0%, #111111 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.6);
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  overflow: visible;
}

.credit-card.popular {
  border: 1px solid rgba(56, 128, 255, 0.3);
  background: linear-gradient(145deg, rgba(56, 128, 255, 0.08) 0%, #0d0d0d 100%);
}

.popular-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: #3880ff;
  color: white;
  padding: 2px 12px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(56, 128, 255, 0.3);
  z-index: 10;
}

/* --- Card Header --- */
.credit-card-header {
  padding: 24px 16px 16px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.04);
}

.credit-amount {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1;
  color: #ffffff;
  letter-spacing: -1px;
}

.credit-label {
  font-size: 1rem;
  font-weight: 600;
  color: #888888;
  margin-top: -4px;
  display: block;
}

.price-pill {
  display: inline-block;
  background: rgba(45, 212, 191, 0.1);
  color: #2dd4bf; 
  padding: 6px 16px;
  border-radius: 50px;
  margin-top: 12px;
  font-weight: 700;
  font-size: 1.1rem;
  border: 1px solid rgba(45, 212, 191, 0.2);
}

/* --- Card Content --- */
.credit-card-content {
  padding: 16px 16px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.features-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-bottom: 24px;
}

.feature-row {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.03);
  padding: 8px 12px;
  border-radius: 12px;
  gap: 12px;
}

.feature-icon-wrapper {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a5b4fc;
  font-size: 1.2rem;
}

.feature-info {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.feature-label {
  font-size: 0.7rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.feature-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #e0e0e0;
}

/* --- Action Button --- */
.action-btn {
  --border-radius: 14px;
  height: 48px;
  font-weight: 700;
  font-size: 1rem;
  margin: 0;
  width: 100%;
  letter-spacing: 0.3px;
  --background: #3880ff;
  --background-hover: #3171e0;
  --box-shadow: 0 4px 12px rgba(56, 128, 255, 0.25);
}

.action-btn.processing {
  --background: var(--ion-color-step-150);
  opacity: 0.7;
}

/* --- Powered By --- */
.powered-by-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 32px;
  gap: 8px;
  padding: 16px;
  opacity: 0.6;
}

.powered-by-text {
  color: #666;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* --- Recent Transactions --- */
.recent-transactions {
  margin: 24px 16px 16px;
  border-radius: 20px !important;
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
  background: #111111 !important;
}

.recent-transactions ion-card-title {
  color: #fff !important;
}

.recent-transactions ion-item {
  --background: transparent !important;
  --color: #fff !important;
  --border-color: rgba(255,255,255,0.1) !important;
}

.recent-transactions ion-label p {
  color: #888 !important;
}

/* --- Loading States --- */
.loading-view {
  min-height: 40vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
}
/* --- Trust Features --- */
.trust-features {
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-around;
  gap: 16px;
  margin: 0 16px 24px;
}

.trust-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 8px;
  flex: 1;
}

.trust-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05); /* Slightly lighter for better visibility */
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2dd4bf;
  font-size: 1.4rem;
  margin-bottom: 4px;
  border: 1px solid rgba(255,255,255,0.1);
}

.trust-item span {
  font-size: 0.75rem;
  color: #aaa;
  font-weight: 500;
  line-height: 1.2;
}

/* --- Legal Footer --- */
.legal-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 8px;
  position: relative;
  z-index: 10;
}

.legal-link {
  font-size: 0.8rem !important;
  color: #888 !important;
  text-decoration: none;
  transition: all 0.2s ease;
}

.legal-link:hover {
  color: #fff !important;
  text-decoration: underline !important;
}
