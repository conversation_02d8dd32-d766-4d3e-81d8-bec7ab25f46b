import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator, GoogleAuthProvider } from 'firebase/auth';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { Capacitor } from '@capacitor/core';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCUl3xL9eGSxlAf_-aQNYWzEwlzaQwKn0Q",
  authDomain: "esim-numero.firebaseapp.com",
  projectId: "esim-numero",
  storageBucket: "esim-numero.firebasestorage.app",
  messagingSenderId: "377397638527",
  appId: "1:377397638527:web:f499f52d25be408b89164a"
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();
export const functions = getFunctions(app);
export const firestore = getFirestore(app);

// Only initialize messaging on web platforms
export const messaging = !Capacitor.isNativePlatform() ? (() => {
  try {
    const { getMessaging } = require('firebase/messaging');
    return getMessaging(app);
  } catch (error) {
    console.warn('Firebase Messaging not available:', error);
    return null;
  }
})() : null;

// Emulators disabled - using production
// if (window.location.hostname === 'localhost') {
//   connectAuthEmulator(auth, 'http://localhost:9099');
//   connectFirestoreEmulator(firestore, 'localhost', 8081);
//   connectFunctionsEmulator(functions, 'localhost', 5002);
// }

// Configure Google provider
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

// For mobile apps, configure redirect URL properly
if (typeof window !== 'undefined' && (window.location.protocol === 'capacitor:' || window.location.protocol === 'ionic:')) {
  // Set custom redirect URL for Capacitor/Ionic apps
  googleProvider.setCustomParameters({
    prompt: 'select_account',
    redirect_uri: 'esimnumero://app/login'
  });

  // Also configure auth domain for mobile
  auth.settings.appVerificationDisabledForTesting = false;
}
