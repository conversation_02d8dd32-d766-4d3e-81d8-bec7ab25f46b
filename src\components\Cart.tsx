import React from 'react';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonText,
  IonBadge,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonChip,
  IonGrid,
  IonRow,
  IonCol
} from '@ionic/react';
import {
  close,
  trash,
  add,
  remove,
  card,
  cardOutline,
  bagOutline
} from 'ionicons/icons';
import { useCart } from '../contexts/CartContext';
import { CartService } from '../services/cartService';
import { useHistory } from 'react-router-dom';
import './Cart.css';

interface CartProps {
  isOpen: boolean;
  onClose: () => void;
}

const Cart: React.FC<CartProps> = ({ isOpen, onClose }) => {
  const { state, removeItem, updateQuantity, clearCart } = useCart();
  const history = useHistory();

  const summary = CartService.calculateCartSummary(state.items);

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId);
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const handleCheckout = () => {
    onClose();
    history.push('/checkout');
  };

  const formatDuration = (days: number) => {
    if (days === 1) return '1 day';
    if (days < 30) return `${days} days`;
    if (days === 30) return '1 month';
    return `${Math.floor(days / 30)} months`;
  };

  return (
    <IonModal isOpen={isOpen} onDidDismiss={onClose} className="cart-modal">
      <IonHeader>
        <IonToolbar>
          <IonTitle>Shopping Cart</IonTitle>
          <IonButtons slot="end">
            <IonButton fill="clear" onClick={onClose}>
              <IonIcon icon={close} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent className="cart-content">
        {state.items.length === 0 ? (
          <div className="empty-cart">
            <IonIcon icon={bagOutline} className="empty-cart-icon" />
            <h2>Your cart is empty</h2>
            <p>Add some eSIM plans to get started</p>
            <IonButton fill="solid" onClick={onClose}>
              Continue Shopping
            </IonButton>
          </div>
        ) : (
          <>
            <IonList className="cart-items">
              {state.items.map((item) => (
                <IonCard key={item.id} className="cart-item-card">
                  <IonCardHeader>
                    <div className="cart-item-header">
                      <IonCardTitle>{item.countryName}</IonCardTitle>
                      <IonButton
                        fill="clear"
                        color="danger"
                        onClick={() => removeItem(item.id)}
                        className="remove-item-btn"
                      >
                        <IonIcon icon={trash} />
                      </IonButton>
                    </div>
                  </IonCardHeader>

                  <IonCardContent>
                    <IonGrid>
                      <IonRow>
                        <IonCol size="8">
                          <div className="item-details">
                            <h3>{item.dataInGb}GB Plan</h3>
                            <p>{formatDuration(item.duration)} • {item.speed}</p>
                            <div className="item-features">
                              <IonChip outline color="primary">
                                {item.ip} IP
                              </IonChip>
                            </div>
                          </div>
                        </IonCol>
                        <IonCol size="4" className="item-price-col">
                          <div className="item-price">
                            <IonText color="primary">
                              <h2>{CartService.formatPrice(parseFloat(item.priceUsd))}</h2>
                            </IonText>
                            <div className="quantity-controls">
                              <IonButton
                                fill="clear"
                                size="small"
                                onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                disabled={item.quantity <= 1}
                              >
                                <IonIcon icon={remove} />
                              </IonButton>
                              <span className="quantity">{item.quantity}</span>
                              <IonButton
                                fill="clear"
                                size="small"
                                onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                disabled={item.quantity >= 10}
                              >
                                <IonIcon icon={add} />
                              </IonButton>
                            </div>
                          </div>
                        </IonCol>
                      </IonRow>
                    </IonGrid>
                  </IonCardContent>
                </IonCard>
              ))}
            </IonList>

            <IonCard className="cart-summary">
              <IonCardHeader>
                <IonCardTitle>Order Summary</IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                <div className="summary-row">
                  <span>Subtotal ({summary.itemCount} items)</span>
                  <span>{CartService.formatPrice(summary.subtotal)}</span>
                </div>
                {summary.savings > 0 && (
                  <div className="summary-row savings">
                    <span>Bulk Discount (3+ items)</span>
                    <span>-{CartService.formatPrice(summary.savings)}</span>
                  </div>
                )}
                {summary.tax > 0 && (
                  <div className="summary-row">
                    <span>Tax</span>
                    <span>{CartService.formatPrice(summary.tax)}</span>
                  </div>
                )}
                <div className="summary-row total">
                  <span>Total</span>
                  <span>{CartService.formatPrice(summary.total)}</span>
                </div>
              </IonCardContent>
            </IonCard>

            <div className="cart-actions">
              <IonButton
                fill="clear"
                color="medium"
                onClick={clearCart}
                className="clear-cart-btn"
              >
                Clear Cart
              </IonButton>
              <IonButton
                fill="solid"
                onClick={handleCheckout}
                className="checkout-btn"
                disabled={state.items.length === 0}
              >
                <IonIcon icon={cardOutline} slot="start" />
                Proceed to Checkout
              </IonButton>
            </div>
          </>
        )}
      </IonContent>
    </IonModal>
  );
};

export default Cart;
