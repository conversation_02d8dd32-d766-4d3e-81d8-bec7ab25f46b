import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions';

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

export interface PurchaseValidationResult {
  isValid: boolean;
  transactionId?: string;
  productId?: string;
  purchaseDate?: Date;
  expirationDate?: Date;
  error?: string;
  platform: 'ios' | 'android';
}

export interface OrderRecord {
  orderId: string;
  userId: string;
  planId: string;
  countryCode: string;
  transactionId: string;
  platform: 'ios' | 'android';
  purchaseDate: Date;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  receiptData?: string;
  validationResult?: any;
  createdAt: Date;
  updatedAt: Date;
}

export class InAppPurchaseService {
  private db: admin.firestore.Firestore;

  constructor() {
    this.db = admin.firestore();
  }

  // Simplified validation - cordova-plugin-purchase handles validation internally
  // This method is mainly for logging and creating order records
  async validatePurchase(
    transactionId: string,
    productId: string,
    platform: 'ios' | 'android',
    purchaseDate?: Date
  ): Promise<PurchaseValidationResult> {
    try {
      // Since cordova-plugin-purchase handles validation internally,
      // we just need to log and return success
      console.log(`Purchase validated: ${transactionId} for product ${productId} on ${platform}`);

      return {
        isValid: true,
        transactionId,
        productId,
        purchaseDate: purchaseDate || new Date(),
        platform,
      };
    } catch (error: any) {
      console.error('Purchase validation error:', error);
      return {
        isValid: false,
        error: error.message || 'Purchase validation failed',
        platform,
      };
    }
  }

  async createOrderRecord(orderData: Partial<OrderRecord>): Promise<string> {
    try {
      const orderId = `order_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
      
      const orderRecord: OrderRecord = {
        orderId,
        userId: orderData.userId!,
        planId: orderData.planId!,
        countryCode: orderData.countryCode!,
        transactionId: orderData.transactionId!,
        platform: orderData.platform!,
        purchaseDate: orderData.purchaseDate || new Date(),
        status: 'pending',
        receiptData: orderData.receiptData,
        validationResult: orderData.validationResult,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await this.db.collection('orders').doc(orderId).set(orderRecord);
      return orderId;
    } catch (error) {
      console.error('Failed to create order record:', error);
      throw new Error('Failed to create order record');
    }
  }

  async updateOrderStatus(orderId: string, status: OrderRecord['status'], additionalData?: any): Promise<void> {
    try {
      const updateData: any = {
        status,
        updatedAt: new Date(),
      };

      if (additionalData) {
        Object.assign(updateData, additionalData);
      }

      await this.db.collection('orders').doc(orderId).update(updateData);
    } catch (error) {
      console.error('Failed to update order status:', error);
      throw new Error('Failed to update order status');
    }
  }

  async getOrdersByUser(userId: string): Promise<OrderRecord[]> {
    try {
      const snapshot = await this.db
        .collection('orders')
        .where('userId', '==', userId)
        .orderBy('createdAt', 'desc')
        .get();

      return snapshot.docs.map(doc => doc.data() as OrderRecord);
    } catch (error) {
      console.error('Failed to get user orders:', error);
      return [];
    }
  }

  async getOrderById(orderId: string): Promise<OrderRecord | null> {
    try {
      const doc = await this.db.collection('orders').doc(orderId).get();
      return doc.exists ? (doc.data() as OrderRecord) : null;
    } catch (error) {
      console.error('Failed to get order:', error);
      return null;
    }
  }

  async processInAppPurchase(
    userId: string,
    planId: string,
    countryCode: string,
    platform: 'ios' | 'android',
    transactionId: string,
    productId: string,
    purchaseDate?: Date
  ): Promise<{ success: boolean; orderId?: string; error?: string }> {
    try {
      // Validate the purchase (simplified since cordova-plugin-purchase handles validation)
      const validationResult = await this.validatePurchase(
        transactionId,
        productId,
        platform,
        purchaseDate
      );

      if (!validationResult.isValid) {
        return {
          success: false,
          error: validationResult.error || 'Purchase validation failed',
        };
      }

      // Check if transaction already exists
      const existingOrder = await this.db
        .collection('orders')
        .where('transactionId', '==', transactionId)
        .get();

      if (!existingOrder.empty) {
        return {
          success: false,
          error: 'Transaction already processed',
        };
      }

      // Create order record
      const orderId = await this.createOrderRecord({
        userId,
        planId,
        countryCode,
        transactionId,
        platform,
        purchaseDate: validationResult.purchaseDate,
        validationResult,
      });

      // Update order status to completed
      await this.updateOrderStatus(orderId, 'completed');

      return {
        success: true,
        orderId,
      };
    } catch (error: any) {
      console.error('Failed to process in-app purchase:', error);
      return {
        success: false,
        error: error.message || 'Failed to process purchase',
      };
    }
  }

  async handleRefund(transactionId: string): Promise<boolean> {
    try {
      const snapshot = await this.db
        .collection('orders')
        .where('transactionId', '==', transactionId)
        .get();

      if (snapshot.empty) {
        console.warn(`No order found for transaction: ${transactionId}`);
        return false;
      }

      const orderDoc = snapshot.docs[0];
      await this.updateOrderStatus(orderDoc.id, 'refunded');

      return true;
    } catch (error) {
      console.error('Failed to handle refund:', error);
      return false;
    }
  }
}

// Firebase Functions exports
let inAppPurchaseService: InAppPurchaseService;

function getInAppPurchaseService(): InAppPurchaseService {
  if (!inAppPurchaseService) {
    inAppPurchaseService = new InAppPurchaseService();
  }
  return inAppPurchaseService;
}

export const processInAppPurchase = functions.https.onCall(async (data: any, context: any) => {
  try {
    const userId = (context && context.auth && context.auth.uid) || data?.userId;
    if (!userId) {
      throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }

    const { planId, countryCode, platform, transactionId, productId, purchaseDate } = data as {
      planId: string;
      countryCode: string;
      platform: 'ios' | 'android';
      transactionId: string;
      productId: string;
      purchaseDate?: string | Date;
    };

    const parsedPurchaseDate = purchaseDate ? new Date(purchaseDate) : undefined;

    const result = await getInAppPurchaseService().processInAppPurchase(
      userId,
      planId,
      countryCode,
      platform,
      transactionId,
      productId,
      parsedPurchaseDate
    );
    return result;
  } catch (error: any) {
    console.error('Error in processInAppPurchase (callable):', error);
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});

export const getUserOrders = functions.https.onCall(async (data: any, context: any) => {
  try {
    const userId = (context && context.auth && context.auth.uid) || data?.userId;
    if (!userId) {
      throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }
    const result = await getInAppPurchaseService().getOrdersByUser(userId);
    return result;
  } catch (error: any) {
    console.error('Error in getUserOrders (callable):', error);
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});

export const getOrderById = functions.https.onCall(async (data: any, context: any) => {
  try {
    const userId = (context && context.auth && context.auth.uid) || data?.userId;
    if (!userId) {
      throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }
    const { orderId } = data as { orderId?: string };
    if (!orderId) {
      throw new functions.https.HttpsError('invalid-argument', 'orderId is required');
    }
    const result = await getInAppPurchaseService().getOrderById(orderId);
    return result;
  } catch (error: any) {
    console.error('Error in getOrderById (callable):', error);
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});

export const handleRefund = functions.https.onCall(async (data: any, context: any) => {
  try {
    // Allow admin function calls or authenticated users
    const transactionId = data?.transactionId;
    if (!transactionId) {
      throw new functions.https.HttpsError('invalid-argument', 'transactionId is required');
    }
    const result = await getInAppPurchaseService().handleRefund(transactionId);
    return result;
  } catch (error: any) {
    console.error('Error in handleRefund (callable):', error);
    throw new functions.https.HttpsError('internal', error?.message || 'Internal server error');
  }
});
