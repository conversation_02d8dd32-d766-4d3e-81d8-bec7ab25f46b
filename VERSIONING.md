# Production Versioning Guide

## How to Release a New Version

When you are ready to release a new version (e.g., to Google Play or App Store), follow these steps:

### 1. Update Version

Use the standard npm command to bump the version. This will automatically sync the version to Android/iOS and your settings page.

```bash
# For a bug fix (e.g., 0.1.0 -> 0.1.1)
npm version patch

# For a new feature (e.g., 0.1.0 -> 0.2.0)
npm version minor

# For a major release (e.g., 0.1.0 -> 1.0.0)
npm version major
```

### 2. Verify Sync

Check that `android/app/build.gradle` has the new version:

- `versionName`: Matches your new version (e.g., "0.2.0")
- `versionCode`: Automatically calculated (e.g., 200)

### 3. Build & Deploy

Run your build command to bake the new version into the app:

```bash
npm run build
npx cap sync
```

### 4. Create Production Builds

- **Android**: Open Android Studio (`npx cap open android`) and Generate Signed Bundle.
- **Web**: Deploy the `dist/` folder.

> **Note**: You do not need to manually edit `package.json` or `build.gradle`. The `npm version` command handles it all via the `scripts/sync-version.cjs` script.
