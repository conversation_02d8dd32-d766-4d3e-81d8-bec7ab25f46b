"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getESimOrderDetails = exports.getUserESimTopups = exports.topupESimCredits = exports.redeemCreditsForESim = exports.getDataPlans = exports.getESimPricing = exports.giveTestCredits = exports.processCreditPurchase = exports.getUserESimOrders = exports.getMyESims = exports.getUserCreditTransactions = exports.getUserCredits = exports.getCreditPackages = void 0;
const admin = require("firebase-admin");
const functions = require("firebase-functions");
const cors = require("cors");
const firebase_functions_1 = require("firebase-functions");
const utils_1 = require("./utils");
// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}
const db = admin.firestore();
// CORS handler for all HTTP functions
const corsHandler = cors({ origin: true });
// Helper function to verify authentication
async function verifyAuth(req) {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new Error('Authentication required');
    }
    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    return decodedToken.uid;
}
// Helper function to handle HTTP responses with proper CORS
function handleHttpResponse(req, res, handler) {
    corsHandler(req, res, async () => {
        try {
            const result = await handler();
            res.status(200).json(result);
        }
        catch (error) {
            firebase_functions_1.logger.error('API Error:', error);
            res.status(500).json({ error: error.message || 'Internal server error' });
        }
    });
}
// =============================================================================
// CREDIT FUNCTIONS
// =============================================================================
exports.getCreditPackages = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        const snapshot = await db.collection('credit_packages')
            .where('active', '==', true)
            .get();
        // Sort in JavaScript instead of Firestore to avoid index requirement
        const packages = snapshot.docs.map(doc => (Object.assign({ id: doc.id }, doc.data())));
        return packages.sort((a, b) => (a.credits || 0) - (b.credits || 0));
    });
});
exports.getUserCredits = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        var _a;
        const userId = await verifyAuth(req);
        const userDoc = await db.collection('users').doc(userId).get();
        if (!userDoc.exists) {
            // Initialize user credits
            const initialCredits = {
                totalCredits: 0,
                lifetimeCredits: 0,
                lifetimeSpent: 0,
                pendingCredits: 0
            };
            await db.collection('users').doc(userId).set({ credits: initialCredits });
            return initialCredits;
        }
        return ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.credits) || {
            totalCredits: 0,
            lifetimeCredits: 0,
            lifetimeSpent: 0,
            pendingCredits: 0
        };
    });
});
exports.getUserCreditTransactions = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        const userId = await verifyAuth(req);
        const limit = parseInt(req.query.limit) || 10;
        const snapshot = await db.collection('users')
            .doc(userId)
            .collection('credit_transactions')
            .orderBy('createdAt', 'desc')
            .limit(limit)
            .get();
        return snapshot.docs.map(doc => {
            var _a;
            return (Object.assign(Object.assign({ id: doc.id }, doc.data()), { createdAt: (_a = doc.data().createdAt) === null || _a === void 0 ? void 0 : _a.toDate() }));
        });
    });
});
// =============================================================================
// ESIM FUNCTIONS
// =============================================================================
exports.getMyESims = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        const userId = await verifyAuth(req);
        try {
            // Simplified query to avoid index issues
            const snapshot = await db.collection('esimPurchases')
                .where('userId', '==', userId)
                .get();
            // Filter out deleted items in JavaScript instead of Firestore
            const esims = snapshot.docs
                .map(doc => doc.data())
                .filter(esim => esim.status !== 'deleted');
            firebase_functions_1.logger.info(`Found ${esims.length} eSIMs for user ${userId}`);
            return esims;
        }
        catch (error) {
            firebase_functions_1.logger.error(`Error fetching eSIMs for user ${userId}:`, error);
            // Return empty array instead of throwing error
            return [];
        }
    });
});
exports.getUserESimOrders = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        const userId = await verifyAuth(req);
        const snapshot = await db.collection('users')
            .doc(userId)
            .collection('esim_orders')
            .orderBy('purchasedAt', 'desc')
            .get();
        return {
            success: true,
            orders: snapshot.docs.map(doc => {
                var _a, _b, _c, _d;
                return (Object.assign(Object.assign({}, doc.data()), { purchasedAt: (_a = doc.data().purchasedAt) === null || _a === void 0 ? void 0 : _a.toDate(), activatedAt: (_b = doc.data().activatedAt) === null || _b === void 0 ? void 0 : _b.toDate(), expiresAt: (_c = doc.data().expiresAt) === null || _c === void 0 ? void 0 : _c.toDate(), lastUsageUpdate: (_d = doc.data().lastUsageUpdate) === null || _d === void 0 ? void 0 : _d.toDate() }));
            })
        };
    });
});
// =============================================================================
// STRIPE FUNCTIONS - REMOVED (using real ones from stripeService.ts)
// =============================================================================
// Legacy function for backward compatibility - credits are handled by Stripe webhook
exports.processCreditPurchase = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        const userId = await verifyAuth(req);
        const { packageId } = req.body;
        firebase_functions_1.logger.info(`Legacy processCreditPurchase called for user ${userId}, package ${packageId}`);
        // Return success - actual credit processing is handled by Stripe webhook
        return {
            success: true,
            message: 'Credits will be processed automatically via Stripe webhook'
        };
    });
});
// Temporary function to give users initial credits for testing
exports.giveTestCredits = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        var _a;
        const userId = await verifyAuth(req);
        const { credits = 50 } = req.body; // Default 50 credits for testing
        const userRef = db.collection('users').doc(userId);
        const userDoc = await userRef.get();
        let currentCredits = {
            totalCredits: 0,
            lifetimeCredits: 0,
            lifetimeSpent: 0,
            pendingCredits: 0
        };
        if (userDoc.exists) {
            currentCredits = ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.credits) || currentCredits;
        }
        const updatedCredits = {
            totalCredits: currentCredits.totalCredits + credits,
            lifetimeCredits: currentCredits.lifetimeCredits + credits,
            lifetimeSpent: currentCredits.lifetimeSpent,
            pendingCredits: currentCredits.pendingCredits
        };
        await userRef.set({ credits: updatedCredits }, { merge: true });
        firebase_functions_1.logger.info(`Gave ${credits} test credits to user ${userId}. New total: ${updatedCredits.totalCredits}`);
        return {
            success: true,
            message: `Added ${credits} test credits`,
            newTotal: updatedCredits.totalCredits
        };
    });
});
// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================
exports.getESimPricing = functions.https.onRequest((req, res) => {
    corsHandler(req, res, async () => {
        var _a;
        try {
            // Temporarily hardcode API key for testing
            const apiKey = process.env.SMSPOOL_API_KEY || 'RfnSUAU3rBKeJirPysbWkGffmEkC7jh4';
            firebase_functions_1.logger.info('SMSPOOL_API_KEY available:', !!apiKey);
            if (!apiKey) {
                firebase_functions_1.logger.error('SMSPOOL_API_KEY is missing from environment variables');
                res.status(500).json({ error: 'SMSPOOL_API_KEY is required' });
                return;
            }
            const params = req.method === 'POST' ? req.body : req.query;
            const formData = new FormData();
            formData.append('key', apiKey);
            formData.append('start', (params === null || params === void 0 ? void 0 : params.start) || '');
            formData.append('length', (params === null || params === void 0 ? void 0 : params.length) || '20');
            formData.append('Search', (params === null || params === void 0 ? void 0 : params.search) || '');
            firebase_functions_1.logger.info('Making request to SMSPOOL API...');
            const response = await fetch('https://api.smspool.net/esim/pricing', {
                method: 'POST',
                body: formData,
            });
            firebase_functions_1.logger.info('SMSPOOL API response status:', response.status);
            if (!response.ok) {
                const errorText = await response.text();
                firebase_functions_1.logger.error('SMSPOOL API error:', errorText);
                throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
            }
            const data = await response.json();
            firebase_functions_1.logger.info('SMSPOOL API response data length:', ((_a = data === null || data === void 0 ? void 0 : data.data) === null || _a === void 0 ? void 0 : _a.length) || 0);
            // Apply profit margins to pricing
            if (data.data && Array.isArray(data.data)) {
                data.data = data.data.map((plan) => {
                    firebase_functions_1.logger.info('Original price:', plan.price);
                    const calculatedPrice = utils_1.PricingUtils.calculateESimPrice(plan.price);
                    firebase_functions_1.logger.info('Calculated price:', calculatedPrice);
                    return Object.assign(Object.assign({}, plan), { originalPrice: plan.price, priceUsd: calculatedPrice.toString() });
                });
            }
            res.status(200).json(data);
        }
        catch (error) {
            firebase_functions_1.logger.error('Error getting eSIM pricing:', error);
            res.status(500).json({ error: error.message || 'Internal server error' });
        }
    });
});
exports.getDataPlans = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        var _a, _b;
        const planId = req.query.plan || ((_a = req.body) === null || _a === void 0 ? void 0 : _a.plan) || ((_b = req.body) === null || _b === void 0 ? void 0 : _b.planId);
        if (!planId) {
            throw new Error('Plan ID is required');
        }
        const formData = new FormData();
        formData.append('plan', planId);
        const response = await fetch('https://api.smspool.net/esim/topup_plans', {
            method: 'POST',
            body: formData,
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        // Apply profit margins to data plans
        if (Array.isArray(data)) {
            return data.map((plan) => (Object.assign(Object.assign({}, plan), { originalPrice: plan.price, priceUsd: utils_1.PricingUtils.calculateDataPlanPrice(plan.price).toString() })));
        }
        return data;
    });
});
// =============================================================================
// ESIM REDEMPTION FUNCTIONS
// =============================================================================
exports.redeemCreditsForESim = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        const userId = await verifyAuth(req);
        const { planId, credits } = req.body;
        if (!planId || !credits) {
            throw new Error('Plan ID and credits are required');
        }
        // TODO: Implement eSIM redemption logic
        // This should:
        // 1. Check user has enough credits
        // 2. Deduct credits from user account
        // 3. Purchase eSIM from SMSPOOL
        // 4. Store eSIM details in user's account
        firebase_functions_1.logger.info(`User ${userId} attempting to redeem ${credits} credits for plan ${planId}`);
        return { success: true, message: 'eSIM redemption not yet implemented' };
    });
});
exports.topupESimCredits = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        const userId = await verifyAuth(req);
        const { eSimId, credits } = req.body;
        if (!eSimId || !credits) {
            throw new Error('eSIM ID and credits are required');
        }
        // TODO: Implement eSIM topup logic
        firebase_functions_1.logger.info(`User ${userId} attempting to topup eSIM ${eSimId} with ${credits} credits`);
        return { success: true, message: 'eSIM topup not yet implemented' };
    });
});
exports.getUserESimTopups = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        const userId = await verifyAuth(req);
        // TODO: Get user's eSIM topups from database
        firebase_functions_1.logger.info(`Getting eSIM topups for user ${userId}`);
        return [];
    });
});
exports.getESimOrderDetails = functions.https.onRequest((req, res) => {
    handleHttpResponse(req, res, async () => {
        const userId = await verifyAuth(req);
        const { orderId } = req.body;
        if (!orderId) {
            throw new Error('Order ID is required');
        }
        // TODO: Get eSIM order details from database
        firebase_functions_1.logger.info(`Getting order details for user ${userId}, order ${orderId}`);
        return { success: true, message: 'Order details not yet implemented' };
    });
});
//# sourceMappingURL=api.js.map