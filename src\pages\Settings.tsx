import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonPage,
  IonList,
  IonItem,
  IonLabel,
  IonToggle,
  IonSelect,
  IonSelectOption,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonNote,
  IonAlert
} from '@ionic/react';
import { App } from '@capacitor/app';
import {
  notifications,
  earth,
  star,
  mail,
  chatbubbles,
  globe,
  informationCircle,
  book,
  document,
  shield
} from 'ionicons/icons';
import { notificationService } from '../services/notificationService';
import { pushNotificationService } from '../services/pushNotificationService';
import { useTranslation } from 'react-i18next';
import { AVAILABLE_LANGUAGES } from '../i18n';
import AppBar from '../components/AppBar';

const Settings: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [showPermissionAlert, setShowPermissionAlert] = useState(false);
  const [appVersion, setAppVersion] = useState(() => {
    try {
        // @ts-ignore
        if (typeof __APP_VERSION__ !== 'undefined') {
            // @ts-ignore
            return __APP_VERSION__;
        }
    } catch (e) {
        console.warn('__APP_VERSION__ not available');
    }
    return '0.1.0'; // Default fallback
  });

  useEffect(() => {
    const loadVersion = async () => {
        try {
            // Check for native version, but don't overwrite if it fails/is empty on web
            const info = await App.getInfo();
            if (info.version && info.version.trim() !== '') {
                setAppVersion(info.version);
            }
        } catch (e) {
            console.warn("Native App.getInfo failed, keeping fallback:", e);
        }
    };
    loadVersion();
  }, []);

  useEffect(() => {
    const checkStatus = async () => {
        const savedNotifications = localStorage.getItem('notifications');
        const permStatus = await pushNotificationService.checkPermissions();
        
        // If permission is already granted, we can trust the saved setting or default to true
        // If permission is denied/prompt, we should reflect that (toggle off)
        
        if (permStatus.receive === 'granted') {
             if (savedNotifications !== null) {
                setNotificationsEnabled(JSON.parse(savedNotifications));
             } else {
                 setNotificationsEnabled(true);
             }
             // Make sure we are initialized if we have permission and it's enabled
             if (savedNotifications === null || JSON.parse(savedNotifications)) {
                 pushNotificationService.initialize();
             }
        } else {
            // Not granted yet, or denied without prompt
            setNotificationsEnabled(false);
        }
    };
    checkStatus();
  }, []);

  const handleNotificationToggle = async (enabled: boolean) => {
    if (enabled) {
        // User wants to enable. Check/Request permission.
        const status = await pushNotificationService.requestPermissions();
        if (status.receive === 'granted') {
            setNotificationsEnabled(true);
            localStorage.setItem('notifications', 'true');
            await notificationService.updateNotificationPreferences(true);
            await pushNotificationService.initialize();
        } else {
            // Permission denied or dismissed
            setNotificationsEnabled(false); // Keep it off
            setShowPermissionAlert(true);
        }
    } else {
        // User disabling
        setNotificationsEnabled(false);
        localStorage.setItem('notifications', 'false');
        try {
            await notificationService.updateNotificationPreferences(false);
        } catch (error) {
             console.error('Failed to update notification preferences:', error);
        }
    }
  };

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
  };

  const handleRateApp = () => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const url = isIOS
      ? import.meta.env.REACT_APP_APPSTORE_URL || 'https://apps.apple.com/app/id123456789'
      : import.meta.env.REACT_APP_PLAYSTORE_URL || 'https://play.google.com/store/apps/details?id=com.esimnumero.app';
    window.open(url, '_blank');
  };

  const handleContactSupport = () => {
    const email = import.meta.env.REACT_APP_SUPPORT_EMAIL || '<EMAIL>';
    window.open(`mailto:${email}`, '_blank');
  };

  const handleTelegramSupport = () => {
    const telegramUrl = import.meta.env.REACT_APP_TELEGRAM_URL || 'https://t.me/esimnumero_support';
    window.open(telegramUrl, '_blank');
  };

  const handleSupportWebsite = () => {
    const websiteUrl = import.meta.env.REACT_APP_SUPPORT_WEBSITE || 'https://esimnumero.com/support';
    window.open(websiteUrl, '_blank');
  };

  return (
    <IonPage>
      <AppBar title={t('settings.title')} />
      
      <IonAlert
        isOpen={showPermissionAlert}
        onDidDismiss={() => setShowPermissionAlert(false)}
        header={t('settings.notifications.permissionRequired')}
        message={t('settings.notifications.enableInSettings')}
        buttons={['OK']}
      />

      <IonContent className="ion-padding">
        {/* Notifications Settings */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>{t('settings.notifications.title')}</IonCardTitle>
          </IonCardHeader>

          <IonCardContent>
            <IonList>
              <IonItem>
                <IonIcon icon={notifications} slot="start" />
                <IonLabel>
                  <h2>{t('settings.notifications.push')}</h2>
                  <p>{t('settings.notifications.pushDescription')}</p>
                </IonLabel>
                <IonToggle
                  checked={notificationsEnabled}
                  onIonChange={(e) => handleNotificationToggle(e.detail.checked)}
                />
              </IonItem>
            </IonList>
          </IonCardContent>
        </IonCard>

        {/* Language Settings */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>{t('settings.language.title')}</IonCardTitle>
          </IonCardHeader>

          <IonCardContent>
            <IonList>
              <IonItem>
                <IonIcon icon={earth} slot="start" />
                <IonLabel>{t('settings.language.language')}</IonLabel>
                <IonSelect
                  value={i18n.language}
                  onIonChange={(e) => handleLanguageChange(e.detail.value)}
                >
                  {AVAILABLE_LANGUAGES.map((lang) => (
                    <IonSelectOption key={lang.code} value={lang.code}>
                      {lang.flag} {lang.nativeName}
                    </IonSelectOption>
                  ))}
                </IonSelect>
              </IonItem>
            </IonList>
          </IonCardContent>
        </IonCard>

        {/* Support & Help */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>{t('settings.support.title')}</IonCardTitle>
          </IonCardHeader>

          <IonCardContent>
            <IonList>
              <IonItem button onClick={handleContactSupport}>
                <IonIcon icon={mail} slot="start" />
                <IonLabel>
                  <h2>{t('settings.support.contactSupport')}</h2>
                  <p>{t('settings.support.contactSupportDescription')}</p>
                </IonLabel>
              </IonItem>

              <IonItem button onClick={handleTelegramSupport}>
                <IonIcon icon={chatbubbles} slot="start" />
                <IonLabel>
                  <h2>{t('settings.support.telegram')}</h2>
                  <p>{t('settings.support.telegramDescription')}</p>
                </IonLabel>
              </IonItem>

              <IonItem button onClick={handleSupportWebsite}>
                <IonIcon icon={globe} slot="start" />
                <IonLabel>
                  <h2>{t('settings.support.website')}</h2>
                  <p>{t('settings.support.websiteDescription')}</p>
                </IonLabel>
              </IonItem>

              <IonItem button routerLink="/esim-tutorial">
                <IonIcon icon={book} slot="start" />
                <IonLabel>
                  <h2>{t('settings.support.howToUse')}</h2>
                  <p>{t('settings.support.howToUseDescription')}</p>
                </IonLabel>
              </IonItem>
            </IonList>
          </IonCardContent>
        </IonCard>

        {/* Legal */}
        <IonCard>
            <IonCardHeader>
                <IonCardTitle>{t('settings.legal.title') || 'Legal'}</IonCardTitle>
            </IonCardHeader>

            <IonCardContent>
                <IonList>
                    <IonItem button routerLink="/terms">
                        <IonIcon icon={document} slot="start" />
                        <IonLabel>
                            <h2>{t('nav.terms')}</h2>
                            <p>{t('settings.legal.termsDescription') || 'Read our terms of service'}</p>
                        </IonLabel>
                    </IonItem>

                    <IonItem button routerLink="/privacy">
                        <IonIcon icon={shield} slot="start" />
                        <IonLabel>
                            <h2>{t('nav.privacy')}</h2>
                            <p>{t('settings.legal.privacyDescription') || 'Read our privacy policy'}</p>
                        </IonLabel>
                    </IonItem>
                </IonList>
            </IonCardContent>
        </IonCard>

        {/* App Settings */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>{t('settings.app.title')}</IonCardTitle>
          </IonCardHeader>

          <IonCardContent>
            <IonList>
              <IonItem button onClick={handleRateApp}>
                <IonIcon icon={star} slot="start" />
                <IonLabel>
                  <h2>{t('settings.support.rateApp')}</h2>
                  <p>{t('settings.support.rateAppDescription')}</p>
                </IonLabel>
              </IonItem>

              <IonItem>
                <IonIcon icon={informationCircle} slot="start" />
                <IonLabel>
                  <h2>{t('settings.app.version')}</h2>
                </IonLabel>
                <IonNote slot="end">{appVersion}</IonNote>
              </IonItem>
            </IonList>
          </IonCardContent>
        </IonCard>
      </IonContent>
    </IonPage>
  );
};

export default Settings;
