import React, { useState } from 'react';
import {
  IonContent,
  IonPage,
  IonItem,
  IonLabel,
  IonInput,
  IonButton,
  IonText,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonSpinner,
  IonBackButton,
  IonButtons
} from '@ionic/react';
import { mail, arrowBack } from 'ionicons/icons';
import { sendPasswordResetEmail } from 'firebase/auth';
import { auth } from '../firebase';
import { Capacitor } from '@capacitor/core';
import { CapacitorAuthService } from '../services/capacitorAuthService';
import { useHistory } from 'react-router-dom';
import './Auth.css';

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const history = useHistory();

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    try {
      if (Capacitor.isNativePlatform()) {
        const result = await CapacitorAuthService.sendPasswordResetEmail(email);

        if (result.success) {
          setMessage('Check your email for password reset instructions');
        } else {
          setError(result.error || 'Failed to send password reset email');
        }
      } else {
        await sendPasswordResetEmail(auth, email);
        setMessage('Check your email for password reset instructions');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <IonPage className="auth-page">
      <IonContent fullscreen className="auth-content">
        <div className="auth-wrapper auth-fade-in">
          <div className="auth-sidebar">
            <div className="sidebar-content">
              <div className="sidebar-logo">KrakenSim</div>
              <h2 className="sidebar-tagline">
                Account <br />
                <span style={{ opacity: 0.7 }}>Recovery.</span>
              </h2>
              <p className="sidebar-description">
                Don't worry, it happens to the best of us. Let's get you back into your account as quickly as possible.
              </p>
            </div>
          </div>

          <div className="auth-container">
            <IonCard className="auth-card" style={{ width: '100%', maxWidth: '440px', boxShadow: 'none', background: 'transparent', border: 'none' }}>
              <IonCardHeader className="auth-header">
                <div style={{ textAlign: 'center' }}>
                  <IonCardTitle className="auth-title">
                    Reset Password
                  </IonCardTitle>
                  <p className="auth-subtitle">We'll send you a link to recover your account</p>
                </div>
              </IonCardHeader>

              <IonCardContent>
                <form onSubmit={handlePasswordReset} className="auth-form">
                  <IonItem className="auth-item" lines="none">
                    <IonIcon icon={mail} slot="start" />
                    <IonLabel position="stacked">Email</IonLabel>
                    <IonInput
                      type="email"
                      value={email}
                      onIonInput={(e) => setEmail(e.detail.value!)}
                      required
                      placeholder="<EMAIL>"
                    />
                  </IonItem>

                  {error && (
                    <div className="auth-error">
                      {error}
                    </div>
                  )}

                  {message && (
                    <div className="auth-success">
                      {message}
                    </div>
                  )}

                  <IonButton
                    expand="block"
                    type="submit"
                    disabled={loading}
                    className="auth-button-main"
                  >
                    {loading ? <IonSpinner name="crescent" /> : 'Send Reset Email'}
                  </IonButton>
                </form>

                <div className="auth-footer">
                  <p>
                    Remember your password?{' '}
                    <span
                      className="auth-link"
                      onClick={() => history.push('/login')}
                    >
                      Sign In
                    </span>
                  </p>
                </div>
              </IonCardContent>
            </IonCard>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default ForgotPassword;